// 导入模块
const { app, BrowserWindow, ipcMain, Tray, Menu, nativeImage, Notification, screen, dialog, shell, desktopCapturer, webContents, session } = require('electron')
const path = require('path')

var mainWindow, tray, timer
// const NODE_ENV = process.env.NODE_ENV
// 创建主窗口
const createWindow = () => {
  mainWindow = new BrowserWindow({
    title: '诺鑫办办',
    width: 1640,
    height: 1000,
    webPreferences: {
      preload: path.join(__dirname, 'preload.js'),
      nodeIntegration: true, //开启true这一步很重要,目的是为了vue文件中可以引入node和electron相关的API
      contextIsolation: true, // 可以使用require方法
      enableRemoteModule: true, // 可以使用remote方法
    }
  })


  tray = new Tray(path.join(__dirname, './buu.ico'))

  const contextMenu = Menu.buildFromTemplate([
    { label: '退出', click: () => { mainWindow.destroy() } }])


  tray.setToolTip('诺鑫办办')
  tray.setContextMenu(contextMenu)


  tray.on('click', () => {
    if (mainWindow.isMinimized()) {
      mainWindow.restore();
    }
    mainWindow.show();
    mainWindow.setSkipTaskbar(false);
    mainWindow.focus();
  })


  // 监听主窗口是否打开
  mainWindow.on('focus', () => {
    // 在这里执行你想要的操作
    console.log('Window is focused.');
    // if(timer){
    //   clearInterval(timer)
    // }
    // timer = null
    // msgFlag = false
    tray.setToolTip('诺鑫办办')
    tray.setImage(path.join(__dirname, './buu.ico'))// 防止 mainWindow.show() 后系统托盘图标的 空白现象；
  });


  // 消息闪烁逻辑
  let msgFlag = false
  ipcMain.on('have-message', () => {
    mainWindow.flashFrame(true) // 任务栏闪烁
    if (timer) clearInterval(timer)

    timer = setInterval(() => {
      msgFlag = !msgFlag
      msgFlag
        ? tray.setImage(nativeImage.createEmpty())
        : tray.setImage(path.join(__dirname, './buu.ico'))
      tray.setToolTip('您有收到新消息')
    }, 500)
  })




  // 取消消息提醒
  ipcMain.on('cancel-message', () => {
    mainWindow.flashFrame(false)
    if (timer) {
      clearInterval(timer)
      tray.setImage(path.join(__dirname, './buu.ico'))
      tray.setToolTip('诺鑫办办')
    }
  })

  // 窗口聚焦时停止闪烁
  mainWindow.on('focus', () => {
    mainWindow.flashFrame(false)
    if (timer) {
      clearInterval(timer)
      tray.setImage(path.join(__dirname, './buu.ico'))
      tray.setToolTip('诺鑫办办')
    }
  })

  // 在 createWindow 函数中添加 close 事件监听
  mainWindow.on('close', (event) => {
    // 阻止默认关闭行为
    event.preventDefault();
    // 隐藏窗口而不是关闭
    mainWindow.hide();
    // 保持任务栏图标（重要修改）
    mainWindow.setSkipTaskbar(false); // 改为 false
  });




  // mainWindow.webContents.openDevTools() //打开控制台



  // 和自己本地vue项目启动的地址保持一致
  mainWindow.loadURL("https://oa.nxbanban.com");
  // mainWindow.loadURL('https://test.dinggehuo.com/web/banban/admin/#/');

}



Menu.setApplicationMenu(null)



// 应用准备就绪，加载窗口
app.whenReady().then(async () => {

  // 清理默认会话的缓存
  await session.defaultSession.clearCache();

  // 确保应用的单实例
  const gotTheLock = app.requestSingleInstanceLock();
  if (!gotTheLock) {
    app.quit();
  } else {
    app.on('second-instance', (event, commandLine, workingDirectory) => {
      // 当启动第二个实例时，将焦点放到主窗口
      if (mainWindow) {
        // 统一处理窗口状态
        if (mainWindow.isMinimized()) {
          mainWindow.restore();
        }
        // 强制显示窗口（关键）
        mainWindow.show();
        // 确保窗口在屏幕最前端
        mainWindow.setAlwaysOnTop(true);
        mainWindow.focus();
        mainWindow.setAlwaysOnTop(false);
        // 更新任务栏状态
        mainWindow.setSkipTaskbar(false);
      }
    });
    createWindow()
  }



  // mac 上默认保留一个窗口
  app.on('activate', () => {
    if (BrowserWindow.getAllWindows().length === 0) createWindow()
  })
})

// 关闭所有窗口 ： 程序退出 ： windows & linux
app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') app.quit()
})