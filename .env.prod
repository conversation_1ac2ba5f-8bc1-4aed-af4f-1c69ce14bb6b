# 标题
VITE_APP_TITLE=诺鑫办办

# 生产环境：只在打包时使用
VITE_NODE_ENV=production

VITE_DEV=false

# 请求路径
VITE_BASE_URL=''

# 文件上传类型：server - 后端上传， client - 前端直连上传，仅支持S3服务
VITE_UPLOAD_TYPE=server
# 上传路径
VITE_UPLOAD_URL='https://api.nxbanban.com/banban/admin-api/infra/file/upload'

# 接口前缀
VITE_API_BASEPATH=

# 接口地址
VITE_API_URL='https://api.nxbanban.com/banban/admin-api'

# 是否删除debugger
VITE_DROP_DEBUGGER=true

# 是否删除console.log
VITE_DROP_CONSOLE=false

# 是否sourcemap
VITE_SOURCEMAP=false

# 打包路径
VITE_BASE_PATH=/

# 输出路径
VITE_OUT_DIR=dist-prod

# 商城H5会员端域名
VITE_MALL_H5_DOMAIN='http://mall.nxbanban.com'

# 验证码的开关
VITE_APP_CAPTCHA_ENABLE=false
