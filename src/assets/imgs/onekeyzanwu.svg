<?xml version="1.0" encoding="UTF-8"?>
<svg width="160px" height="160px" viewBox="0 0 160 160" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>切片</title>
    <defs>
        <linearGradient x1="50%" y1="0.33326049%" x2="50%" y2="100%" id="linearGradient-1">
            <stop stop-color="#3370FF" stop-opacity="0.6203417" offset="0%"></stop>
            <stop stop-color="#FFFFFF" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="-0.923595324%" x2="50.5384156%" y2="76.0258076%" id="linearGradient-2">
            <stop stop-color="#F8F8F8" stop-opacity="0.71" offset="0%"></stop>
            <stop stop-color="#E2E3F2" stop-opacity="0" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="63.86449%" id="linearGradient-3">
            <stop stop-color="#3370FF" stop-opacity="0.6203417" offset="0%"></stop>
            <stop stop-color="#FFFFFF" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="67.5786273%" y1="46.4838615%" x2="-0.46250816%" y2="60.5%" id="linearGradient-4">
            <stop stop-color="#E3E5EE" offset="0%"></stop>
            <stop stop-color="#E1E2ED" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="97.60614%" y1="47.1%" x2="-7.032155%" y2="45.400003%" id="linearGradient-5">
            <stop stop-color="#EAECF2" offset="0%"></stop>
            <stop stop-color="#E1E3ED" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="73.1%" y1="95.3%" x2="73.1%" y2="21.700001%" id="linearGradient-6">
            <stop stop-color="#F8F8F8" offset="0%"></stop>
            <stop stop-color="#E2E7F2" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="30.5%" y1="95.897436%" x2="32.5%" y2="12.609865%" id="linearGradient-7">
            <stop stop-color="#F8F8F8" offset="0%"></stop>
            <stop stop-color="#E2E4F2" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="49.999923%" y1="0%" x2="49.999923%" y2="99.999845%" id="linearGradient-8">
            <stop stop-color="#F8F8F8" offset="0%"></stop>
            <stop stop-color="#E2E7F2" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="49.999923%" y1="0%" x2="49.999923%" y2="99.999845%" id="linearGradient-9">
            <stop stop-color="#F8F8F8" offset="0%"></stop>
            <stop stop-color="#E2E7F2" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="0.33326049%" x2="50%" y2="100%" id="linearGradient-10">
            <stop stop-color="#3370FF" stop-opacity="0.6203417" offset="0%"></stop>
            <stop stop-color="#FFFFFF" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="0%" y1="50%" x2="100%" y2="50%" id="linearGradient-11">
            <stop stop-color="#62A9FF" offset="0%"></stop>
            <stop stop-color="#3370FF" offset="100%"></stop>
        </linearGradient>
    </defs>
    <g id="办一下-pc" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="办一下-无未读" transform="translate(-578.000000, -230.000000)">
            <g id="暂无内容" transform="translate(578.000000, 230.000000)">
                <g id="编组" fill-rule="nonzero">
                    <polygon id="矩形_343" fill-opacity="0" fill="#FFFFFF" points="0 0 156.125 0 156.125 160 0 160"></polygon>
                    <g transform="translate(11.156250, 30.812500)">
                        <polygon id="联合_69" fill-opacity="0.2" fill="url(#linearGradient-1)" points="85.890625 54.75 84.421875 54.640625 83.015625 54.40625 81.671875 54.03125 80.375 53.515625 79.125 52.875 77.96875 52.140625 76.875 51.296875 75.875 50.34375 74.953125 49.296875 74.15625 48.171875 73.453125 46.96875 72.84375 45.6875 72.375 44.34375 72.03125 42.953125 71.8125 41.515625 71.734375 40 71.8125 38.484375 72.03125 37.046875 72.375 35.65625 72.84375 34.3125 73.453125 33.03125 74.15625 31.828125 74.953125 30.703125 75.875 29.65625 76.875 28.703125 77.96875 27.859375 79.125 27.125 80.375 26.484375 81.671875 25.984375 83.015625 25.609375 84.421875 25.359375 85.890625 25.265625 87.703125 25.375 89.453125 25.734375 91.171875 26.3125 91.125 25.265625 91.203125 23.703125 91.453125 22.1875 91.859375 20.734375 92.4375 19.328125 93.15625 18.03125 94.03125 16.796875 95.046875 15.6875 96.171875 14.703125 97.4375 13.828125 98.8125 13.125 100.203125 12.59375 101.640625 12.25 103.125 12.078125 104.59375 12.078125 106.0625 12.25 107.5 12.59375 108.90625 13.125 110.265625 13.828125 111.53125 14.703125 112.671875 15.6875 113.671875 16.796875 114.546875 18.03125 115.265625 19.328125 115.84375 20.734375 116.25 22.1875 116.5 23.703125 116.578125 25.265625 116.46875 26.96875 116.15625 28.625 115.640625 30.25 116.65625 29.671875 117.703125 29.203125 118.78125 28.84375 119.90625 28.578125 121.03125 28.421875 122.1875 28.359375 123.9375 28.5 125.59375 28.859375 127.15625 29.4375 128.625 30.21875 129.984375 31.15625 131.203125 32.28125 132.28125 33.546875 133.1875 34.921875 133.921875 36.453125 134.46875 38.0625 134.796875 39.75 134.921875 41.546875 134.796875 43.34375 134.46875 45.046875 133.921875 46.65625 133.1875 48.171875 132.28125 49.5625 131.203125 50.828125 129.984375 51.953125 128.625 52.890625 127.15625 53.671875 125.59375 54.234375 123.9375 54.59375 122.1875 54.75"></polygon>
                        <polygon id="路径_63" fill="url(#linearGradient-2)" points="2.984375 90.921875 1.90625 85.390625 1.328125 81.8125 0.796875 77.84375 0.578125 75.796875 0.328125 71.703125 0.3125 69.78125 0.421875 67.34375 0.546875 66.25 0.71875 65.171875 0.953125 64.234375 1.21875 63.421875 1.484375 62.8125 1.78125 62.3125 2.109375 61.890625 2.453125 61.546875 2.96875 61.171875 3.921875 60.671875 4.375 60.53125 5.015625 60.4375 5.640625 60.4375 6.234375 60.53125 6.84375 60.703125 7.421875 60.953125 7.984375 61.265625 8.796875 61.828125 9.546875 62.46875 10.25 63.171875 11.4375 64.59375 12.78125 66.609375 12.15625 64.234375 11.578125 61.640625 10.96875 58.46875 10.484375 54.96875 10.3125 53.171875 10.1875 50.21875 10.296875 48.03125 10.4375 46.96875 10.625 46 10.859375 45.109375 11.109375 44.453125 11.375 43.875 12.03125 42.90625 12.421875 42.5 12.875 42.15625 13.375 41.875 13.921875 41.671875 14.5 41.546875 15.0625 41.515625 15.609375 41.578125 16.140625 41.71875 16.65625 41.921875 17.6875 42.546875 18.203125 42.96875 18.828125 43.59375 19.453125 44.328125 20.078125 45.171875 21.21875 46.984375 21.765625 48 22.515625 49.5625 23.890625 52.84375 24.46875 54.4375 25.46875 57.484375 26.1875 60 26.796875 62.359375 26.609375 60.03125 26.5625 57.609375 26.609375 56.25 26.84375 53.8125 27.21875 51.90625 27.5 50.984375 27.828125 50.109375 28.203125 49.28125 28.546875 48.703125 29.328125 47.6875 29.78125 47.265625 30.28125 46.890625 30.828125 46.578125 31.4375 46.34375 32.109375 46.15625 32.640625 46.109375 33.1875 46.15625 33.734375 46.3125 34.3125 46.578125 34.828125 46.90625 35.375 47.3125 35.921875 47.8125 36.5 48.4375 37.203125 49.28125 37.90625 50.265625 38.625 51.390625 39.984375 53.765625 41.59375 57.09375 43.359375 61.28125 44.875 65.3125 46.546875 70.3125 47.4375 73.265625"></polygon>
                        <polygon id="椭圆_16" fill-opacity="0.061778482" fill="url(#linearGradient-3)" points="148.84375 96.5 148.796875 97.34375 148.6875 98.171875 148.5 99.015625 148.21875 99.859375 147.859375 100.71875 147.40625 101.59375 146.8125 102.53125 146.109375 103.484375 145.28125 104.453125 144.3125 105.4375 143.171875 106.453125 142.078125 107.328125 140.859375 108.21875 139.5 109.109375 138 110.015625 136.34375 110.921875 134.328125 111.921875 132.125 112.921875 129.703125 113.90625 127.046875 114.890625 124.25 115.8125 121.265625 116.703125 118.109375 117.546875 114.9375 118.3125 111.640625 119.015625 108.171875 119.671875 104.71875 120.25 101.140625 120.765625 97.453125 121.21875 93.765625 121.609375 89.984375 121.921875 86.140625 122.171875 82.28125 122.359375 78.375 122.46875 74.421875 122.5 70.46875 122.46875 66.5625 122.359375 62.71875 122.171875 58.859375 121.921875 55.078125 121.609375 51.390625 121.21875 47.703125 120.765625 44.125 120.25 40.671875 119.671875 37.21875 119.015625 33.90625 118.3125 30.734375 117.546875 27.578125 116.703125 24.609375 115.8125 21.796875 114.890625 19.15625 113.90625 16.71875 112.921875 14.515625 111.921875 12.5 110.921875 10.84375 110.015625 9.34375 109.109375 7.984375 108.21875 6.765625 107.328125 5.671875 106.453125 4.546875 105.4375 3.5625 104.453125 2.734375 103.484375 2.03125 102.53125 1.453125 101.59375 0.984375 100.71875 0.625 99.859375 0.359375 99.015625 0.15625 98.171875 0.046875 97.34375 0 96.5 0.046875 95.65625 0.15625 94.828125 0.359375 93.984375 0.625 93.140625 0.984375 92.28125 1.453125 91.40625 2.03125 90.46875 2.734375 89.515625 3.5625 88.546875 4.546875 87.5625 5.671875 86.546875 6.765625 85.671875 7.984375 84.78125 9.34375 83.890625 10.84375 82.984375 12.5 82.078125 14.515625 81.078125 16.71875 80.078125 19.15625 79.09375 21.796875 78.109375 24.609375 77.1875 27.578125 76.296875 30.734375 75.453125 33.90625 74.6875 37.21875 73.984375 40.671875 73.328125 44.125 72.75 47.703125 72.234375 51.390625 71.78125 55.078125 71.390625 58.859375 71.078125 62.71875 70.828125 66.5625 70.640625 70.46875 70.53125 74.421875 70.5 78.375 70.53125 82.28125 70.640625 86.140625 70.828125 89.984375 71.078125 93.765625 71.390625 97.453125 71.78125 101.140625 72.234375 104.71875 72.75 108.171875 73.328125 111.640625 73.984375 114.9375 74.6875 118.109375 75.453125 121.265625 76.296875 124.25 77.1875 127.046875 78.109375 129.703125 79.09375 132.125 80.078125 134.328125 81.078125 136.34375 82.078125 138 82.984375 139.5 83.890625 140.859375 84.78125 142.078125 85.671875 143.171875 86.546875 144.3125 87.5625 145.28125 88.546875 146.109375 89.515625 146.8125 90.46875 147.40625 91.40625 147.859375 92.28125 148.21875 93.140625 148.5 93.984375 148.6875 94.828125 148.796875 95.65625"></polygon>
                        <g transform="translate(32.515625, 26.578125)">
                            <polygon id="路径_107" fill="#DBDDE6" points="35.5625 0 35.5625 20.15625 69.0625 19.234375 69.0625 4.09375"></polygon>
                            <polygon id="路径_108" fill="#D0D5E0" points="35.5625 0 35.5625 20.15625 8.671875 19.234375 8.671875 5.953125"></polygon>
                            <polygon id="路径_103" fill="url(#linearGradient-4)" points="39.4375 10.25 39.4375 54.09375 69.0625 48.140625 69.0625 4.21875"></polygon>
                            <polygon id="路径_104" fill="url(#linearGradient-5)" points="39.4375 54.09375 8.671875 48.59375 8.671875 5.828125 39.4375 10.15625"></polygon>
                            <polygon id="路径_105" fill="url(#linearGradient-6)" points="8.671875 5.828125 0 14.328125 30.234375 19.8125 39.578125 10.15625"></polygon>
                            <polygon id="路径_106" fill="url(#linearGradient-7)" points="48.265625 19.234375 77.953125 12.75 69.140625 4.25 39.296875 10.109375"></polygon>
                        </g>
                        <polygon id="路径_109" fill="url(#linearGradient-8)" points="8.53125 79.703125 9.9375 80.09375 10.609375 80.21875 12.21875 80.390625 13.09375 80.359375 14.25 80.1875 14.828125 80.03125 15.375 79.796875 15.890625 79.5 16.390625 79.140625 16.828125 78.703125 17.234375 78.171875 17.59375 77.53125 17.984375 76.453125 18.234375 75.046875 18.53125 74 18.84375 73.15625 19.15625 72.515625 19.53125 71.90625 19.890625 71.4375 20.21875 71.09375 20.9375 70.59375 21.703125 70.359375 22.3125 70.34375 21.625 70.328125 20.78125 70.5 19.984375 70.890625 19.609375 71.171875 19.203125 71.546875 18.796875 72.0625 18.4375 72.609375 18.09375 73.296875 17.765625 74.1875 16.140625 73.9375 14.796875 73.9375 13.953125 74.0625 13.1875 74.25 12.53125 74.515625 11.90625 74.84375 11.34375 75.21875 10.859375 75.609375 10.03125 76.515625 9.296875 77.6875 9 78.296875 8.625 79.296875"></polygon>
                        <polygon id="路径_110" fill="url(#linearGradient-9)" points="99.796875 92.125 101.125 92.359375 101.734375 92.40625 102.4375 92.390625 103.15625 92.3125 103.875 92.15625 104.328125 91.984375 104.78125 91.75 105.1875 91.46875 105.5625 91.125 105.875 90.703125 106.15625 90.1875 106.3125 89.765625 106.4375 89.296875 106.546875 88.109375 106.703125 87.34375 106.875 86.71875 107.296875 85.75 107.75 85.125 108.140625 84.796875 108.453125 84.609375 108.78125 84.484375 109.234375 84.4375 108.734375 84.46875 108.375 84.5625 108 84.71875 107.3125 85.21875 106.78125 85.90625 106.546875 86.328125 106.15625 87.515625 104.78125 87.453125 103.6875 87.59375 103.015625 87.796875 102.4375 88.0625 101.953125 88.375 101.5 88.75 100.828125 89.53125 100.4375 90.1875 100.171875 90.765625 99.875 91.734375"></polygon>
                        <polygon id="联合_70" fill-opacity="0.2" fill="url(#linearGradient-10)" points="16.375 12.34375 15.8125 12.296875 15.28125 12.1875 14.78125 12 14.3125 11.75 13.875 11.453125 13.484375 11.09375 13.140625 10.671875 12.84375 10.234375 12.609375 9.734375 12.4375 9.203125 12.328125 8.65625 12.28125 8.078125 12.328125 7.484375 12.4375 6.9375 12.609375 6.40625 12.84375 5.921875 13.140625 5.46875 13.484375 5.0625 13.875 4.703125 14.3125 4.390625 14.78125 4.140625 15.28125 3.953125 15.8125 3.84375 16.375 3.8125 16.890625 3.84375 17.390625 3.9375 17.875 4.109375 17.890625 3.359375 18.078125 2.5 18.234375 2.09375 18.6875 1.359375 18.984375 1.046875 19.671875 0.5 20.46875 0.15625 21.3125 0 22.15625 0.046875 22.578125 0.15625 23.359375 0.5 24.0625 1.046875 24.59375 1.71875 24.96875 2.5 25.15625 3.359375 25.140625 4.296875 24.90625 5.25 25.34375 5.015625 25.796875 4.84375 26.28125 4.734375 26.78125 4.703125 27.28125 4.75 27.765625 4.859375 28.203125 5.03125 29.015625 5.53125 29.671875 6.21875 30.140625 7.046875 30.296875 7.515625 30.390625 8 30.4375 8.515625 30.390625 9.03125 30.296875 9.53125 29.9375 10.421875 29.359375 11.1875 28.625 11.78125 27.765625 12.1875 27.28125 12.296875 26.78125 12.34375"></polygon>
                    </g>
                </g>
                <g id="编组" transform="translate(62.593750, 26.062500)">
                    <polygon id="路径_72备份" fill="url(#linearGradient-11)" points="39.703125 1 38.046875 0.46875 37.25 0.28125 36.3125 0.125 35.328125 0.015625 34.296875 0 32.90625 0.171875 32.21875 0.34375 31.546875 0.578125 30.921875 0.90625 30.3125 1.328125 29.90625 1.6875 29.515625 2.109375 29.15625 2.609375 28.8125 3.1875 28.546875 3.78125 28.3125 4.453125 28.109375 5.234375 27.96875 6.125 27.640625 7.15625 27.3125 8.015625 26.984375 8.703125 26.234375 9.90625 25.484375 10.703125 24.78125 11.1875 24.25 11.421875 23.828125 11.53125 23.421875 11.578125 22.90625 11.546875 23.484375 11.609375 24.40625 11.515625 25.359375 11.1875 26.203125 10.65625 27 9.875 27.40625 9.328125 27.765625 8.75 28.125 8.046875 28.484375 7.171875 29.484375 7.40625 30.40625 7.53125 31.234375 7.59375 32 7.59375 32.78125 7.515625 33.484375 7.390625 34.71875 7.015625 35.484375 6.640625 36.171875 6.21875 36.75 5.765625 37.3125 5.234375 38.1875 4.1875 38.703125 3.375 39.09375 2.640625 39.390625 1.953125"></polygon>
                    <path d="M0,29.03125 L1.0625,29.328125 L2.0625,29.546875 L3,29.6875 L3.90625,29.765625 L5.6875,29.734375 L6.484375,29.640625 L7.96875,29.265625 L9.21875,28.71875 L9.765625,28.390625 L10.265625,28.015625 L10.703125,27.609375 L11.0625,27.1875 L11.359375,26.734375 L11.578125,26.25 L11.734375,25.75 L11.8125,25.25 L11.8125,24.71875 L11.734375,24.203125 L11.5625,23.671875 L11.3125,23.125 L10.953125,22.5625 L10.5625,22.125 L10.21875,21.859375 L9.90625,21.703125 L9.578125,21.65625 L9.265625,21.6875 L8.984375,21.796875 L8.71875,22 L8.484375,22.25 L8.28125,22.5625 L8.015625,23.296875 L7.9375,24.140625 L8,24.546875 L8.109375,24.96875 L8.296875,25.34375 L8.5625,25.703125 L8.90625,26.03125 L9.203125,26.21875 L9.96875,26.53125 L10.46875,26.625 L11.5,26.671875 L12.140625,26.609375 L12.90625,26.484375 L13.859375,26.234375 L15,25.859375 L16.359375,25.296875 L19.9375,22.734375 L21.03125,21.84375 L22.40625,20.546875 L23.078125,19.78125 L23.703125,19 L24.328125,18.078125 L24.953125,17.046875 L25.53125,15.984375 L26.109375,14.734375 L26.71875,13.296875" id="路径-2" stroke="#BCC2C9" stroke-linecap="round" stroke-dasharray="2"></path>
                </g>
            </g>
        </g>
    </g>
</svg>