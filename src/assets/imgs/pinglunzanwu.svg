<?xml version="1.0" encoding="UTF-8"?>
<svg width="160px" height="160px" viewBox="0 0 160 160" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>切片</title>
    <defs>
        <linearGradient x1="50%" y1="0.33326049%" x2="50%" y2="100%" id="linearGradient-1">
            <stop stop-color="#3370FF" stop-opacity="0.6203417" offset="0%"></stop>
            <stop stop-color="#FFFFFF" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="-0.923595324%" x2="50.5425236%" y2="76.0258076%" id="linearGradient-2">
            <stop stop-color="#F8F8F8" stop-opacity="0.71" offset="0%"></stop>
            <stop stop-color="#E2E3F2" stop-opacity="0" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="124.24271%" id="linearGradient-3">
            <stop stop-color="#F8F8F8" offset="0%"></stop>
            <stop stop-color="#D6D8ED" stop-opacity="0.071" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="63.86449%" id="linearGradient-4">
            <stop stop-color="#3370FF" stop-opacity="0.6203417" offset="0%"></stop>
            <stop stop-color="#FFFFFF" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-5">
            <stop stop-color="#F5F5F8" offset="0%"></stop>
            <stop stop-color="#E2E4F2" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-6">
            <stop stop-color="#3370FF" offset="0%"></stop>
            <stop stop-color="#62A9FF" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-7">
            <stop stop-color="#3370FF" offset="0%"></stop>
            <stop stop-color="#62A9FF" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="0.33326049%" x2="50%" y2="100%" id="linearGradient-8">
            <stop stop-color="#3370FF" stop-opacity="0.6203417" offset="0%"></stop>
            <stop stop-color="#FFFFFF" offset="100%"></stop>
        </linearGradient>
    </defs>
    <g id="办一下-pc" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="办一下-暂无新动态" transform="translate(-578.000000, -230.000000)">
            <g id="暂无消息-/评论" transform="translate(578.000000, 230.000000)">
                <rect id="矩形_322" fill="#FFFFFF" fill-rule="nonzero" opacity="0" x="0" y="0" width="160" height="160"></rect>
                <g id="组_157备份" transform="translate(7.170088, 7.775852)">
                    <path d="M103.130897,44 C97.3094661,44.0008144 92.0561041,40.3222717 89.8042919,34.6683599 C87.5524799,29.0144478 88.7420508,22.4895117 92.8219674,18.1160556 C96.9018841,13.7425995 103.07524,12.3748648 108.482393,14.6464183 C108.455729,14.2892766 108.442295,13.9282761 108.442091,13.563417 C108.45055,8.08119228 111.586847,3.14158164 116.394116,1.03911937 C121.201386,-1.0633429 126.737402,0.0834266362 130.43059,3.94673375 C134.123778,7.81004086 135.250271,13.6326759 133.286794,18.7099244 C137.96787,15.7664375 143.899526,16.2345775 148.111808,19.8799488 C152.32409,23.52532 153.910296,29.563248 152.076219,34.9705697 C150.242142,40.3778914 145.382571,43.9906684 139.930673,44 L103.130897,44 Z" id="联合_64备份" fill-opacity="0.2" fill="url(#linearGradient-1)" fill-rule="nonzero"></path>
                    <path d="M3.52502355,116.419048 C3.52502355,116.419048 1.103217,90.8554617 6.59345137,87.2059789 C12.0836857,83.5564961 15.9404873,93.4522906 15.9404873,93.4522906 C15.9404873,93.4522906 12.4245234,70.1141439 20.0873541,68.8325569 C27.7501848,67.5509699 29.9990655,90.8989825 29.9990655,90.8989825 C29.9990655,90.8989825 29.8953158,75.9879963 37.1042266,75.4435919 C44.3131375,74.8991874 48.6037925,104.163134 48.6037925,104.163134 L3.52502355,116.419048 Z" id="路径_63" fill="url(#linearGradient-2)" fill-rule="nonzero" transform="translate(25.798382, 92.600356) rotate(-7.000000) translate(-25.798382, -92.600356) "></path>
                    <g id="编组-18" transform="translate(79.536574, 51.479387)" fill-rule="nonzero">
                        <path d="M50.7503698,3 L50.7503698,33.8446511 C50.7503698,35.5015054 49.4072241,36.8446511 47.7503698,36.8446511 L43.9201895,36.8446511 L43.9201895,36.8446511 L35.3747516,42.3558909 C35.2297106,42.4494328 35.0363009,42.4076844 34.9427592,42.2626434 C34.9101972,42.2121545 34.8928791,42.1533495 34.8928791,42.0932711 L34.8928791,36.8446511 L3,36.8446511 C1.34314575,36.8446511 1.61901177e-14,35.5015054 0,33.8446511 L0,3 C-2.02906125e-16,1.34314575 1.34314575,7.48448398e-16 3,0 L47.7503698,0 C49.4072241,-4.27501655e-15 50.7503698,1.34314575 50.7503698,3 Z" id="矩形备份" fill="url(#linearGradient-3)"></path>
                        <path d="M31.0850939,18.604292 C29.1481666,18.6066169 27.5785272,20.2597734 27.5763197,22.2997608 C27.5785272,24.339748 29.1481666,25.9929045 31.0850939,25.9952295 L41.4268614,25.9952295 C43.3638467,25.9929906 44.9335763,24.3398089 44.9357839,22.2997608 C44.9335763,20.2597125 43.3638467,18.6065308 41.4268614,18.604292 L31.0850939,18.604292 Z M19.6352534,7.32304197 C17.698302,7.32545292 16.1286866,8.97865433 16.1264794,11.018667 C16.1289319,13.0584712 17.6984997,14.7113426 19.6352534,14.713667 L41.4268614,14.713667 C43.3638467,14.7114282 44.9335763,13.0582464 44.9357839,11.0181982 C44.9335764,8.97812466 43.3638706,7.32489808 41.4268614,7.32257322 L19.6352534,7.32304197 Z" id="路径_65" fill="#FFFFFF" opacity="0.568464007"></path>
                    </g>
                    <ellipse id="椭圆_16" fill-opacity="0.061778482" fill="url(#linearGradient-4)" fill-rule="nonzero" cx="72.6945736" cy="123.630398" rx="72.6945736" ry="21.09375"></ellipse>
                    <path d="M102.435526,65.3333333 L102.435526,104.666667 C102.435526,106.875806 100.644665,108.666667 98.4355263,108.666667 L93.6609772,108.666667 L93.6609772,108.666667 L82.5457064,115.835278 C82.4006655,115.92882 82.2072558,115.887071 82.1137141,115.74203 C82.081152,115.691542 82.0638341,115.632737 82.0638341,115.572658 L82.0638341,108.666667 L41.2378897,108.666667 C39.0287507,108.666667 37.2378897,106.875806 37.2378897,104.666667 L37.2378897,65.3333333 C37.2378897,63.1241943 39.0287507,61.3333333 41.2378897,61.3333333 L98.4355263,61.3333333 C100.644665,61.3333333 102.435526,63.1241943 102.435526,65.3333333 Z" id="矩形" fill="url(#linearGradient-5)" fill-rule="nonzero"></path>
                    <ellipse id="椭圆_26" fill="url(#linearGradient-6)" cx="53.2036253" cy="83.3317519" rx="4.27266065" ry="4.5"></ellipse>
                    <ellipse id="椭圆_27" fill="url(#linearGradient-7)" cx="69.8362241" cy="83.3317519" rx="4.35993816" ry="4.5"></ellipse>
                    <ellipse id="椭圆_28" fill="url(#linearGradient-6)" cx="86.4688229" cy="83.3317519" rx="4.27266065" ry="4.5"></ellipse>
                    <path d="M28.9228667,48.7137312 C26.094128,48.7141269 23.5414241,46.9266563 22.4472277,44.179318 C21.3530314,41.4319797 21.9310655,38.2613949 23.9135709,36.1362533 C25.8960764,34.0111116 28.8958222,33.3465045 31.5232555,34.4502936 C31.5102991,34.2767519 31.5037713,34.1013353 31.5036723,33.9240436 C31.5077828,31.2601313 33.0317661,28.8598848 35.3677055,27.8382603 C37.703645,26.8166358 40.3936956,27.3738719 42.1882827,29.2511228 C43.9828695,31.1283738 44.5302531,33.9576977 43.5761641,36.4248249 C45.8507838,34.9945313 48.7330827,35.2220089 50.7799069,36.9933609 C52.8267313,38.764713 53.5974972,41.6986516 52.7062859,44.3261672 C51.8150747,46.953683 49.4537214,48.7091967 46.804545,48.7137312 L28.9228667,48.7137312 Z" id="联合_64" fill-opacity="0.2" fill="url(#linearGradient-8)" fill-rule="nonzero"></path>
                </g>
            </g>
        </g>
    </g>
</svg>