.oaCard {
  border-radius: 8px !important;
  border: 1px solid #eceded !important;
  box-shadow: none !important;
}

.marginBottom {
  margin-bottom: 15px
}

.cardTitle {
  cursor: pointer;
  font-size: 16px;
  font-weight: bold;
  display: flex;
  align-items: center;
  margin-bottom: 15px;

  i {
    margin-left: 4px;
  }
}

.cursorPointer {
  cursor: pointer;
}

.addressBook .el-dialog__header {
  display: none !important;
}

.addressBook .el-dialog__body {
  padding: 0;
  background: #F2F2F6;
}

.addressBook .el-dialog__footer {
  background: #F2F2F6;
}

.amap-sug-result {
  z-index: 9999 !important;
}

/* 工作台四模块详情 el-drawer 头部样式 start */
.custom-detail-header .el-drawer__header {
  padding: 15px 10px;
  margin: 0;
  position: relative;
  border-bottom: 1px solid #eee;
}

.custom-detail-header .el-drawer__body {
  padding: 0 !important;
}

/* 工作台四模块详情 el-drawer 头部样式 end */

.el-table__header tr,
.el-table__header th {
  height: 50px;
  background: #F3F4F7 !important;
  color: #303133;
}

/* 各行 */
.el-table__body tr,
.el-table__body td {
  /* padding: 0; */
  /* height: 50px; */
}

.el-table .el-table__cell {
  /* padding: 0; */
}

.el-table--striped .el-table__body tr.el-table__row--striped td.el-table__cell {
  background-color: rgb(243 244 247 / 50%) !important;
}

.el-table--enable-row-hover .el-table__body tr:hover>td.el-table__cell {
  background-color: rgb(243 244 247 / 50%) !important;
}

.el-pagination.is-background .btn-next,
.el-pagination.is-background .btn-prev,
.el-pagination.is-background .el-pager li {
  background: #fff;
  border: 1px solid #ebeef5;
  font-weight: normal;
}

.el-dialog__header {
  margin-right: 0 !important;
}

.box .el-input {
  width: 240px !important;
  height: 40px !important;
}

.mBT {
  margin-bottom: 20px;
}

.bianji .el-drawer__header {
  margin-bottom: 0 !important;

}

/* 弹窗头 自定义 start */
.dialogTab .el-tabs__header {
  margin: 0;
}

.dialogTab .el-tabs__item {
  /* padding: 15px 20px; */
  height: 55px;
  line-height: 55px;
  font-size: 16px;
}

.dialogTab .el-tabs__nav {
  margin-left: 20px;
}

.dialogTab .el-tabs__nav-wrap::after {
  height: 1px;
  background-color: #ebeef5;
}

.dialogTab .el-tabs__content {
  padding: 0 20px;
}

/* 弹窗头 自定义 end */

/* 文字前边的竖杠 start */
.beforeStyle {
  padding-left: 12px;
  position: relative;
  font-size: 16px;
  margin-bottom: 10px;
  display: inline-block;
}

.beforeStyle:before {
  content: "";
  position: absolute;
  width: 4px;
  height: 16px;
  background-color: #3370FF;
  left: 0;
  top: 3px;
}

/* 文字前边的竖杠 end*/
.custom-page-header {
  border-bottom: 1px solid #eceded;
  padding-bottom: 15px;
  margin-bottom: 15px;
}

.custom-form {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
}

.custom-form .forms {
  width: 23%;
  margin-right: 20px;
}

.custom-form .el-form-item__label {
  line-height: normal !important;
  height: auto !important;
  padding-top: 7px !important;
  width: 83px;
  margin-right: 8px;
  justify-content: flex-end;
}

.redColor {
  color: #f56c6c;
}

.tach .el-dialog__body {
  height: 300px !important;
  overflow: auto !important;
}

/* 其他样式调整 */
.custom-placeholder .el-input__inner {
  height: 110px;
  position: relative;
  top: -30px;
  white-space: normal !important;
  word-break: break-all !important;
}

/* .custom-placeholder input::placeholder {

} */
.custom-placeholder .el-input__prefix-inner {
  position: relative;
  top: 14px;
  pointer-events: none;
  display: block;
  align-items: normal;
  justify-content: normal;
}


.el-date-editor {
  /* display: none !important; */
}

.hide_input {
  position: relative !important;
}

.hide_input .el-date-editor {
  position: absolute;
  /* top: 0 ;
  left: 0 ; */
  opacity: 0;

  top: -18px;
  left: 21px;
}

.el-step__head.is-success {
  color: #409EFF !important;
  border-color: #409EFF !important;
}

.el-step__title.is-success {
  color: #409EFF !important;
}

.blueColor {
  color: #409EFF;
}


/* 自定义dialog 发起弹窗样式修改 start*/
.custom-dialog-process .el-dialog__title {
  opacity: 0;
}

.custom-dialog-process .el-dialog__headerbtn {
  z-index: 10;
}

.dialogTab-process {
  margin-top: -75px;
}

.dialogTab-process .el-tabs__content {
  padding: 20px 0 0 !important;
  margin-left: -5px;
}

.dialogTab-process .el-tabs__header {
  margin-right: 50px;
}

.dialogTab-process .el-tabs__nav {
  margin-left: 10px
}

.dialogTab-process .el-tabs__active-bar {
  background-color: #303133;
  /* margin-left:-2px; */
}

.dialogTab-process .el-tabs__item.is-active {
  position: relative;
  color: #303133;
}

.dialogTab-process .el-tabs__item {
  color: #747677
}

.dialogTab-process .el-tabs__item:hover {
  color: #303133
}

/* 自定义dialog 发起弹窗样式修改 end*/
.el-image-viewer__wrapper {
  z-index: 10001 !important;
}

.tonghua .moren .el-checkbox__inner {
  background-color: #7EC1FB !important;
  border-color: #7EC1FB !important;
}

.tonghua .moren .el-checkbox__inner::after {
  /* border-color:#EFF7FF !important; */
  border-color: #A4D3FC !important;
}

/* 修改el-drawer位置 */
.el-drawer.rtl {
  top: 41px !important;
  height: auto !important;
}

/* 去掉el-drawer遮罩层默认颜色 */
.el-overlay {
  background-color: transparent !important;
}





.openItemDiv {
  position: relative;
  /* left: -15px; */
  top: -15px;
  /* margin-right: -30px; */
}

.isOaClass{
  top: -115px;
}
.isOaClass .form-contentItem{
  top: 75px;
}
.isFormClass{
  top: -255px;
  background: #F3F4F7;
}
.nextSubOa{
  background-color: #F3F4F7 !important;
  border-bottom: none !important;
  border: none !important;
}
.openItemDiv .el-card__body {
  padding-top: 0;
}
.transitionNone{
  transition:none !important;
}

.form-topItem {
  display: flex;
  box-shadow: inset 0px -1px 0px 0px #ECEDED;
  margin: 0 -15px;
  padding: 0 15px;
  position: fixed;
  background: #fff;
  width: 100%;
  box-sizing: border-box;
  z-index:100
}

.form-topItem .el-button {
  padding: 10px;
  font-size: 20px;
  font-weight: bold;
  position: relative;
  color: #333;
}
.form-topItem .el-button:hover{
  color: #333;
}
.form-topItem .el-button:hover::before{
  position: absolute;
  content: '';
  right: 0;
  left: 0;
  width: 30px;
  height: 30px;
  margin: auto;
  background: #F3F4F7;
  border-radius: 2px;
}
.form-topItem .el-button::after{
  position: absolute;
  content:'';
  right:-7px;
  width: 1px;
  height: 22px;
  background: #dfdfdf;
}
.openItem-tab-list {
  list-style-type: none;
  display: flex;
  padding: 10px 15px;
  z-index: 100;
}

.openItem-tab-list li {
  cursor: pointer;
  padding: 10px 20px;
  margin-right: 10px;
  border-radius: 5px;
  font-size: 15px;
}

.openItem-tab-list li:hover {
  background: #eee;
}

.openItem-tab-list li.active {
  position: relative;
}

.openItem-tab-list li.active::before {
  position: absolute;
  bottom: 0;
  content: '';
  width: 20px;
  height: 3px;
  background: #303133;
  left: 0;
  right: 0;
  margin: auto;
  border-radius: 1px;
}

.form-contentItem {
  top: 75px;
  background: #fff;
  position: relative;
  width: 100%;
  box-sizing: border-box;
  padding: 20px 10px;
  /* width: 80%; */
  margin: auto;
  border-radius: 6px;
  margin-bottom: 20px;
  border-bottom:20px solid #F3F4F7;
}
.form-contentItem .tableDiv{
  /* margin-top: -20px; */
}
.content-footer {
  padding-left: 15px;
  margin: 30px 0 20px;
}

@media print {
  @page {
    size: auto;
  }
  body, html,div{
    height: auto!important;
  }
}


.center-view {
  display: flex;
  justify-content: center;
  align-items: center;
}

.center-align {
  display: flex;
  justify-content: flex-start;
  align-items: center;
}

.center-justify {
  display: flex;
  justify-content: center;
  align-items: flex-start;
}

.textover {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.textover2 {
  overflow: hidden;
  white-space: pre-line;
  word-break: break-all;
  word-wrap: break-word;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}
