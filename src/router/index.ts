import type { App } from 'vue'
import type { RouteRecordRaw } from 'vue-router'
import { createRouter, createWebHashHistory } from 'vue-router'
import remainingRouter from './modules/remaining'


// console.log('remainingRouter=',remainingRouter)

// 创建路由实例
const router = createRouter({
  history: createWebHashHistory(), // createWebHashHistory URL带#，createWebHistory URL不带#
  strict: true,
  routes: remainingRouter as RouteRecordRaw[],
  scrollBehavior: () => ({ left: 0, top: 0 })
})

// console.log('Routes-000:', router.getRoutes())

export const resetRouter = (): void => {
  console.log('触发了resetRouter')
  const resetWhiteNameList = ['Redirect', 'Login', 'NoFind', 'Root']
  router.getRoutes().forEach((route) => {
    const { name } = route
    if (name && !resetWhiteNameList.includes(name as string)) {
      router.hasRoute(name) && router.removeRoute(name)
    }
  })
  // 2. 重新添加 remainingRouter 中的路由
  remainingRouter.forEach((route) => {
    if (!resetWhiteNameList.includes(route.name as string)) {
      router.addRoute(route)
    }
  })
}

export const setupRouter = (app: App<Element>) => {
  app.use(router)
}

export default router
