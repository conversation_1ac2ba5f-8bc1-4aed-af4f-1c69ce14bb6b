// import request from '@/config/axios/i2'
import request from '@/config/axios'

// 查询表单及分组
export const getGroupModels = async (params, isSub = false) => {
  return await request.get2({
    url: `/wflow/model/${isSub ? 'sub/' : ''}group/list`,
    params
  })
}
// 查询表单及分组
export const getGroupModelsByUser = async (params) => {
  return await request.get2({
    url: '/wflow/model/list/byUser',
    params
  })
}
// 查询所有模型分组
export const getModelGroups = async (params, isSub = false) => {
  return await request.get2({
    url: `/wflow/model/${isSub ? 'sub/' : ''}group`,
    params
  })
}
// 表单分组排序
export const modelGroupsSort = async (data, isSub = false) => {
  return await request.put({
    url: `/wflow/model/${isSub ? 'sub/' : ''}group/sort`,
    data
  })
}
// 表单排序
export const modelsSort = async (groupId, data, isSub = false) => {
  return await request.put({
    url: `/wflow/model/${isSub ? 'sub/' : ''}sort/${groupId}`,
    data
  })
}
// 修改分组
export const updateModelGroupName = async (groupId, params, isSub = false) => {
  return await request.put({
    url: `/wflow/model/${isSub ? 'sub/' : ''}group/${groupId}`,
    params
  })
}
// 新增模型分组
export const createModelGroup = async (params, isSub = false) => {
  return await request.post2({
    url: `/wflow/model/${isSub ? 'sub/' : ''}group`,
    params
  })
}
// 删除分组
export const deleteModelGroup = async (groupId, isSub = false) => {
  return await request.delete({
    url: `/wflow/model/${isSub ? 'sub/' : ''}group/${groupId}`,
  })
}
// 删除模型
export const deleteModel = async (modelId) => {
  return await request.delete({
    url: `/wflow/model/${modelId}`,
  })
}
// 获取模型
export const getModelById = async (modelId) => {
  return await request.get2({
    url: `/wflow/model/detail/${modelId}`,
  })
}
// 获取模型
export const getModelByDefId = async (defId) => {
  return await request.get2({
    url: `/wflow/model/detail/def/${defId}`,
  })
}
// 修改分组
export const modelMoveToGroup = async (modelId, groupId, isSub = false) => {
  return await request.put({
    url: `/wflow/model/${isSub ? 'sub/' : ''}${modelId}/move/${groupId}`
  })
}
// 启用或停用流程
export const enOrDisModel = async (modelId, type, params = {}, isSub = false) => {
  return await request.put({
    url: `/wflow/model/${isSub ? 'sub/' : ''}${modelId}/active/${type}`,
    params
  })
}
// 批量启用或停用流程 true禁用 false启用
export const batchEnOrDisModel = async (type: boolean, data: string[]) => {
  return await request.post({
    url: `/wflow/model/batch/active/${type}`,
    data
  })
}
export const processCcMeExport = async (params) => {
  return await request.download({
    url: `/wflow/process/ccMeExport`,
    params
  })
}
// 获取我收到的流程
export const getCcMeList = async (params) => {
  return await request.get2({
    url: '/wflow/process/ccMe',
    params
  })
}
// 获取控制台统计数据
export const getProcessCountData =  () => {
  return request.get2({
    url: '/wflow/process/instance/count',
  })
}
// 复制流程，复制的是草稿不发布，会在history中生成新版本记录
export const modelCopy =  (params) => {
  return request.get2({
    url: '/wflow/process/model/copy',
    params
  })
}

export const groupModelList =  (params) => {
  return request.get2({
    url: '/wflow/model/group/simple-list',
    params
  })
}

export const groupTenantList =  () => {
  return request.get2({
    url: '/system/tenant/simple-list',
  })
}
//应用列表
export function getAppList(params) {
  return request.get2({
    url: `system/work/app/list`,
    params: params,
  })
}
// 获取我收到的模块未读数量
export function getUnreadNum() {
  return request.get2({
    url: `oa/process-read-record/get-unread-num`,
  })
}
// 我收到的模块 全部标记为已读
export function allSetRead() {
  return request.get2({
    url: `oa/process-read-record/all-set-read`,
  })
}
export default {
  createModelGroup, updateModelGroupName, modelGroupsSort,
  modelsSort, getGroupModels, getModelGroups, modelMoveToGroup,
  deleteModelGroup, deleteModel, getModelById, getModelByDefId,
  enOrDisModel, batchEnOrDisModel, getCcMeList, getProcessCountData, getGroupModelsByUser,
  modelCopy,groupModelList,groupTenantList,getUnreadNum,allSetRead,processCcMeExport
}
