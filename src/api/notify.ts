// import request from '@/config/axios/i2'
import request from '@/config/axios'

//获取用户通知
export const getUserNotify = async (params) => {
  return await request.get2({
    url: `/wflow/notify`,
    params
  })
}
//已读通知
export const readNotify = async (data) => {
  return await request.put({
    url: `/wflow/notify`,
    data
  })
}

// 通知详情
export const readNotifyDetail = async (params) => {
  return await request.get({
    url: `/wflow/notify/get`,
    params
  })
}

// 公告列表 个人
export const getNoticeList = async (params) => {
  return await request.get2({
    url: `/system/notice/me/page`,
    params
  })
}
//公告详情
export const getNoticeDetail = async (params) => {
  return await request.get2({
    url: `/system/notice/me/get`,
    params
  })
}
// 公告列表 全部记录
export const getNoticeRecord = async (params) => {
  return await request.get2({
    url: `/system/notice/record/page`,
    params
  })
}
// 获取通知公告记录已读-未读人员
export const getReadUser = async (params) => {
  return await request.get2({
    url: `/system/notice/record/readUser`,
    params
  })
}
// 删除公告记录
export const noticeCancel = async (id) => {
  return await request.delete({
    url: `/system/notice/cancel?id=` + id ,
  })
}
// 公告列表 置顶
export const recordTopSet = async (id) => {
  return await request.get2({
    url: `/system/notice/record/top-set?recordId=` + id ,
  })
}
// 是否租户管理员
export const permissionIsAdmin = async () => {
  return await request.get2({
    url: `/system/permission/is-admin`,
  })
}
// 公告列表 隐藏
export const recordHiddenSet = async (id) => {
  return await request.get2({
    url: `/system/notice/record/hidden-set?recordId=` + id ,
  })
}

//获取强制阅读公告
export const getForceReadNotice = async () => {
  return await request.get2({
    url: `/system/notice/mandatory/read`,
  })
}
export default {
  getUserNotify,
  readNotify,
  readNotifyDetail,
  getNoticeList,
  getNoticeDetail,
  getNoticeRecord,
  getReadUser,
  noticeCancel,
  recordTopSet,
  permissionIsAdmin,
  recordHiddenSet,
  getForceReadNotice
}
