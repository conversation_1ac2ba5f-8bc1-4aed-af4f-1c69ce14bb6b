import request from '@/config/axios'


// 获取用户实时轨迹设置列表
export function getUserTrackSettingPage(params) {
  return request.get2({
    url: `system/user-track-setting/page`,
    params: params,
  })
}


// 后台管理发起定位
export function userTrackSettingSend(params) {
  return request.post2({
    url: `system/user-track-setting/send`,
    data: params,
  })
}

// 重新发起定位请求 
export function restartTrackSettingSend(params) {
  return request.post2({
    url: `system/user-track-setting/restart`,
    data: params,
  })
}

// 定位取消审核 
export function auditTrackSettingSend(params) {
  return request.post2({
    url: `system/user-track-setting/audit`,
    data: params,
  })
}


// 地图路线列表(日期分组)  
export function getUserTrackMonthPage(params) {
  return request.get2({
    url: `system/user-track/month/page`,
    params: params,
  })
}

// 地图展示列表
export function getUserTrackMapList(params) {
  return request.get2({
    url: `system/user-track/map/list`,
    params: params,
  })
}

// 定位详情 
export function getUserTrackDetailPage(params) {
  return request.get2({
    url: `system/user-track/page`,
    params: params,
  })
}

// 定位上报分页列表 
export function getLocationReportPage(params) {
  return request.get2({
    url: `oa/locationReport/page`,
    params: params,
  })
}

// 定位上报详情 
export function getLocationReportDetail(params) {
  return request.get2({
    url: `oa/locationReport/`+params,
    params: {},
  })
}