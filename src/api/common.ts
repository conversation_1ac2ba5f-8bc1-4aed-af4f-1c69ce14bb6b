// import request from '@/config/axios/i2'
import request from '@/config/axios'

// 查询表单组
export const getClientUrl = async (params) => {
  return await request.get2({
    url: '/system/dict/clientUrl',
    params
  })
}


//应用列表
export function getAppList(params) {
  return request.get2({
    url: `system/work/app/list`,
    params: params,
  })
}

//获得我的应用访问记录
export function getUserAppHistoryList(params) {
  return request.get2({
    url: `system/work/app/history/list`,
    params: params,
  })
}

//获得我的应用列表
export function getUserAppList(params) {
  return request.get2({
    url: `system/work/app/list/user`,
    params: params,
  })
}

// 新增我的应用 
export function appSaveUser(param) {
  return request.post2({
    url: 'system/work/app/save/user',
    data: param,
  })
}

// 删除个人应用  
export function delUserWorkApp(instanceId){
  return request.delete2({
    url: `system/work/app/${instanceId}`,
  })
}

// 我的应用排序  
export function sortUserAppList(params) {
  return request.post2({
    url: `system/work/app/sort`,
    data: params,
  })
}



// 引荐客户
export function addRecommendOrder(params) {
  return request.post2({
    url: `yqb/order/save`,
    data: params,
  })
}



// 获取产品列表
export function getProductPage(params) {
  return request.get2({
    url: `yqb/product/page`,
    params: params,
  })
}

// 获取产品详情
export function getProductDetail(params) {
  return request.get2({
    url: `yqb/product/detail`,
    params: params,
  })
}


// 查询分类管理列表
export function getProductCategoryList(params) {
  return request.get2({
    url: `yqb/category/list`,
    params: params,
  })
}

// 查询产品评论
export function getProductCommentPage(params) {
  return request.get2({
    url: `yqb/comment/page`,
    params: params,
  })
}


// 购买易企帮产品 
export function postYqbOrderPay(params) {
  return request.post2({
    url: `yqb/order/pay`,
    data: params,
  })
}

// 查询易企帮产品购买结果
export function getYqbOrderPayStatus(params) {
  return request.get2({
    url: `yqb/order/pay/status`,
    params: params,
  })
}

// 删除最近访问应用  
export function delUserWorkAppHistory(){
  return request.delete2({
    url: `system/work/app/history`,
  })
}

// 增加应用访问记录
export function addUserWorkAppHistory(params) {
  return request.post2({
    url: `system/work/app/history/save`,
    data: params,
  })
}

// 获取生活应用列表 - 带分组   
export function getLifeCenterAppList(params?) {
  return request.get2({
    url: `system/life/center/type/application/work/list`,
    params: params,
  })
}

// 获取生活应用列表 - page
export function getLifeCenterAppListPage(params: any) {
  return request.get2({
    url: 'system/life/center/type/app/page',
    params: params,
  })
}

// 获取易企帮产品订单列表 
export function getYqbOrderPage(params) {
  return request.get2({
    url: `yqb/order/page`,
    params: params,
  })
}

// 获取易企帮应用市场商家列表 
export function getYqbProductEntPage(params) {
  return request.get2({
    url: `yqb/ent/list`,
    params: params,
  })
}

// 获取易企帮应用市场商家详情
export function getYqbProductEntDetail(params) {
  return request.get2({
    url: `yqb/ent/detail`,
    params: params,
  })
}


// 获取易企帮商品规格列表 
export function getYqbProductSpecList(params) {
  return request.get2({
    url: `yqb/getSpecList`,
    params: params,
  })
}


// 引荐客户里更新订单信息
export function recommendOrderUpdate(params) {
  return request.post2({
    url: `yqb/order/update`,
    data: params,
  })
}