import request from '@/config/axios'

// 入职信息 VO
export interface EntryVO {
  id: number // 用户ID
  name: string // 姓名
  deptId: number // 部门ID
  postIds: string // 岗位编号数组
  entryTable: number // 是否提交入职登记表
  preEntryTime: Date // 预计入职时间
  opSourceType: number // 操作方式
  realAuthStatus: number // 实人认证
}

// 入职信息 API
export const EntryApi = {
  // 查询入职信息分页
  getEntryPage: async (params: any) => {
    return await request.get({ url: `/system/entry/page`, params })
  },

  // 查询入职信息详情
  getEntry: async (id: number) => {
    return await request.get({ url: `/system/entry/get?id=` + id })
  },

  // 新增入职信息
  createEntry: async (data: EntryVO) => {
    return await request.post({ url: `/system/entry/create`, data })
  },

  // 修改入职信息
  updateEntry: async (data: EntryVO) => {
    return await request.put({ url: `/system/entry/update`, data })
  },

  // 删除入职信息
  deleteEntry: async (id: number) => {
    return await request.delete({ url: `/system/entry/delete?id=` + id })
  },

  // 导出入职信息 Excel
  exportEntry: async (params) => {
    return await request.download({ url: `/system/entry/export-excel`, params })
  },
  // 获取办理入职链接
  getEntryUrl: async (params: any) => {
    return await request.get({ url: `/system/entry/geturl`, params })
  },
  // 获取确认到岗信息
  getConfirm: async (data) => {
    return await request.post({ url: `/system/entry/confirm`, data })
  },
}
