import request from '@/config/axios'

// 自定义表单 VO
export interface CustomTableVO {
  id: number // ID
  tableName: string // 表名称
  tableComment: string // 表描述
  remark: string // 备注
}

// 自定义表单 API
export const CustomTableApi = {
  // 查询自定义表单分页
  getCustomTablePage: async (params: any) => {
    return await request.get({ url: `/system/custom-table/page`, params })
  },

  // 查询自定义表单详情
  getCustomTable: async (id: number) => {
    return await request.get({ url: `/system/custom-table/get?id=` + id })
  },

  // 新增自定义表单
  createCustomTable: async (data: CustomTableVO) => {
    return await request.post({ url: `/system/custom-table/create`, data })
  },

  // 修改自定义表单
  updateCustomTable: async (data: CustomTableVO) => {
    return await request.put({ url: `/system/custom-table/update`, data })
  },

  // 删除自定义表单
  deleteCustomTable: async (id: number) => {
    return await request.delete({ url: `/system/custom-table/delete?id=` + id })
  },

  // 导出自定义表单 Excel
  exportCustomTable: async (params) => {
    return await request.download({ url: `/system/custom-table/export-excel`, params })
  },

// ==================== 子表（自定义表单字段） ====================

  // 获得自定义表单字段分页
  getCustomFieldPage: async (params) => {
    return await request.get({ url: `/system/custom-table/custom-field/page`, params })
  },
  // 新增自定义表单字段
  createCustomField: async (data) => {
    return await request.post({ url: `/system/custom-table/custom-field/create`, data })
  },

  // 修改自定义表单字段
  updateCustomField: async (data) => {
    return await request.put({ url: `/system/custom-table/custom-field/update`, data })
  },

  // 删除自定义表单字段
  deleteCustomField: async (id: number) => {
    return await request.delete({ url: `/system/custom-table/custom-field/delete?id=` + id })
  },

  // 获得自定义表单字段
  getCustomField: async (id: number) => {
    return await request.get({ url: `/system/custom-table/custom-field/get?id=` + id })
  },

// ==================== 子表（自定义表单分组） ====================

  // 获得自定义表单分组分页
  getCustomGroupPage: async (params) => {
    return await request.get({ url: `/system/custom-table/custom-group/page`, params })
  },
  // 新增自定义表单分组
  createCustomGroup: async (data) => {
    return await request.post({ url: `/system/custom-table/custom-group/create`, data })
  },

  // 修改自定义表单分组
  updateCustomGroup: async (data) => {
    return await request.put({ url: `/system/custom-table/custom-group/update`, data })
  },

  // 删除自定义表单分组
  deleteCustomGroup: async (id: number) => {
    return await request.delete({ url: `/system/custom-table/custom-group/delete?id=` + id })
  },

  // 获得自定义表单分组
  getCustomGroup: async (id: number) => {
    return await request.get({ url: `/system/custom-table/custom-group/get?id=` + id })
  },
   // 获得自定义表单分组详情
  getDetail: async (name) => {
    return await request.get({ url: `/system/custom-table/get/detail?tableName=` + name })
  },
}
