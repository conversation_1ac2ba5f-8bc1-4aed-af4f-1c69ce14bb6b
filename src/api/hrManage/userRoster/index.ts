import request from '@/config/axios'

// 花名册 VO
export interface UserRosterVO {
  id: number // ID
  userId: number // 用户ID
  username: string // 用户姓名
  sex: number // 用户性别
  mobile: string // 手机号码
  education: string // 学历
  address: string // 家庭住址
  idcard: string // 身份证号
  birthday: string // 出生年月日
  bankNo: string // 银行卡号
  bankName: string // 开户行
  urgentContact: string // 紧急联系人
  urgentContactMobile: string // 紧急联系人手机号
  urgentContactRelation: string // 紧急联系人与本人关系
  deptId: number // 部门ID
  postIds: string // 岗位编号数组
  status: number // 帐号状态（0正常 1停用）
  remark: string // 备注
}

// 花名册 API
export const UserRosterApi = {
  // 查询花名册分页
  getUserRosterPage: async (data: any) => {
    return await request.post({ url: `/system/user-roster/page`, data })
  },
  // 查询花名册详情
  getUserRoster: async (id: number) => {
    return await request.get({ url: `/system/user-roster/get?mobile=` + id })
  },
  // 查询入职管理详情
  getEntryByMobile: async (id: number) => {
    return await request.get({ url: `/system/entry/getEntryByMobile?mobile=` + id })
  },
  // 删除花名册
  deleteUserRoster: async (id: number) => {
    return await request.delete({ url: `/system/user-roster/delete?id=` + id })
  },
  // 导出花名册 Excel
  exportUserRoster: async (params) => {
    return await request.download({ url: `/system/user-roster/export-excel`, params })
  },
  // 入职管理-获取员工状态各状态人数
  getEmpStatus: async () => {
    return await request.get({ url: `/system/user-roster/get/empstatus` })
  },
  //入职管理- 获取员工类型各类型人数
  getEmpNum: async () => {
    return await request.get({ url: `/system/user-roster/get/empnum` })
  },
  // 个人资料详情-花名册保存 个人资料
  saveCustomData: async (data:any) => {
    return await request.post2({ url: `/system/custom-data/save`,data})
  },
  // 个人资料详情-员工管理保存 个人资料
  commitCustomForm: async (data:any) => {
    return await request.post2({ url: `/system/entry/commit`,data})
  },
  //  个人资料详情-自定义表单列表
  getCustomForm: async (params) => {
    return await request.get2({ url: `/system/custom-table/get/detail`,params})
  },
  // 查询花名册详情-个人
  getPersonRoster: async () => {
    return await request.get({ url: `/system/user-roster/personal-info` })
  },
  // 个人资料详情-花名册-个人保存 个人资料
  saveCustomPersonData: async (data:any) => {
    return await request.post2({ url: `/system/user-roster/save`,data})
  },
  // 个人保存 保存后推送入职签约信息
  pushContractCustomPersonData: async (data:any) => {
    return await request.post2({ url: `/system/user-roster/contract/info/push`,data})
  },
}
