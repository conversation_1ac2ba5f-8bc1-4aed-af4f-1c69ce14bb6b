import request from '@/config/axios'

export const RegisterApi = {
  // 获取租户列表
  getTenantList: () => {
    return request.get({ url: '/system/register/tenant' })
  },

  // 注册用户及租户
  registerAndCreateEnt: (data: any) => {
    return request.post({ url: '/system/register/tenant', data })
  },

  // 注册用户并加入租户
  registerAndJoinEnt: (data: any) => {
    return request.post({ url: '/system/register/user', data })
  },

  // 上传图片/附件
  uploadFile: async (data: any) => {
    return await request.upload({ url: '/wflow/res',data})
  }
}