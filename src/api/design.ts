// import request from '@/config/axios/i2'
import request from '@/config/axios'

// 查询表单组
export const getFormGroups = async (params) => {
  return await request.get2({
    url: '/admin/form/group',
    params
  })
}
// 表单排序
export const groupItemsSort = async (data) => {
  return await request.put({
    url: '/admin/form/group/sort',
    data
  })
}
// 更新表单组
export const updateGroup = async (params, method) => {
   return await request[method]({
    url: "/admin/form/group",
    params,
  })
}
// 获取表单分组
export const getGroup = async () => {
  return await request.get2({
    url: '/admin/form/group/list',
  })
}
// 更新表单
export const updateForm = async (params) => {
  return await request.put({
    url: '/admin/form',
    params
  })
}

export const createForm = async (data) => {
  return await request.post2({
    url: '/admin/form',
    data
  })
}
// 查询表单详情
export const getFormDetail = async (id: any) => {
  return await request.get2({ url: `/admin/form/detail?id=` + id })
}
// 更新表单详情
export const updateFormDetail = async (data) => {
  return await request.put({
    url: '/admin/form/detail',
    data
  })
}


export default {
  getFormGroups,
  groupItemsSort,
  createForm,
  getFormDetail,
  updateGroup,
  getGroup,
  updateForm,
  updateFormDetail,
}
