import request from '@/config/axios'

export const fileCloudApi = {
  //团队文件 根目录
  rootPage: async (params) => {
    return await request.get({ url: `/system/cloud-drive/rootpage`,params })
  },
  //
  cloudList: async (params) => {
    return await request.get({ url: `/system/cloud-drive/cloudlist`,params })
  },
  //
  create: async (data) => {
    return await request.post({ url: `/system/cloud-drive/create`,data })
  },
  //
  update: async (data) => {
    return await request.put({ url: `/system/cloud-drive/update`,data })
  },
  delete: async (id) => {
    return await request.delete({ url: `/system/cloud-drive/delete?id=` + id })
  },
  // 权限 我的
  getMyPermission: async (params) => {
    return await request.get({ url: `/system/cloud-permission/getme`,params })
  },
  // 权限管理列表
  permissionList: async (params) => {
    return await request.get({ url: `/system/cloud-permission/list`,params })
  },
  // 新增
  permissionCreate: async (data) => {
    return await request.post({ url: `/system/cloud-permission/create`,data })
  },
  // 批量新增
  permissionBatchCreate: async (data) => {
    return await request.post({ url: `/system/cloud-permission/batch-create`,data })
  },
  permissionUpdate: async (data) => {
    return await request.put({ url: `/system/cloud-permission/update`,data })
  },
  permissionDelete: async (id) => {
    return await request.delete({ url: `/system/cloud-permission/delete?id=` + id })
  },
  // 批量新增文件
  permissionBatchFile: async (data) => {
    return await request.post({ url: `/system/cloud-file/batch-create`,data })
  },
  fileDelete: async (id) => {
    return await request.delete({ url: `/system/cloud-file/delete?id=` + id })
  },
  fileUpload: async (data) => {
    return await request.put({ url: `/system/cloud-file/update`,data })
  },
}