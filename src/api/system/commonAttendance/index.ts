import request from '@/config/axios'


// 查询考勤上传记录分页列表
export const getWorkAttendancePage = async (params) => {
  return await request.get2({ url: '/work-attendance/page', params })
}

//上传考勤原始记录
export const workAttendanceAdd = async (data) => {
  return await request.post2({ url: '/work-attendance/save', data })
}

// 删除考勤   
export const deleteWorkAttendance = async (id: number) => {
  return await request.delete2({ url: '/work-attendance/' + id })
}

// 查询考勤操作记录分页列表 
export const getWorkAttendanceOperatePage = async (params) => {
  return await request.get2({ url: '/work-attendance/operate/page', params })
}

// 查询考勤明细分页列表 
export const getWorkAttendanceDetailPage = async (params) => {
  return await request.get2({ url: '/work-attendance/detail/page', params })
}

// 考勤详情状态修改与审核  
export const workAttendanceAudit = async (data) => {
  return await request.post2({ url: '/work-attendance/audit', data })
}

// 修改考勤明细 
export const workAttendanceUpdateDetail = async (data) => {
  return await request.post2({ url: '/work-attendance/update/detail', data })
}

// 获取考勤班次
export const getShiftList = async (params) => {
  return await request.get2({ url: '/system/attendance/manager/shift/list', params })
}

// 查询工作日分页列表
export const getWorkDatePage = async (params) => {
  return await request.get2({ url: '/work-date/page', params })
}

// 新增工作日  
export const workDateAdd = async (data) => {
  return await request.post2({ url: '/work-date', data })
}

// 查询工作日设置列表  
export const getWorkDateSettingList = async (params) => {
  return await request.get2({ url: '/work-date-setting/list', params })
}

// 批量修改工作日设置列表 
export const setWorkDateSettingBatch = async (data) => {
  return await request.put2({ url: '/work-date-setting/batch', data })
}



//获取薪资考勤规则 
export const getSalaryAttendanceRule = async (params) => {
  return await request.get2({ url: '/system/salary-attendance-rule/list', params })
}

//修改薪资考规则 
export const salaryAttendanceRuleUpdate = async (data) => {
  return await request.post2({ url: '/system/salary-attendance-rule/update', data })
}



// 新增打卡班次
export const attendanceShiftAdd = async (data) => {
  return await request.post2({ url: '/system/attendance/manager/shift/add', data })
}


// 获取部门列表
export const getDeptListNew = async (params) => {
  return await request.get2({ url: '/system/dept/new/list', params })
}


// 薪资考核部门人员绑定 
export const salaryRuleUserBind = async (data) => {
  return await request.post2({ url: '/system/salary-rule-user/bind', data })
}


// 薪资考核已选择考勤人员列表
export const getSalaryRuleUserBindList = async (params) => {
  return await request.get2({ url: '/system/salary-rule-user/bind/list', params })
}

// 薪资考核部门架构树列表
// export const getSalaryRuleTreeList = async (params) => {
//   return await request.get2({ url: '/system/salary-rule-tree/dept', params })
// }