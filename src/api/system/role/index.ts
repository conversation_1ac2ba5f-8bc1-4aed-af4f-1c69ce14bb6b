import request from '@/config/axios'

export interface RoleVO {
  id: number
  name: string
  code: string
  sort: number
  status: number
  type: number
  dataScope: number
  dataScopeDeptIds: number[]
  createTime: Date
}

export interface UpdateStatusReqVO {
  id: number
  status: number
}

// 查询角色列表
export const getRolePage = async (params: PageParam) => {
  return await request.get({ url: '/system/role/page', params })
}

// 查询角色（精简)列表
export const getSimpleRoleList = async (): Promise<RoleVO[]> => {
  return await request.get({ url: '/system/role/simple-list' })
}

// 查询角色详情
export const getRole = async (id: number) => {
  return await request.get({ url: '/system/role/get?id=' + id })
}

// 新增角色
export const createRole = async (data: RoleVO) => {
  return await request.post({ url: '/system/role/create', data })
}

// 修改角色
export const updateRole = async (data: RoleVO) => {
  return await request.put({ url: '/system/role/update', data })
}

// 修改角色状态
export const updateRoleStatus = async (data: UpdateStatusReqVO) => {
  return await request.put({ url: '/system/role/update-status', data })
}

// 删除角色
export const deleteRole = async (id: number) => {
  return await request.delete({ url: '/system/role/delete?id=' + id })
}

// 导出角色
export const exportRole = (params) => {
  return request.download({
    url: '/system/role/export-excel',
    params
  })
}


//新的
//个人 查询日历事件
export const getDateList = async (data) => {
  return await request.post({ url: '/customize/userCalendarEvent/v1/selectUserCalendarEventList', data })
}
// 个人新增日历事件
export const addUserCalenderEvent = async (data) => {
  return await request.post({ url: '/customize/userCalendarEvent/v1/addUserCalendarEvent', data })
}
//个人 查询日历日程详细事件
export const getUserEventDetail = async (data) => {
  return await request.post({ url: '/customize/userCalendarEvent/v1/selectUserCalendarEventDetail', data })
}
// 部门新增日历事件
export const addDepartmentCalenderEvent = async (data) => {
  return await request.post({ url: '/customize/departmentCalendarEvent/v1/addDepartmentCalendarEvent', data })
}
//部门 查询日历日程详细事件
export const selectDepartmentCalenderEventDetail = async (data) => {
  return await request.post({ url: '/customize/departmentCalendarEvent/v1/selectDepartmentCalendarEventDetail', data })
}
export const getParentList = async (params) => {
  return await request.get({ url: '/system/dept/parent-list', params })
}
export const getOrgtree = async (params) => {
  return await request.get({ url: '/oa/org/tree', params })
}

export const getUser = async (params) => {
  return await request.get({ url: '/system/user/get', params })
}

export const getUserSeniorManager = async (params) => {
  return await request.get({ url: '/system/user-senior-manager/get-permission', params })
}

export const selectDepartmentCalenderEventList = async (data) => {
  return await request.post({ url: '/customize/departmentCalendarEvent/v1/selectDepartmentCalendarEventList', data })
}

export const importDepartmentCalenderEvent = async (data) => {
  return await request.upload({
     url: '/customize/departmentCalendarEvent/v1/importDepartmentCalendarEvent',
     data 
    })
}

export const dowCalenderEventExcel = async () => {
  return await request.download({
     url: '/customize/userCalendarEvent/v1/dowCalendarEventExcel',
    })
}

export const selectDepartmentEventUserConfirmList = async (data) => {
  return await request.post({ url: '/customize/departmentCalendarEvent/v1/selectDepartmentEventUserConfirmList', data })
}

export const confirmCalendarEvent = async (data) => {
  return await request.post({ url: '/customize/userCalendarEvent/v1/confirmCalendarEvent', data })
}