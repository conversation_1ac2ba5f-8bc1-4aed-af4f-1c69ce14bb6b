import request from '@/config/axios'

export const createDing = async (data) => {
  return await request.post({ url: '/oa/ding/create', data })
}

export const dingPage = async (params) => {
  return await request.get({ url: '/oa/ding/page' ,params})
}
export const dingGet = async (params) => {
  return await request.get({ url: '/oa/ding/get',params })
}

export const getUrgent = async (instanceId,nodeId) => {
  return await request.get2({ url: `/oa/ding/getUrgent/${instanceId}/${nodeId}` })
  // return await request.get({ url: `/oa/ding/getUrgent`,params })
}

export const createDingMessage = async (data) => {
  return await request.post({ url: '/oa/ding-message/create', data })
}

// 删除
export const deleteDing = async (id) => {
  return await request.delete({ url: '/oa/ding/delete?id='+id })
}
// 撤销
export const cancelDing = async (id) => {
  return await request.delete({ url: '/oa/ding/cancel?id='+id })
}

export const deleteDingMessage = async (id) => {
  return await request.delete({ url: '/oa/ding-message/delete?id='+id })
}

export const recoveryDing = async (id) => {
  return await request.get({ url: '/oa/ding/recovery?id='+id })
}

export const dingAllSetRead = async () => {
  return await request.get({ url: '/oa/ding/all-set-read' })
}

export const dingMessageAllSetRead = async () => {
  return await request.get({ url: '/oa/ding-message/all-set-read' })
}