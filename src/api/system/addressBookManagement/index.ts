import request from '@/config/axios'



export const editUser = async (data) => {
  return await request.put({ url: '/system/user/edit', data })
}

export const addUsers = async (data) => {
  return await request.post({ url: '/system/user/add', data })
}



export const setDeptLeader = async (params) => {
  return await request.get({ url: '/system/user/setDeptLeader', params })
}

// export const importDepartmentCalenderEvent = async (data) => {
//   return await request.upload({
//      url: '/customize/departmentCalendarEvent/v1/importDepartmentCalendarEvent',
//      data 
//     })
// }



// export const selectDepartmentEventUserConfirmList = async (data) => {
//   return await request.post({ url: '/customize/departmentCalendarEvent/v1/selectDepartmentEventUserConfirmList', data })
// }

// export const confirmCalendarEvent = async (data) => {
//   return await request.post({ url: '/customize/userCalendarEvent/v1/confirmCalendarEvent', data })
// }