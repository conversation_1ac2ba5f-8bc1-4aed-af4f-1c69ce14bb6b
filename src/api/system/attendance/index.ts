import request from '@/config/axios'

export const attendanceApi = {
  //考勤概览
  queryTeamDailyStatics: async (params) => {
    return await request.get({ url: `/system/attendance/statistics/queryTeamDailyStatics`,params })
  },
  //报表管理-列表
  reportList: async (data) => {
    return await request.post({ url: `/system/attendance/statistics/calculator/report`,data })
  },
  //报表管理详情 原始数据-表头
  getRecordHeader: async (params) => {
    return await request.get({ url: `/system/attendance/statistics/calculator/v1/report/table/getRecordHeader`,params })
  },
  //报表管理详情 原始数据-body
  getRecordBody: async (data) => {
    return await request.post({ url: `/system/attendance/statistics/calculator/v1/report/table/getRecordBody`,data })
  },
  //详情 创建导出任务
  createReportExportTask: async (params) => {
    return await request.get({ url: `/system/attendance/statistics/calculator/v1/report/createReportExportTask`,params })
  },
  //导出记录
  reportRecord: async (params) => {
    return await request.get({ url: `/system/attendance/statistics/calculator/v1/report/record`,params })
  },
}