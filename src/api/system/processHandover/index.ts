import request from '@/config/axios'
export const processHandoverData = async (params) => {
  return await request.get({ url: '/oa/process-handover/page',params})
}

export const createHandover = async (data) => {
  return await request.post({ url: '/oa/process-handover/create',data})
}

export const userTodoList = async (params) => {
  return await request.get({ url: '/oa/process-handover/userTodoList',params})
}

export const replaceTodoProcess = async (params) => {
  return await request.get({ url: '/oa/process-handover/replaceTodoProcess',params})
}

