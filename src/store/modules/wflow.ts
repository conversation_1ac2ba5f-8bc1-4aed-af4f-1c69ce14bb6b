import { defineStore } from 'pinia'
import { store } from '../index'
interface WFlowState {
  nodeMap: any;
  isEdit: any;
  loginUser: any;
  selectedNode: any;
  selectFormItem: any;
  design: any;
  isJump:any;
}

export const useWFlowStore = defineStore('wFlow', {
  state: (): WFlowState => {
    return {
      nodeMap: new Map(),
      isEdit: null,
      loginUser: JSON.parse(localStorage.getItem('loginUser') || '{}'),
      selectedNode: {},
      selectFormItem: null,
      design: {},
      isJump:null,
    }
  },
  getters: {
    
  },
  actions: {
    selectedNode(val:any) {
      this.selectedNode = val
    },
    loadForm(val:any) {
      this.design = val
    },
    setIsEdit(val:any) {
      this.isEdit = val
    },
    setIsJump(val:any){
      this.isJump = val
    }
  }
})

export const useWFlowStoreWithOut = () => {
  return useWFlowStore(store)
}
