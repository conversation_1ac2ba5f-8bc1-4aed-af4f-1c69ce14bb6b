import { defineStore } from 'pinia';

export const useMyStore = defineStore('myStore', {
  state: () => ({
    jumpMessage: {},//监听是否来源为搜索联系人 外部跳转携带id打开消息模块的
    toMessage:false,//是否外部跳转进消息模块的 true去滚动到最底部
    launchStatus:false,//点进发起模块 来源出入状态
    launchSuccess:false,
    openForm:false,//审批详情进入 app.vue判断值
    openFormObj:{},
    formClose:false,
    wsPush:false,//ws是否推送 用于监听审批数量变化
    todoNum:0,
    ccNum:0,
    processNum:0,
  }),
  actions: {
    updateValue(newValue) {
      this.jumpMessage = newValue
    },
    handleMessage(newValue) {
      this.toMessage = newValue
    },
    handleLaunchStatus(newValue){
      this.launchStatus = newValue
    },
    handleLaunchSuccess(newValue){
      this.launchSuccess = newValue
    },
    handleOpenForm(newValue){
      this.openForm = newValue
    },
    handleFormObj(newValue){
      this.openFormObj = newValue
    },
    handleFormClose(newValue){
      this.formClose = newValue
    },
    handleWS(newValue){
      this.wsPush =newValue
    },
    handleTodoNum(newValue){
      this.todoNum = newValue
      this.handleProcessNum()
    },
    handleCcNum(newValue){
      this.ccNum = newValue
      this.handleProcessNum()
    },
    handleProcessNum(){
      this.processNum = this.todoNum + this.ccNum
    }
  }
});
