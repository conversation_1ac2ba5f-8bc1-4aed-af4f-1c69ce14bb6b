<template>
  <Dialog :title="formType == 'create' ? '创建分类' : '重命名'" v-model="dialogVisible" width="450">
    <el-form ref="formRef" :model="formData" :rules="formRules">
      <el-form-item prop="name">
        <el-input v-model="formData.name" placeholder="请输入分类名称" />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="dialogVisible = false">取消</el-button>
      <el-button @click="submitForm" type="primary" :disabled="formLoading" :loading="formLoading">
        {{ formType == 'create' ? '创建' : '确定' }}
      </el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
import { rulelibApi } from '@/api/rulelib/list'

const { t } = useI18n()
const message = useMessage()

const dialogVisible = ref(false)
const formLoading = ref(false)
const formType = ref('')
const formData = ref({
  name: undefined
})
const formRules = reactive({
  name: [{ required: true, message: '文件夹名称不能为空', trigger: 'blur' }]
})
const formRef = ref()

/** 打开弹窗 */
const rowObj = ref({
  parentId: undefined,
  id: undefined,
  type: undefined,
  pid: undefined
})

const open = async (type: string, row: any) => {
  rowObj.value = row
  dialogVisible.value = true
  formType.value = type

  resetForm()
  if (type == 'rename') {
    formData.value.name = row.name
  }
}

defineExpose({ open })
const emit = defineEmits(['success'])

const submitForm = async () => {
  await formRef.value.validate()
  formLoading.value = true
  try {
    if (formType.value === 'create') {
      await rulelibApi.create({
        className: formData.value.name,
        pid: rowObj.value.parentId
      })
      message.success(t('common.createSuccess'))
    } else {
      await rulelibApi.editTreeItemName({
        className: formData.value.name,
        pid: rowObj.value.pid,
        id: rowObj.value.id
      })
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    emit('success', formType.value)
  } finally {
    formLoading.value = false
  }
}

const resetForm = () => {
  formData.value = {
    name: undefined
  }
  formRef.value?.resetFields()
}
</script>