<!-- src/components/SelectUser.vue -->
<template>
  <el-dialog
    v-model="dialogVisible"
    title="选择联系人"
    width="800px"
    :close-on-click-modal="false"
    destroy-on-close
    class="pick-user-dialog"
  >
    <div class="container">
      <!-- 左侧搜索区域 -->
      <div class="w-1/2 border-r pr-4">
        <el-input
          v-model="pickUserSearch"
          placeholder="搜索名字、拼音、手机号..."
          prefix-icon="Search"
          clearable
          @input="handlePickUserSearch"
        />

        <div class="mt-4 space-y-2">
          <template v-if="!pickUserSearch">
            <div class="text-gray-500">输入关键字搜索联系人</div>
          </template>
          <template v-else-if="pickUserSearch && pickUserSearchResult.length > 0">
            <div class="space-y-2 max-h-96 overflow-y-auto hide-scrollbar">
              <div
                v-for="contact in pickUserSearchResult"
                :key="contact?.id || Math.random()"
                class="p-2 rounded cursor-pointer hover:bg-gray-100 flex items-center"
                @click="contact && contact.id ? handlePickUserSelect(contact) : null"
              >
                <el-radio :label="contact?.id" v-model="pickUserSelectedList">
                  {{ ' ' }}
                </el-radio>
                <el-avatar :size="40" :src="contact?.portrait || ''" shape="square" />
                <div class="ml-3">
                  <div
                    class="font-medium"
                    v-html="highlightKeyword(contact?.name || '', pickUserSearch)"
                  ></div>
                  <template v-if="contact?.type === 1">
                    <div class="text-sm text-gray-500">{{ contact?.postNames || '' }}</div>
                    <div class="text-sm text-gray-500">{{ contact?.objectName || '' }}</div>
                  </template>
                  <template v-else>
                    <div class="text-sm text-gray-500">
                      包含：<span class="text-blue-500">{{ pickUserSearch }}</span>
                    </div>
                  </template>
                </div>
              </div>
            </div>
          </template>
          <template v-else>
            <div class="text-gray-500">sorry，没有搜索到相关联系人</div>
          </template>
        </div>
      </div>
      <el-divider direction="vertical" />

      <!-- 右侧最近聊天区域 -->
      <div class="w-1/2">
        <div class="text-gray-500 mb-4 font-bold">最近聊天</div>
        <div class="space-y-2 max-h-96 overflow-y-auto hide-scrollbar">
          <div
            v-for="contact in pickUserRightList"
            :key="contact?.id || Math.random()"
            @click="contact && contact.id ? handlePickUserSelect(contact) : null"
            class="p-2 rounded cursor-pointer hover:bg-gray-100 flex items-center"
          >
            <el-radio :label="contact?.id" v-model="pickUserSelectedList">
              {{ ' ' }}
            </el-radio>
            <el-avatar :size="40" :src="contact?.portrait || ''" shape="square" />
            <div class="ml-3">
              <div class="font-medium">{{ contact?.name || '' }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" @click="handleConfirm" :disabled="!pickUserSelectedList"
          >确定
        </el-button>
        <el-button @click="dialogVisible = false">取消</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref } from 'vue';
import { getUserConvs } from '@/api/message';
import { searchUserFromDeptAndChatByName } from '@/api/org';
import { generateGroupAvatar, generateTextAvatar } from '@/utils/avatar';
import { useWFlowStore } from '@/store/modules/wflow';
import { ElMessage } from 'element-plus';

const emit = defineEmits(['confirm']);

const dialogVisible = ref(false);
const pickUserSearch = ref('');
const pickUserSearchResult = ref([]);
const pickUserRightList = ref([]);
const pickUserSelectedList = ref(null);
const selectArr = ref([]);
const loading = ref(false);
const debounceTimer = ref(null);

// 打开弹窗并初始化数据
const open = async () => {
  pickUserSearch.value = '';
  pickUserSelectedList.value = null;
  pickUserSearchResult.value = [];
  selectArr.value = [];
  loading.value = true;

  try {
    // 加载最近联系人
    const rsp = await getUserConvs({ userId: useWFlowStore().loginUser.id });
    pickUserRightList.value = [];
    const promises = [];

    rsp.data.convList.forEach((item) => {
      if (item.groupInfo) {
        try {
          const extra = JSON.parse(item.groupInfo.extra || '{}');
          const groupMemberList = extra.groupMemberList?.map((member) => ({
            name: member.alias || '',
            avatar: member.avatar || '',
          })) || [];

          const index = pickUserRightList.value.length;
          const promise = generateGroupAvatar(groupMemberList).then((dataUrl) => {
            const { target_id, type, name } = item.groupInfo;
            if (target_id) {
              pickUserRightList.value[index] = {
                id: target_id,
                name: name || '',
                portrait: dataUrl,
                type: type || 2,
              };
            }
          });
          promises.push(promise);
        } catch (error) {
          console.error('解析 groupInfo extra 失败:', error);
        }
      } else if (item.target) {
        const { userId, type, displayName, portrait } = item.target;
        if (userId) {
          pickUserRightList.value.push({
            id: userId,
            name: displayName || '',
            portrait: portrait || generateTextAvatar(displayName || ''),
            type: type || 1,
          });
        }
      }
    });

    await Promise.all(promises);
    dialogVisible.value = true;
  } catch (err) {
    console.error('加载最近联系人失败:', err);
    ElMessage.error('加载最近联系人失败');
  } finally {
    loading.value = false;
  }
};

// 搜索防抖处理
const handlePickUserSearch = () => {
  if (debounceTimer.value) {
    clearTimeout(debounceTimer.value);
  }
  debounceTimer.value = setTimeout(() => {
    executeSearch();
  }, 600);
};

// 执行搜索
const executeSearch = async () => {
  loading.value = true;
  const userName = pickUserSearch.value.trim();
  if (userName === '') {
    loading.value = false;
    return;
  }
  pickUserSearchResult.value = [];

  try {
    const rsp = await searchUserFromDeptAndChatByName({ keyWord: userName });
    const promises = rsp.data.map((item) => {
      if (!item.id) {
        console.warn('联系人缺少 id:', item);
        return Promise.resolve();
      }
      if (item.portrait) {
        return Promise.resolve();
      }
      if (item.type === 1) {
        item.portrait = generateTextAvatar(item.name || '');
        return Promise.resolve();
      } else if (item.type === 2) {
        try {
          const extra = JSON.parse(item.extra || '{}');
          const groupMemberList = extra.map((member) => ({
            name: member.alias || '',
            avatar: member.avatar || '',
          }));
          return generateGroupAvatar(groupMemberList).then((dataUrl) => {
            item.portrait = dataUrl;
          });
        } catch (error) {
          console.error('解析 extra 失败:', error);
          item.portrait = '';
          return Promise.resolve();
        }
      } else {
        item.portrait = '';
        return Promise.resolve();
      }
    });

    await Promise.all(promises);
    pickUserSearchResult.value = rsp.data.filter((item) => item.id);
  } catch (err) {
    console.error('获取用户信息失败:', err);
    ElMessage.error('获取用户信息失败');
  } finally {
    loading.value = false;
  }
};

// 选择联系人
const handlePickUserSelect = (contact) => {
  if (!contact || !contact.id) {
    console.warn('无效的联系人:', contact);
    return;
  }
  pickUserSelectedList.value = contact.id;
  // console.info("contact.type---->", contact.type)
  const targetUser = {
    id: contact.id,
    name: contact.name || '',
    type: contact.type,
  };
  selectArr.value = [targetUser]; // 单选
};

// 高亮搜索关键字
const highlightKeyword = (text, keyword) => {
  if (!text || !keyword) return text;
  const regex = new RegExp(`(${keyword})`, 'gi');
  return text.replace(regex, '<span class="text-blue-500">$1</span>');
};

// 确认选择并返回选中的用户
const handleConfirm = () => {
  console.info("选择的用户信息---->", selectArr.value)
  if (selectArr.value.length > 0) {
    dialogVisible.value = false;
    emit('confirm', selectArr.value);
    ElMessage.success('操作成功');
  } else {
    ElMessage.warning('请选择至少一个用户');
  }
};

defineExpose({
  open,
});
</script>

<style lang="less" scoped>
.container {
  max-height: 600px;
  min-height: 300px;
  display: flex;
  align-items: stretch;
  height: 100%;
}

.mb-4 {
  font-weight: bold;
}

.hide-scrollbar::-webkit-scrollbar {
  display: none;
}

.hide-scrollbar {
  scrollbar-width: none;
  -ms-overflow-style: none;
}

.dialog-footer {
  display: flex;
  justify-content: flex-start;
  margin-top: 15px;
  gap: 12px;
}
</style>