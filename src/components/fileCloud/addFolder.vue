<template>
  <Dialog :title="formType == 'create' ? '新建文件夹' : '重命名'" v-model="dialogVisible" width="450">
    <el-form ref="formRef" :model="formData" :rules="formRules">
      <el-form-item prop="name">
        <el-input v-model="formData.name" placeholder="请输入文件夹名称" />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="dialogVisible = false">取消</el-button>
      <el-button @click="submitForm" type="primary" :disabled="formLoading" :loading="formLoading">
        {{ formType == 'create' ? '创建' : '确定' }}
      </el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
import { fileCloudApi } from '@/api/fileCloud/list'

const { t } = useI18n()
const message = useMessage()

const dialogVisible = ref(false)
const formLoading = ref(false)
const formType = ref('')
const formData = ref({
  name: undefined,
})
const formRules = reactive({
  name: [{ required: true, message: '文件夹名称不能为空', trigger: 'blur' }],
})
const formRef = ref()

/** 打开弹窗 */
const rowObj = ref({
  parentId: undefined,
  id: undefined,
  type: undefined
})
const tenantId = ref()
const open = async (type: string, row: any, id) => {
  rowObj.value = row
  dialogVisible.value = true
  formType.value = type
  tenantId.value = id
  resetForm()
  if (type == 'update') {
    formData.value.name = row.name
  }
}

defineExpose({ open })
const emit = defineEmits(['success'])

const submitForm = async () => {
  await formRef.value.validate()
  formLoading.value = true
  try {
    if (formType.value === 'create') {
      await fileCloudApi.create({
        name: formData.value.name,
        parentId: rowObj.value.parentId,
        tenantId: tenantId.value
      })
      message.success(t('common.createSuccess'))
    } else {
      if (rowObj.value.type == 2) {
        await fileCloudApi.fileUpload({
          name: formData.value.name,
          parentId: rowObj.value.parentId,
          id: rowObj.value.id,
          tenantId: tenantId.value
        })
      } else {
        await fileCloudApi.update({
          name: formData.value.name,
          parentId: rowObj.value.parentId,
          id: rowObj.value.id,
          tenantId: tenantId.value
        })
      }
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    emit('success', formType.value)
  } finally {
    formLoading.value = false
  }
}

const resetForm = () => {
  formData.value = {
    name: undefined
  }
  formRef.value?.resetFields()
}
</script>