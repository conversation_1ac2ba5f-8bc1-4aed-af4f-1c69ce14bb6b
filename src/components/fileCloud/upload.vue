<template>
  <Dialog title="上传文件" v-model="dialogVisible" width="500">
    <el-upload action="" :auto-upload="false" multiple :file-list="fileList" :on-change="onChange"
      :on-remove="onRemove">
      <el-button size="default">点击上传</el-button>
      <template #tip>
        <span class="el-upload__tip"> 请选择附件</span>
      </template>
    </el-upload>
    <template #footer>
      <el-button @click="dialogVisible = false">取 消</el-button>
      <el-button type="primary" @click="submitForm" :loading="formLoading">确 定</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
import { fileCloudApi } from '@/api/fileCloud/list'
import * as messageApi from '@/api/message/index'
import * as FileApi from '@/api/infra/file'
const { t } = useI18n()
const message = useMessage()
const dialogVisible = ref(false)

const rowId = ref()
const tenantId = ref()
const open = async (id, tId) => {
  rowId.value = id
  tenantId.value = tId
  fileList.value = []
  dialogVisible.value = true
}
const fileList = ref([])
const endResult = ref([])
const formLoading = ref(false)
const onRemove = (file, list) => {
  fileList.value = list
}
const onChange = (file, list) => {
  fileList.value = list
}

const submitForm = async () => {
  // 校验表单
  if (fileList.value.length == 0) return message.warning('请选择文件')
  formLoading.value = true
  const uploadQueue = []
  fileList.value.forEach(file => {
    const formData = new FormData()
    formData.append('file', file.raw)
    formData.append('isImg', false)
    uploadQueue.push(messageApi.uploadFile(formData))

  })
  Promise.all(uploadQueue).then(async res => {
    endResult.value = []
    let isFault = false;
    res.forEach(item => {
      if (item.code === 0) {
        console.log(item)
        endResult.value.push(item.data)
      } else {
        isFault = true
      }
    })
    if (isFault) {
      ElMessage.error("附件上传失败，请重新上传")
      formLoading.value = false
      return
    }
    submitAll()
  }).catch(() => {
    ElMessage.error("附件上传失败，请重新上传")
    formLoading.value = false
  })

}
const arr = ref([
  {
    cloudId: undefined,
    name: undefined,
    path: undefined,
    tenantId: undefined,
  }
])
const submitAll = async () => {
  try {
    console.log(fileList.value, 'fileList')
    console.log(endResult.value, 'endResult')
    arr.value = []
    endResult.value.forEach((item) => {
      arr.value.push({
        cloudId: rowId.value,
        name: item.name,
        path: item.url,
        tenantId: tenantId.value
      })
    })
    // return
    fileCloudApi.permissionBatchFile(arr.value)
    message.success(t('common.createSuccess'))
    dialogVisible.value = false
    setTimeout(() => {
      emit('success')
    }, 1000)
  } finally {
    formLoading.value = false
  }
}


defineExpose({ open })
const emit = defineEmits(['success'])





</script>
<style scoped lang="less"></style>