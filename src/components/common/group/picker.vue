<template>
  <w-dialog
    :border="false"
    closeFree
    :width="width"
    @ok="selectOk"
    :title="title"
    v-model="visible"
    class="outsideBox"
  >
    <div class="picker" v-if="visible">
      <!-- 左 -->
      <div class="candidate" v-loading="loading">
        
        <div v-if="pickType !== 'role'">
          <el-input
            v-model="search"
            @input="searchUser"
            style="width: 100%"
            clearable
            placeholder="搜索"
            prefix-icon="el-icon-search"
            
          />
          <div v-show="!showUsers">
            <div style="height: 24px"></div>
            <!-- <ellipsis  hoverTip class="ell-box" :row="1" :content="deptStackStr" :canSelect="true">
            </ellipsis> -->
            <div style="display: flex;flex-wrap: wrap;">
              <div v-for="(item, index) in deptStack" class="center-align" :key="index">
                <div
                  @click="deptStackClick(item, index)"
                  style="cursor: pointer;line-height:26px;"
                  :style="{ color: index < deptStack.length - 1 ? '#A2A3A5' : '#303133' }"
                  >{{ item.name }}</div
                >
                <img
                  v-if="deptStack.length > 1 && index < deptStack.length - 1"
                  src="@/assets/image/arrow_right_gray.svg"
                  style="width: 13px; height: 26px; margin: 0 5px"
                />
              </div>
            </div>
            <div class="leftTop">
              <el-checkbox
                v-model="checkAllBox"
                @change="handleCheckAllChange"
                :disabled="!multiple"
                >全选</el-checkbox
              >
            </div>
          </div>
        </div>
        <div class="role-header" v-else>
          <div>系统角色</div>
        </div>
        <div class="org-items" :style="pickType === 'role' ? 'height: 350px' : ''">
          <el-empty :image-size="100" description="似乎没有数据" v-show="orgs.length === 0" />
          <div
            v-for="(org, index) in orgs"
            :key="index"
            :class="orgItemClass(org)"
            @click="selectChange(org)"
          >
            <el-checkbox
              v-model="org.selected"
              :disabled="disableNode(org)"
              @change="checkboxChange(org)"
            />
            <div v-if="org.type === 'dept'" style="display: flex; justify-content: space-between">
              <div class="center-align" style="">
                <img
                  style="width: 36px; height: 36px; margin-right: 8px"
                  src="@/assets/image/dept.svg"
                />

                <div>
                  <div style="font-size: 14px; color: #303133; line-height: 20px">{{
                    org.name
                  }}</div>
                  <div style="font-size: 11px; color: #a2a3a5; line-height: 16px">
                    {{ org.employNum }}人
                  </div>
                </div>
              </div>

              <span
                @click.stop="nextNode(org)"
                :class="`next-dept${org.selected ? '-disable' : ''}`"
              >
                下级
              </span>
              <!-- <icon name="el-icon-folderopened" />
              <span class="name">{{ org.name }}</span>
              <span
                @click.stop="nextNode(org)"
                :class="`next-dept${org.selected ? '-disable' : ''}`"
              >
                <icon name="iconfont icon-map-site" />下级
              </span> -->
            </div>
            <div v-else-if="org.type === 'user'">
              <avatar
                :size="30"
                :name="org.name"
                :status="org.isLeader ? 'leader' : ''"
                :src="org.avatar"
              />
            </div>
            <div style="display: inline-block" v-else>
              <icon name="iconfont icon-bumen" />
              <span class="name">{{ org.name }}</span>
            </div>
          </div>
        </div>
      </div>
      <!-- 右 -->
      <div class="selected">
        <div class="count">
          <span>已选择 ({{ selectpeopleCount }} <span style="color:#909399;">/ 1000</span>  ) </span>
          <span @click="clearSelected">清空</span>
        </div>
        <div
          class="org-items org-items-r" 
          :style="pickType == 'createGroup' ? 'height: 250px' : ''"
        >
          <el-empty
            :image-size="100"
            description="请点击左侧列表选择数据"
            v-show="select.length === 0"
          />
          <div style="width:100%;flex-wrap:wrap;" class="center-align">
            <div v-for="(org, index) in select" :key="index" class="orgItemCard center-align">
            <div v-if="org.type === 'dept'" class="center-align">
              <img
                  style="width: 24px; height: 24px;"
                  src="@/assets/image/dept.svg"
                />
               <div class="textover" style="margin-left:10px;max-width:80px;"> {{ org.name }}</div> 
            </div>
            <div v-else-if="org.type === 'user'" style="display: flex; align-items: center">
              <avatar :size="24" :name="org.name" :src="org.avatar" />
            </div>
            <img src="@/assets/image/close.svg" style="width:16px;height:16px;margin-left:4px;"  v-if="org.memberType != 2" @click="noSelected(index)"/>
            
          </div>
          </div>
        </div>

        <div v-if="pickType == 'createGroup'">
          <div style="height: 1px; border-top: 1px solid #dcdfe6"></div>

          <div class="center-align" style="margin-top: 15px; padding-left: 20px">
            <div style="font-size: 14px; color: #303133; line-height: 20px"> 群名称： </div>
            <el-input
              v-model="groupName"
              style="width: 280px"
              clearable
              placeholder="取个群名称方便后续搜索"
            />
          </div>

          <div class="center-align" style="margin-top: 15px; padding-left: 20px">
            <div style="font-size: 14px; color: #303133; line-height: 20px"> 群归属： </div>
            <el-input
              v-model="tenantName"
              disabled
              style="width: 280px"
              clearable
              placeholder="组织架构"
            />
          </div>
        </div>
      </div>
    </div>
  </w-dialog>
</template>

<script>
import { Popup, List, Cell, NavBar, Checkbox, Dialog, showFailToast } from 'vant'
import {
  getOrgTree,
  getUserByName,
  getDeptByName,
  getListByTenan,
  getDeptGroupParentList,
  getArchitectureGroupMembers
} from '@/api/org'
import { useUserStore } from '@/store/modules/user'
const userStore = useUserStore()
export default {
  name: 'OrgPicker',
  components: { Popup, List, Cell, NavBar, Checkbox },
  props: {
    title: {
      default: '请选择',
      type: String
    },
    pickType: {
      default: 'createGroup', //user-加人  createGroup-创建群组
      type: String
    },
    multiple: {
      //是否多选
      default: false,
      type: Boolean
    },
    selected: {
      default: () => {
        return []
      },
      type: Array
    },
    groupId: {
      default: null,
      type: String
    }
  },
  emits: ['ok', 'close'],
  data() {
    return {
      visible: false,
      loading: false,
      checkAllBox: false,
      nowDeptId: null,
      isIndeterminate: false,
      searchUsers: [],
      nodes: [],
      select: [],
      search: '',
      deptStack: [],
      popupStyle: {
        height: '100%',
        width: '100%',
        background: '#f7f7f9'
      },
      width: '45%',
      screenWidth: null,
      groupName: '',
      tenantName: '',
      // 当前群id
      groupId: null
    }
  },
  computed: {
    selectpeopleCount() {
      
      let count=0

      for(let i=0;i<this.select.length;i++){
        if(this.select[i].type=='user'){
          count+=1
        }
        if(this.select[i].type=='dept'){
          count+=  this.select[i].employNum
        }
      }




      return count
    },
    deptStackStr() {
      return String(this.deptStack.map((v) => v.name)).replaceAll(',', ' > ')
    },
    orgs() {
      return !this.search || this.search.trim() === '' ? this.nodes : this.searchUsers
    },
    showUsers() {
      console.log('触发showUsers变更')
      return this.search || this.search.trim() !== ''
    },
    userStore() {
      return userStore
    },
    checkAll() {
      // console.log("触发checkAll计算")
      for (let i = 0; i < this.nodes.length; i++) {
        if (!this.nodes[i].selected) {
          return false
        }
      }

      return true
    }
  },
  watch: {
    screenWidth: function (n, o) {
      if (n > 1600) {
        this.width = '45%'
      }
      if (n <= 1600) {
        this.width = '65%'
      }
      if (n <= 1200) {
        this.width = '75%'
      }
      if (n <= 700) {
        this.width = '95%'
      }
    },
    // 监听checkAll的变化，然后更新checkAllBox
    checkAll(newValue) {
      console.log('触发checkAll变更')
      this.checkAllBox = newValue
    }
  },
  methods: {
    async show(groupIdVal) {
      this.groupId = groupIdVal
      console.log('show groupIdVal=', groupIdVal)

      this.search = ''
      this.visible = true

      this.getResize()
      this.getTenantName()

      await this.init()
      await this.getOrgList()
    },
    getResize() {
      this.screenWidth = document.body.clientWidth
      window.onresize = () => {
        this.screenWidth = document.body.clientWidth
      }
    },
    orgItemClass(org) {
      return {
        'org-item': true,
        'org-dept-item': org.type === 'dept',
        'org-user-item': org.type === 'user',
        'org-role-item': org.type === 'role'
      }
    },
    disableNode(node) {
      if(node.type=='dept' && this.pickType=='remove') {
        return true
      }

      if(node.memberType == 2){
        return true
      }


      return false
    },
    async getOrgList(v) {
      this.loading = true
      let pickType = null

      if(this.pickType=='remove'){
        getListByTenan({
        groupId: this.groupId,
        keyWord: ''
      })
        .then((rsp) => {
          this.loading = false

          let nodes = []
          for (let i = 0; i < rsp.data.length; i++) {
            rsp.data[i].name = rsp.data[i].nickname
            rsp.data[i].type = 'user'
          }
          for (let i = 0; i < rsp.data.length; i++) {
            if (rsp.data[i].isInGroup == 1) {
              nodes.push(rsp.data[i])
            }
          }

          this.nodes = nodes

          this.selectToLeft()
        })
        .catch((err) => {
          this.loading = false
          this.$err(err, '获取数据失败')
        })
      }else{

        
        getArchitectureGroupMembers({
          groupId: this.groupId,
          keyWord: '',
          type: 'user',
          deptIds: this.nowDeptId
        })
          .then((rsp) => {
            // console.log('getArchitectureGroupMembers=', rsp)

            this.loading = false
            let nodes = []
            if (this.pickType == 'createGroup') {
              nodes = rsp.data
            } else if (this.pickType == 'add') {
              for (let i = 0; i < rsp.data.length; i++) {
                if (rsp.data[i].isInGroup != 1) {
                  nodes.push(rsp.data[i])
                }
              }
            } else if (this.pickType == 'remove') {
              for (let i = 0; i < rsp.data.length; i++) {
                if (rsp.data[i].type=='dept' || rsp.data[i].isInGroup == 1) {
                  nodes.push(rsp.data[i])
                }
              }
            }

            this.nodes = nodes

            this.selectToLeft()
          })
          .catch((err) => {
            this.$err(err, '获取数据失败')
          })

      }

    },
    getShortName(name) {
      if (name) {
        return name.length > 2 ? name.substring(1, 3) : name
      }
      return '**'
    },
    searchUser() {
      let userName = this.search.trim()
      this.searchUsers = []
      
      if(this.search.trim()){
        this.loading = true

        getListByTenan({
          groupId: this.groupId,
          keyWord: userName
        })
          .then((rsp) => {
            this.loading = false
            let searchUsers = rsp.data

            for (let i = 0; i < searchUsers.length; i++) {
              searchUsers[i].name = searchUsers[i].nickname
              searchUsers[i].type = 'user'
            }

            if(this.pickType=="remove"){
              let tempList=[]
              for (let i = 0; i < searchUsers.length; i++) {
                if(searchUsers[i].isInGroup==1){
                  tempList.push(searchUsers[i])
                }
              }

              searchUsers = tempList
            }

            // if (rsp.data[i].isInGroup == 1) {
            //     nodes.push(rsp.data[i])
            //   }
            
            this.searchUsers = searchUsers

            this.selectToLeft()
          })
          .catch((err) => {
            this.loading = false
            this.$err(err, '获取用户信息失败')
          })
      }else{
        this.selectToLeft()
      }

      
    },
    selectToLeft() {
      console.log("selectToLeft=")
      let nodes = this.search.trim() === '' ? this.nodes : this.searchUsers
      nodes.forEach((node) => {
        for (let i = 0; i < this.select.length; i++) {
          if (Number(this.select[i].id) === Number(node.id)) {
            node.selected = true
            break
          } else {
            node.selected = false
          }
        }
      })
    },
    checkboxChange(node) {
      // console.log(node,'checkboxChange:node')
      node.selected = !node.selected
    },
    selectChange(node) {
      // console.log(node, 'node')
      // 如果是群主，则禁止操作
      if (node.memberType == 2) {
        return
      }
      console.log(node, 'node')
      if (node.selected) {
        for (let i = 0; i < this.select.length; i++) {
          if (Number(this.select[i].id) === Number(node.id)) {
            this.select.splice(i, 1)
            console.log(this.select)
            break
          }
        }
        node.selected = false
      } else if (!this.disableNode(node)) {
        node.selected = true
        let nodes = this.search.trim() === '' ? this.nodes : this.searchUsers
        if (!this.multiple) {
          nodes.forEach((nd) => {
            if (node.id !== nd.id) {
              nd.selected = false
            }
          })
        }
        if (node.type === 'dept') {
          if (!this.multiple) {
            this.select = [node]
          } else {
            this.select.unshift(node)
          }
        } else {
          if (!this.multiple) {
            this.select = [node]
          } else {
            this.select.push(node)
          }
        }
      }
    },
    noSelected(index) {
      let nodes = this.nodes
      for (let f = 0; f < 2; f++) {
        for (let i = 0; i < nodes.length; i++) {
          if (Number(nodes[i].id) === Number(this.select[index].id)) {
            nodes[i].selected = false
            // this.checkAll = false
            break
          }
        }
        nodes = this.searchUsers
      }
      this.select.splice(index, 1)
    },
    handleCheckAllChange() {
      if (this.checkAllBox) {
        this.nodes.forEach((node) => {
          if (!node.selected && !this.disableNode(node)) {
            node.selected = true
            this.select.push(node)
          }
        })
      } else {
        this.recover()
      }
    },
    async nextNode(node) {
      if (!node.selected) {
        this.nowDeptId = node.id
        this.deptStack.push(node)
        await this.getOrgList()
      }
    },

    recover() {

      this.nodes.forEach((node) => {
        if (node.memberType != 2) {
          node.selected = false

          
        }
      })

      let newSelect=[]

      for (let i = 0; i < this.select.length; i++) {
            if (this.select[i].memberType === 2) {
              newSelect.push(this.select[i])
            }
          }
          this.select=newSelect

      // this.nodes.forEach((node) => {
      //   if (node.memberType != 2) {
      //     node.selected = false

      //     for (let i = 0; i < this.select.length; i++) {
      //       if (this.select[i].id === node.id) {
      //         this.select.splice(i, 1)
      //         break
      //       }
      //     }
      //   }
      // })
    },
    selectOk() {
      if (this.select && this.select.length === 0) {
        this.$message.warning('选择的项为空')
        return
      }

      this.$emit('ok', Object.assign([], this.select), this.groupName)
      //console.log(Object.assign([], this.select))
      this.visible = false
      this.recover()
    },
    clearSelected() {
      this.$confirm('您确定要清空已选中的项?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          // this.checkAll = false
          this.recover()
        })
    },
    close() {
      this.$emit('close')
      this.visible = false
      this.recover()
    },
    async init() {
      // this.checkAll = false

      let topInfo = await getDeptGroupParentList({ id: 0 })

      console.log('topInfo=', topInfo)
      topInfo.data.deptRespVOList[0].id = 0

      let deptRespVO = topInfo.data.deptRespVOList[0]
      this.nowDeptId = deptRespVO.id
      this.nodes = []
      this.deptStack = [deptRespVO]
      this.select = Object.assign([], this.selected)

      if (this.pickType == 'createGroup') {
        // console.log('触发 show',this.userStore.user)
        this.select = [
          {
            id: this.userStore.user.id+"",
            avatar: this.userStore.user.avatar,
            username: '',
            nickname: this.userStore.user.nickname,
            isInGroup: 0,
            memberType: 2,
            name: this.userStore.user.nickname,
            type: 'user',
            selected: true
          }
        ]
      }

      console.log("this.userStore.user=",this.userStore.user)
      this.selectToLeft()
    },
    getTenantName() {
      this.tenantName = JSON.parse(localStorage.getItem('currentList'))[0].name
    },
    async deptStackClick(val, index) {
      if (index == this.deptStack.length - 1) {
        return
      }

      console.log('')

      this.nowDeptId = this.deptStack[index].id
      this.deptStack = this.deptStack.splice(0, index + 1)
      await this.getOrgList()
    }
  }
}
</script>

<style lang="less" scoped>
.picker {
  position: relative;
  display: flex;
  color: #303133;

  .role-header {
    padding: 10px !important;
    margin-bottom: 5px;
    // border-bottom: 1px solid #e8e8e8;
  }

  .leftTop {
    margin-top: 25px;
    display: flex;
    justify-content: left;
    align-items: center;
  }

  .ell-box {
    padding: 10px 0 5px;
    display: flex;
    align-items: center;
    justify-content: left;

    .el-icon {
      margin-top: 1px;
      margin-right: 6px;
      // font-size:16px
    }
  }

  .candidate {
    width: 50%;
    border-right: 1px solid #DCDFE6;

    .top-dept {
      margin-left: 20px;
      cursor: pointer;
      color: #1989fa;
    }

    .next-dept {
      float: right;
      color: @theme-primary;
      cursor: pointer;

      .iconfont {
        font-size: 14px;
        margin-right: 2px;
      }
    }

    .next-dept-disable {
      float: right;
      color: #b9b9b9;
      cursor: not-allowed;
    }

    & > div:first-child {
      padding: 10px 10px 5px;
    }
  }

  .selected {
    flex: 1;
    box-sizing: border-box;
    height: 100%;
    // border: 1px solid #e8e8e8;
    border-left: 0;

    .count {
      padding: 10px;
      /* display: inline-block; */
      // border-bottom: 1px solid #e8e8e8;
      /* margin-bottom: 5px; */
      /* width: 100%; */
      box-sizing: border-box;

      & > span:nth-child(2) {
        float: right;
        color: #c75450;
        cursor: pointer;
      }
    }
  }

  .org-items {
    overflow-y: auto;
    padding-bottom: 5px;
    height: 405px;

    .el-icon-close {
      position: absolute;
      right: 5px;
      cursor: pointer;
      font-size: larger;
    }

    .org-dept-item {
      padding: 5px;

      // height:32px;
      & > div {
        display: flex;
        justify-content: left;
        align-items: center;

        .el-icon {
          font-size: 16px;
        }

        & > span:last-child {
          position: absolute;
          right: 5px;
        }
      }
    }

    .org-role-item {
      display: flex;
      align-items: center;
      padding: 10px 5px;
    }

    .org-user-item {
      display: flex;
      align-items: center;
      padding: 5px;
      // height:32px
    }

    :deep(.org-item) {
      margin: 0 5px;
      border-radius: 5px;
      position: relative;
      display: flex;
      align-items: center;
      padding: 5px;

      // height:32px;
      .el-checkbox {
        color: #303133;
        margin-right: 10px;
      }

      .a-img span {
        font-size: 12px;
        display: block;
      }

      .name {
        margin-left: 5px;
        font-size: 14px;
        // white-space: nowrap; /* 禁止自动换行 */
        // overflow: hidden; /* 隐藏超出部分 */
        // text-overflow: ellipsis; /* 超出部分显示省略号 */
        // width: 238px;
        padding-right: 50px;
      }

      &:hover {
        background: #f1f1f1;
      }
    }
  }

  .org-items-r {
    height: 450px;
    padding-top: 5px;
  }
}

.disabled {
  cursor: not-allowed !important;
  color: #b9b9b9 !important;
}

:deep(.el-checkbox__input.is-disabled .el-checkbox__inner) {
  background: #efefef;
}

.mobile-picker {
  :deep(.el-input) {
    border-radius: 10px;

    :deep(.icon) {
      font-size: 1.2rem;
    }

    .el-input__inner {
      border: none;
      border-radius: 5px;
      font-size: 1rem;
    }
  }

  .m-org-item-tab {
    margin-bottom: 10px;
    color: @theme-primary;
  }

  .m-org-item {
    color: #303133;
    font-size: 1.1rem !important;
    padding: 15px 15px !important;
    border-bottom: 1px solid @theme-aside-bgc;

    .m-org-item-next {
      cursor: pointer;
      color: @theme-primary;
      font-size: 1.1rem;
    }

    .m-org-item-next-disabled {
      cursor: not-allowed;
      color: #b9b9b9;
    }

    :deep(.icon) {
      font-size: 1.1rem;
    }

    .to-top {
      cursor: pointer;
      color: @theme-primary;
    }
  }
}

::-webkit-scrollbar {
  float: right;
  width: 4px;
  height: 4px;
  background-color: white;
}

::-webkit-scrollbar-thumb {
  border-radius: 16px;
  background-color: #efefef;
}



.orgItemCard{
  background: #F3F4F7;
  border-radius: 4px;
  padding: 0 8px 0 4px;
  height:32px;
  margin-left:8px;
  font-weight: 400;
  font-size: 12px;
  color: #303133;
  margin-bottom: 12px;

  
  
}
</style>
<style>
.outsideBox .el-dialog__body {
  padding: 4px 9px!important;
}

.outsideBox .el-dialog__header{
  border-bottom: 0px!important;
  padding: 4px 9px!important;
}

.outsideBox .el-dialog__footer{
  border-top: 0px!important;
}
</style>
