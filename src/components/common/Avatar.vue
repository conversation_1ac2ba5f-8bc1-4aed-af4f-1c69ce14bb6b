<template>
  <div :class="{ avatar: true, 'show-y': showY }">
    <div class="a-img">
      <template v-if="type === 'user'">
        <el-avatar v-if="src" :size="size" :src="src" shape="square" />
        <div v-else :style="{ '--bgc': bgc, '--size': size + 'px' }">
          <span>{{ alisa }}</span>
        </div>
      </template>
      <el-avatar v-else-if="type === 'dept'" style="background: #f78f5f" icon="el-icon-histogram" :size="size"
        shape="square" />
      <el-avatar v-else icon="el-icon-userfilled" style="background: #576a95" :size="size" shape="square" />
      <icon name="el-icon-close close" v-if="closeable" @click="$emit('close')" />
      <icon :name="`${statusIcon} status`" v-show="status && status != 'cc' && status != 'transfer'" />
      <img src="@/assets/image/cc.svg" v-show="status == 'cc'" class="status-img-cc">
      <img src="@/assets/image/transfer.svg" v-show="status == 'transfer'" class="status-img">
    </div>
    <div class="name" v-if="!showY && showName">{{ name }}</div>
    <ellipsis hoverTip class="name" v-show="showName" :style="{ width: size + 10 + 'px' }" :content="name" v-else />
  </div>
</template>

<script>
export default {
  name: 'Avatar',
  install(Vue) {
    window.$vueApp.component('avatar', this)
  },
  components: {},
  props: {
    type: {
      type: String,
      default: 'user',
    },
    name: {
      type: String,
      default: '',
    },
    size: {
      type: Number,
      default: 36,
    },
    src: {
      type: String,
      default: undefined,
    },
    bgc: {
      type: String,
      default: '#1989fa',
    },
    square: {
      type: Boolean,
      default: false,
    },
    showY: {
      type: Boolean,
      default: false,
    },
    showName: {
      type: Boolean,
      default: true,
    },
    closeable: {
      type: Boolean,
      default: false,
    },
    status: {
      type: String,
    },
  },
  emits: ['close'],
  data() {
    return {}
  },
  computed: {
    alisa() {
      return (this.name && this.name.length > 2)
        ? this.name.substring(this.name.length - 2, this.name.length)
        : this.name
    },
    statusIcon() {
      switch (this.status) {
        case 'error':
          return 'el-icon-circleclosefilled error'
        case 'pending':
          return 'pending iconfont icon-dengdaizhongbeifen'
        case 'success':
          return 'el-icon-successfilled success'
        case 'cc':
          return 'el-icon-promotion cc'
        case 'comment':
          return 'comment iconfont icon-xiaoxi'
        case 'transfer':
          return 'transfer iconfont icon-zhuanyi'
        case 'cancel':
          return 'cancel iconfont icon-fanhui-chehui-06'
        case 'recall':
          return 'recall iconfont icon-quxiao'
        case 'leader':
          return 'el-icon-userfilled leader'
        default:
          return null
      }
    },
  },

  methods: {},

}
</script>

<style lang="less" scoped>
.show-y {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column !important;

  .name {
    margin-left: 0 !important;
  }
}

.close {
  position: absolute;
  top: 0;
  right: 0;
  color: white;
  cursor: pointer;
  border-radius: 50%;
  background: black;
}

.avatar {
  display: flex;
  flex-direction: row;
  position: relative;
  align-items: center;
  // overflow: hidden;

  .a-img {
    display: flex;
    border-radius: 6px;
    flex-direction: column;
    justify-content: center;
    background: white;
    position: relative;

    &>div {
      display: inline-block;
      text-align: center;
      background: var(--bgc);
      color: white;
      border-radius: 6px;
      width: var(--size);
      height: var(--size);
      line-height: var(--size);
      font-size: 14px;
      overflow: hidden;
    }
  }

  :deep(.el-avatar) {
    background: transparent;
    // border: 1px solid rgb(25 137 250 / 10%)
  }

  .name {
    text-align: center;
    color: #19191a;
    font-size: 14px;
    margin-left: 10px;
    font-family: none;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}

.status {
  position: absolute;
  bottom: -4px;
  right: -8px;
  border-radius: 50%;
  font-size: 15px;
  background: white;
  border: 2px solid white;
}

.status-img-cc {
  position: absolute;
  bottom: -4px;
  right: -8px;
  border-radius: 50%;
  font-size: 15px;
  background: white;
  border: 2px solid white;
  width: 15px;
  height: 15px;
}

.status-img {
  position: absolute;
  bottom: -4px;
  right: -8px;
  border-radius: 50%;
  width: 19px;
  height: 19px;
}

.error {
  color: @theme-danger !important;
  ;
}

.leader {
  font-size: 12px;
  color: @theme-warning !important;
  ;
}

.pending {
  color: @theme-warning !important;
}

.success {
  color: @theme-success !important;
  ;
}

.transfer {
  color: white;
  background: #7a939d;
  font-size: 12px;
  padding: 1px;
}

.comment {
  color: @theme-primary !important;
  ;
}

.cc {
  color: white;
  font-size: 12px;
  padding: 1px;
  background: @theme-primary !important;
  ;
}

.cancel {
  color: #838383 !important;
  ;
}

.recall {
  color: #f56c6c !important;
  ;
}
</style>
