<template>
  <div>
    <el-row
      class="rowBox relative"
      :class="{ isCol: isCollapse, 'isCBox-else': windowWidth < 768 }"
      v-loading="loading"
    >
      <el-col :span="4" :xl="4" :lg="6" :md="6" :sm="6" :xs="8" class="left-col">
        <div class="center-align" style="justify-content: space-between">
          <div> 
          </div>
          <div>
            <el-button
              style="justify-content: space-between"
              type="text"
              @click="handleAdd('create')"
              >新建分类</el-button
            >
          </div>
        </div>
        <el-input v-model="search" @input="handleInput" placeholder="搜索分类" clearable :prefix-icon="Search"></el-input>

        <el-tree
          class="temp-tree"
          ref="leftTreeRef"
          :data="leftTree"
          :props="{ children: 'children', label: 'name' }"
          :check-strictly="true"
          node-key="id"
          placeholder="请选择"
          style="margin-top: 10px"
          :highlight-current="true"
          @node-click="handleTreeClick"
        >
          <template #default="{ node, data }">
            <div class="center-align" style="justify-content: space-between; flex: 1">
              <div class="center-align">
                <img v-if="node.level == 1" style="width: 18px;height: 18px;margin-right: 8px;" src="@/assets/image/class1.svg" />
                <img v-if="node.level == 2" style="width: 18px;height: 18px;margin-right: 8px;" src="@/assets/image/class1.svg" />
                {{ node.label }}
                
                </div>
              <el-dropdown v-if="data.perm == 1 || data.perm == 2"
                @command="
                  (command) => {
                    handleTreeCommand(command, node, data)
                  }
                "
              >
                <img style="16px;height:16px;" src="@/assets/image/3dot.svg" />

                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item command="create" v-if="node.level == 1 && data.perm == 1">创建子分类</el-dropdown-item>
                    <el-dropdown-item command="rename" v-if="data.perm == 1 || data.perm == 2">修改分类</el-dropdown-item>
                    <el-dropdown-item command="delete" v-if="data.perm == 1">删除分类</el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </div>
          </template>
        </el-tree>
      </el-col>
      <el-col :span="20" :xl="20" :lg="18" :md="18" :sm="18" :xs="16" class="rightCol">
        <!-- top1 -->
        <div class="rightTop">
          <p></p>
          <div>
            <el-button :icon="Refresh" @click="getList">刷新</el-button>
            <!-- <el-button :icon="Plus" @click="handleAdd('create')" v-if="queryParams.classId == 0"
              >新建文件夹</el-button
            > -->
            <el-button
              type="primary"
              :icon="Upload"
              @click="handleUpload"
              v-if="queryParams.classId != 0 && (permissionNum == 1 || permissionNum == 2)"
              >上传</el-button
            >
          </div>
        </div>
        <!-- top2 -->
        <div class="crumbDiv">
          <el-breadcrumb
            :separator-icon="ArrowRight"
            class="crumb-left"
            :class="{ onlyOne: crumbsList.length == 1 }"
          >
            <el-breadcrumb-item v-for="(item, index) in crumbsList" :key="index">
              <!-- <img src="@/assets/image/fileCloud6-1.svg" v-if="index != 0 && item.parentId == 0" />
              <img src="@/assets/image/fileCloud7-1.svg" v-if="index != 0 && item.parentId != 0" /> -->
              <span :title="item.name" @click="breadcrumbClick(item, index)">
                {{ item.name }}
              </span>
            </el-breadcrumb-item>
          </el-breadcrumb>
          <div class="flex">
            
            <div class="crumb-right" v-if="queryParams.classId != 0 && permissionNum == 1">
              <div
                v-for="(item, index) in permissionUser"
                :key="index"
                class="user" style="margin-left: 5px;"
                v-show="index < 2"
                :class="{ secondPosition: index == '1' }"
                @click="handlePermission(queryParams.classId)"
              >
                <p v-if="permissionUser.length > 2 && index == '1'" class="absoluteP">{{
                  permissionUser.length
                }}</p>
                <avatar
                  :name="item.userName"
                  :src="''"
                  :size="24"
                  
                ></avatar>
              </div>
              <div class="add" @click="handlePermission(queryParams.classId)" v-if="permissionNum == 1" >+</div>
            </div>
            <!-- <div class="layout">
              <img v-for="(item, index) in layoutList" :key="index" :src="item.url"
                :class="{ 'activeLayout': index == layoutActive }" @click.stop="handleLayout(index)" />
            </div> -->
          </div>
        </div>
        <!-- content -->
        <div>
          <div>
            <el-row class="tableTop tableFlex" type="flex">
              <p>名称</p>
              <p>文件名</p>
              <p>描述</p>
              <p>发布时间</p>
              <p>备注</p>
              <p></p>
            </el-row>
            <div class="tableBody">
              <p v-if="tableData.length == 0" class="alignP">暂无数据</p>
              <div
                v-for="(item, index) in tableData"
                :key="index"
                class="tableFlex tableBottom"
                @click="handleDetail(item)"
              >
                <p>
                  <!-- <img src="@/assets/image/fileCloud6-1.svg" v-if="queryParams.classId == 0" />
                  <img
                    src="@/assets/image/fileCloud7-1.svg"
                    v-if="queryParams.classId != 0 && item.type == 1"
                  /> -->
                  {{ item.name }}
                </p>
                <p>{{ item.fileName }}</p>
                <p>{{ item.content }}</p>
                <p>{{ item.releaseTime }}</p>
                <p>
                  {{ item.remark }}
                </p>
                <p class="configP center-view" @click.stop>
                  <el-dropdown v-if="permissionNum == 1 || permissionNum == 2"
                    @command="(command) => handleCommand(command, item)"
                    trigger="contextmenu"
                    :ref="setItemRef(index)"
                  >
                    <img
                      :src="configImg2"
                      @mouseover="item.showSecondImage = true"
                      @mouseleave="item.showSecondImage = false"
                      @click.stop="handleClick(item, index)"
                    />
                    <template #dropdown>
                      <el-dropdown-menu>
                        <!-- <el-dropdown-item command="permission" :icon="Lock"
                          >权限管理</el-dropdown-item
                        > -->
                        <el-dropdown-item command="edit" v-if="permissionNum == 1 || permissionNum == 2" :icon="Edit">编辑</el-dropdown-item>
                        <el-dropdown-item command="rename" v-if="permissionNum == 1 || permissionNum == 2" :icon="Edit">重命名</el-dropdown-item>
                        <el-dropdown-item command="delete" v-if="permissionNum == 1" :icon="Delete">删除</el-dropdown-item>
                      </el-dropdown-menu>
                    </template>
                  </el-dropdown>
                </p>
              </div>
            </div>
          </div>
        </div>
      </el-col>
    </el-row>
  </div>
  <!-- 新建/修改文件夹名称 -->
  <addFolder ref="addFolderRef" @success="folderSuccess"></addFolder>
  <!-- 权限配置 -->
  <permissionConfig ref="permissionRef"></permissionConfig>
  <upload ref="uploadRef" @success="uploadSuccess"></upload>
  <!-- 预览附件 -->
  <readFile ref="readFileRef" />
</template>
<script type="ts" setup>
defineOptions({ name: 'fileCloud' })
import { ElMessage } from 'element-plus'
import {
  Search,
  Plus,
  Upload,
  ArrowRight,
  Lock,
  Edit,
  Delete,
  Refresh
} from '@element-plus/icons-vue'
import { fileCloudApi } from '@/api/fileCloud/list'
import { rulelibApi } from '@/api/rulelib/list'
import { defaultProps, handleTree } from '@/utils/tree'

import { useAppStore } from '@/store/modules/app'

import layout1 from '@/assets/image/fileCloudLayout1.png'
import layout2 from '@/assets/image/fileCloudLayout2.png'
import configImg1 from '@/assets/image/fileCloudConfig1.png'
import configImg2 from '@/assets/image/fileCloudConfig2.png'
import addFolder from '@/components/rulelib/addFolder.vue'
import permissionConfig from '@/components/rulelib/permission.vue'

import upload from '@/components/rulelib/upload.vue'
import readFile from '@/views/system/notify/list/readFile.vue'

const message = useMessage()
const { t } = useI18n()

const appStore = useAppStore()

const collapse = computed(() => appStore.getCollapse)
const isCollapse = computed(() => appStore.getCollapse)
watch(
  () => collapse.value,
  (newPath, oldPath) => {
    isCollapse.value = newPath
  }
)
// 监听浏览器宽度
const windowWidth = ref(window.innerWidth)
const handleResize = () => {
  windowWidth.value = window.innerWidth
}

const leftTree = ref([]) // 树形结构

// 以下是文库代码

// 搜索相关  

const search = ref('');
const className = ref('');

function handleInput(event) {
  console.log('输入框内容变化：', event);
  search.value = event;
  className.value = event;

  getRuleLibTree()
}

const queryParams = reactive({
  pageNo: 1,
  pageSize: 100,
  classId: 0
})

watch(
  () => queryParams.classId,
  (newVal, oldVal) => {
    if(newVal){
      // console.log('queryParams.classId  变更后=', queryParams.classId)

      myPermission()

    }
  }
)

const currentClassName =ref('')


// 获取左侧树列表
const getRuleLibTree = async () => {
  const res = await rulelibApi.getRuleLibClassTree({
    className:className.value
  })
  if (res) {
    // console.log('getRuleLibClassTree res=', res)

    leftTree.value = res
    // ruleList
  }
}







const addFolderRef = ref()
const handleAdd = () => {
  addFolderRef.value.open('create', { parentId: 0 })
}

const handleTreeCommand = async (type, node, data) => {
  // console.log('handleTreeCommand type=', type, 'node=', node, 'data=', data)
 


  switch (type) {
    case 'create':
      addFolderRef.value.open('create', { parentId: data.id })
      break
    case 'rename':
      addFolderRef.value.open('rename', data)
      break
    case 'delete':
      await message.delConfirm('确定删除该分类吗?删除后不可恢复。(分类中有数据时无法进行删除!)')
      await rulelibApi.delTreeItem(data.id)
      message.success(t('common.delSuccess'))
      await getList()
      await getRuleLibTree()
      break
    default:
      break
  }
}

const handleTreeClick = async (data, node) => {
  if (!node.expanded) {
    node.expanded = true
  }
  // console.log('handleTreeClick data=', data)
  // console.log('queryParams.classId=', queryParams.classId)
  // console.log('data.id=', data.id)

  if (queryParams.classId == data.id) return

  queryParams.classId = data.id
  currentClassName.value = data.name

  if(node.level == 1){
    crumbsList.value=[]
    crumbsList.value.push({
      name: data.name,
      parentId: data.parentId,
      id: data.id,
      classId: data.id,
    })

  }else if(node.level == 2){
    crumbsList.value=[]
    crumbsList.value.push({
      name: node.parent.data.name,
      parentId: node.parent.data.parentId,
      id: node.parent.data.id,
      classId: node.parent.data.id,
    })
    crumbsList.value.push({
      name: data.name,
      parentId: data.parentId,
      id: data.id,
      classId: data.id,
    })

  }

  
  getList()
}

// 面包屑列表
const crumbsList = ref([])
// 点击面包屑导航
const breadcrumbClick = (item, index) => {
  // console.log('breadcrumbClick item=', item)
  if (queryParams.classId == item.classId) return
  queryParams.classId = item.classId
  currentClassName.value = item.name
  crumbsList.value.splice(index + 1)
  getList()
}
// 权限列表人员
const permissionUser = ref([])
// 权限修改
const handlePermission = async (item) => {
  try {

    
    permissionRef.value.open(item)
  } finally {
  }
}
// 布局
const layoutActive = ref(1)
const layoutList = ref([{ url: layout1 }, { url: layout2 }])
// 布局change
const handleLayout = (index) => {
  layoutActive.value = index
}

const tableData = ref([])
const loading = ref(false)
const getList = async () => {
  loading.value = true
  try {
    // const apiCall = queryParams.classId != 0 ? rulelibApi.cloudList : rulelibApi.rootPage
    const res = await rulelibApi.getRuleLibFilePage(queryParams)
    // console.log('getRuleLibFilePage res=', res)
    tableData.value = res.list
  } finally {
    loading.value = false
  }
}
const handleSelectionChange = (row) => {}
// 时间转换
const formatDate = (timestamp) => {
  // const isoTimestamp = timestamp.replace(' ', 'T')
  const date = new Date(timestamp)
  const today = new Date()

  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')

  const hours = String(date.getHours()).padStart(2, '0')
  const minutes = String(date.getMinutes()).padStart(2, '0')

  if (
    date.getFullYear() === today.getFullYear() &&
    date.getMonth() === today.getMonth() &&
    date.getDate() === today.getDate()
  ) {
    return `${hours}:${minutes}`
  } else {
    return `${year}-${month}-${day}`
  }
}

const permissionRef = ref()
const folderSuccess = (type) => {
  getRuleLibTree()
  getList()
}
const uploadRef = ref()
const handleUpload = () => {
  uploadRef.value.open(queryParams.classId,null,'edit',currentClassName.value)
}
const uploadSuccess = () => {
  getList()
}
// 点详情
const readFileRef = ref()
const handleDetail = async (item) => {
  ElMessage.closeAll()
  // loading.value = true
  try {
    
    fileOpen(null, item)
  } finally {
    loading.value = false
  }
}
const fileOpen = (res, item) => {
  // let isDown = res && res.permission ? true : false
  let isDown = true
  if (isDown) {
    let obj = {
      name: item.fileName,
      url: item.perviewPath,
      downloadUrl: item.path
    }
    readFileRef.value.open(obj, isDown, '1')
  } else {
    message.warning('暂无权限')
  }
}


// 更多下拉
const handleCommand = (i, item) => {
  switch (i) {
    // case 'permission':
    //   handlePermission(item)
    //   break
    case 'edit':
      handleEdit(item, i)
      break
    case 'rename':
      handleEdit(item, i)
      break
    case 'delete':
      handleDelete(item)
      break
    default:
      break
  }
}
// 重命名
const handleEdit = async (item, type) => {
  // console.log('handleEdit item=', item)
  uploadRef.value.open(queryParams.classId, item, type,item.name)
}
// 删除
const handleDelete = async (item) => {
  try {
    await message.delConfirm()
    await rulelibApi.ruleLibFileDel(item.id)
    message.success(t('common.delSuccess'))
    await getList()
  } catch {}
}

const permissionNum = ref(-1) //是否为最高权限：1管理
const myPermission = async () => {
  permissionUser.value = []
  if (queryParams.classId == 0) return
  try {
    const res = await fetchPermissions()
    // console.log('getMyPermission res=', res)
    if(res){
      permissionNum.value = res.perm
    }else{
      permissionNum.value = -1
    }

    getPermissionList()
    
  } finally {
  }
}

const getPermissionList = async () => {
  try {
    const res = await rulelibApi.getRuleLibPermPage({
      classId: queryParams.classId,
    })
    permissionUser.value = res.list
    // console.log('getRuleLibPermPage res=', res)
  } finally {
  }
}


const fetchPermissions = async (item) => {
  let obj = {
    classId: queryParams.classId
    
  }
  const res = await rulelibApi.getMyPermission(obj)
  return res
}



const itemRefs = ref([])
const setItemRef = (index) => {
  return (el) => {
    itemRefs.value[index] = el
  }
}
const handleClick = async (item, index) => {
  ElMessage.closeAll()
  // loading.value = true
  try {
   
    
    if (itemRefs.value[index]) {
      itemRefs.value[index].handleOpen()
    }
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  window.addEventListener('resize', handleResize)
  getRuleLibTree()
  // getList()
  // myPermission()
})
onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
})




</script>




<style lang="less" scoped>
p {
  padding: 0;
  margin: 0;
}

.rowBox {
  margin: 0 0 0 20px;
  position: fixed;
  left: 180px;
  right: 0;
  top: 81px;
  bottom: 0;
  font-size: 14px;
  background: #fff;
  color: #303133;
}

.isCol {
  left: 64px;
  margin-left: 0 !important;
}

.isCBox-else {
  left: 0;
}

.el-col {
  height: 100%;
}

.left-col {
  padding: 12px !important;
  border-right: 1px solid #eceded;
  box-sizing: border-box;
}

.rightCol {
  padding: 18px 32px;
}

.left-col .title {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 10px;
}

.leftUl {
  margin-top: 16px;
}

.leftUl li {
  padding: 12px;
  display: flex;
  align-items: center;
  margin-bottom: 6px;
  border-radius: 4px;
  cursor: pointer;

  img {
    margin-right: 4px;
  }
}

.rightTop {
  display: flex;
  justify-content: space-between;
  align-items: center;

  p {
    font-weight: bold;
    font-size: 16px;
  }

  .el-button--primary {
    background: #3370ff;
  }
}

.crumbDiv {
  margin: 24px 0;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.crumb-left {
  display: flex;
  align-items: center;

  :deep(.el-breadcrumb__inner) {
    display: flex;
    align-items: center;
    cursor: pointer;
    color: #a2a3a5;

    img {
      margin-right: 8px;
      width: 28px;
    }
  }

  :deep(.el-breadcrumb__inner:hover) {
    color: #54a7ee;
  }

  :deep(.el-breadcrumb__item:nth-last-child(1) .el-breadcrumb__inner) {
    color: #303133;
    cursor: inherit;
  }
}

.onlyOne {
  font-size: 16px;

  :deep(.el-breadcrumb__inner) {
    font-weight: bold;
  }
}

.flex {
  align-items: center;
}

.crumb-right {
  display: flex;
  cursor: pointer;

  .user {
    :deep(.avatar .a-img > div) {
      border-radius: 50%;
      font-size: 9px;
      background: #3370ff;
    }

    :deep(.avatar .name) {
      display: none;
    }
  }

  .secondPosition {
    position: relative;
  }

  .absoluteP {
    position: absolute;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    z-index: 1;
    background: rgba(0, 0, 0, 0.4);
    border-radius: 50%;
    color: #fff;
    display: flex;
    justify-content: center;
    align-items: center;
    pointer-events: none;
  }

  .add {
    margin-left: 6px;
    color: #fff;
    width: 24px;
    height: 24px;
    background: #3370ff;
    border-radius: 50%;
    font-size: 22px;
    display: flex;
    justify-content: center;
    align-items: center;
    box-sizing: border-box;
  }
}

.layout {
  margin-right: 4px;
  margin-left: 12px;
  position: relative;
  padding-left: 12px;

  img {
    padding: 4px;
    cursor: pointer;
    margin-left: 4px;
  }

  .activeLayout {
    background: #f3f4f7;
    border-radius: 4px;
  }
}

.layout::before {
  content: ' ';
  position: absolute;
  left: 0;
  top: 6px;
  background: #c1c1c1;
  width: 1px;
  height: 12px;
}

.tableFlex {
  display: flex;
  align-items: center;

  p {
    line-height: 20px;
  }

  p:nth-child(1) {
    width: 20%;
    display: flex;
    align-items: center;

    img {
      margin-right: 16px;
      width: 32px;
    }
  }

  p:nth-child(2) {
    width: 20%;
  }

  p:nth-child(3) {
    width: 15%;
  }

  p:nth-child(4) {
    width: 18%;
  }

  p:nth-child(5) {
    width: 19%;
  }

  p:nth-child(6) {
    width: 8%;
  }
}

.tableTop {
  padding-bottom: 12px;
  margin-bottom: 12px;
  border-bottom: 1px solid #eceded;
  font-size: 12px;
  color: #606266;
  font-weight: bold;
}

.tableBottom {
  padding: 16px 0;
  font-size: 14px;
  color: #606266;
  position: relative;
  cursor: pointer;
  box-sizing: border-box;

  p:nth-child(1) {
    color: #303133;
  }
}

.tableBottom:hover::before {
  cursor: pointer;
  background: #f3f4f7;
  border-radius: 4px;
  content: ' ';
  position: absolute;
  left: -10px;
  right: 0;
  top: 0;
  height: 100%;
  z-index: -1;
}

.configP {
  // opacity: 0;
  position: relative;

  .imgTip {
    // position: absolute;
  }
}

.tableBottom:hover .configP {
  // opacity: 1;
}

:deep(.el-tooltip__trigger:focus-visible) {
  outline: unset;
}

.alignP {
  text-align: center;
  margin-top: 50px;
  color: #909399;
}

:deep(.el-dropdown-menu__item:not(.is-disabled):focus) {
  background: #f3f4f7;
  color: #303133;
}

::v-deep .el-tree--highlight-current .el-tree-node.is-current > .el-tree-node__content {
  background-color: #C8C8C8 !important;
}
</style>
