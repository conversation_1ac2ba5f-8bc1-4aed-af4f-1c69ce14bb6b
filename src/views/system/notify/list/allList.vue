<template>
  <div>
    <div v-show="isShow == 1">
      <div
        class="relative bg-[#fff] formsHeader"
        style="display: flex; justify-content: space-between; align-items: center"
      >
        <el-col :span="3" :xl="3" :lg="5" :md="6" :sm="7" :xs="8" class="left-col">
          <el-button
            v-hasPermi="['system:notice:create']"
            type="primary"
            @click="openForm('create')"
            style="width: 100%; border-radius: 20px;margin-top: 17px"
          >
            <el-icon>
              <EditPen />
            </el-icon>
            发公告
          </el-button>
        </el-col>
        <el-input
          clearable
          placeholder="请输入公告内容（按下回车键↩︎进行搜索）"
          prefix-icon="el-icon-search"
          v-model="title"
          @keyup.enter="handleTitle"
          @clear="handleTitle"
        />
      </div>
      <el-row
        :gutter="20"
        class="rowBox relative"
        :class="{ isCol: isCollapse, 'isCBox-else': windowWidth < 768 }"
        v-loading="loading"
      >
        <el-col :span="3" :xl="3" :lg="5" :md="6" :sm="7" :xs="8" class="left-col">
          <div>
            <el-menu
              :default-active="activeIndex"
              class="el-menu-demo"
              mode="vertical"
              @select="handleSelect"
            >
              <el-menu-item index="-1">全部公告</el-menu-item>
              <el-menu-item
                v-for="dict in noticeTypeData"
                :key="parseInt(dict.value.toString())"
                :label="dict.label"
                :index="dict.value.toString()"
                class="menu-item-with-dropdown"
              >
                {{ dict.label }}
                <el-dropdown
                  @command="(command) => handleAddTypeCommand(command, dict)"
                  class="dropClass"
                >
                  <span class="icon-wrapper" @click.stop>
                    <el-icon class="rotate-icon"><MoreFilled /></el-icon>
                  </span>
                  <template #dropdown>
                    <el-dropdown-menu>
                      <el-dropdown-item command="delete">删除</el-dropdown-item>
                      <el-dropdown-item command="update" v-hasPermi="['system:notice:update']">
                        修改
                      </el-dropdown-item>
                    </el-dropdown-menu>
                  </template>
                </el-dropdown>
              </el-menu-item>
              <!-- 添加类型+ -->
              <el-menu-item @click="openAddTypeDialog" class="add-type-item">
                新建类型
                <el-icon class="plus-icon">
                  <Plus />
                </el-icon>
              </el-menu-item>
            </el-menu>
          </div>
        </el-col>
        <el-col :span="21" :xl="21" :lg="19" :md="18" :sm="17" :xs="16">
          <el-card class="oaCard isRight">
            <div v-if="notifyData.length > 0">
              <div
                class="notify-list-div"
                v-for="(item, index) in notifyData"
                :key="index"
                @click="handleDetail(item)"
              >
                <p class="p1">
                  <!-- <el-tag class="tag" size="small">保密</el-tag> -->
                  <span v-if="item.topFlag == 1" class="topFlagSpan">置顶</span>
                  <span v-if="item.hidden == 1" class="hiddenSpan">隐藏</span>
                  <span class="span1">{{ item.title }}</span>
                </p>
                <div class="imgText"
                  ><span>{{ item.title }}</span></div
                >
                <el-image :src="i1" class="img" />
                <span class="span2">{{ getHtml(item.content) }}...</span>
                <div class="p2" @click.stop>
                  <span class="span3"
                    >#<dict-tag :type="DICT_TYPE.SYSTEM_NOTICE_TYPE" :value="item.type" :dict-type-data="noticeTypeData" />#</span
                  >
                  <span class="span4">{{ item.tenantName }}</span>
                  <span class="span5">{{ toTime(item.createTime) }}</span>
                  <span class="span6" @click="showReadDetail(item)" v-if="item.readNum">已读 {{ item.readNum }}</span>
                  <span class="span6" v-else>暂未设置推送</span>
                  <el-dropdown
                    @command="(command) => handleCommand(command, item)"
                    class="dropClass"
                  >
                    <span>
                      <Icon icon="ep:more-filled" />
                    </span>
                    <template #dropdown>
                      <el-dropdown-menu>
                        <el-dropdown-item command="delete">删除</el-dropdown-item>
                        <el-dropdown-item command="topSet">
                          {{ item.topFlag == 1 ? '取消置顶' : '置顶公告' }}
                        </el-dropdown-item>
                        <el-dropdown-item command="update" v-hasPermi="['system:notice:update']">
                          编辑
                        </el-dropdown-item>
                        <el-dropdown-item command="push">
                          推送
                        </el-dropdown-item>
                      </el-dropdown-menu>
                    </template>
                  </el-dropdown>
                </div>
              </div>
              <div class="el-page">
                <el-pagination
                  background
                  v-model:current-page="pageNo"
                  v-model:page-size="pageSize"
                  layout="total,prev, pager, next, jumper"
                  :total="total"
                  @size-change="handleSizeChange"
                  @current-change="handleCurrentChange"
                />
              </div>
            </div>
            <el-empty
              :image-size="50"
              description="暂无消息"
              v-if="notifyData.length == 0"
            ></el-empty>
          </el-card>
        </el-col>
      </el-row>
    </div>
    <div v-show="isShow == 2">
      <div class="relative bg-[#fff] formsHeader">
        <el-button round @click="backList">返回</el-button>
        <span class="headerText">公告详情</span>
      </div>
      <el-card
        class="oaCard notify-html"
        :class="{ isCol: isCollapse, isNoCol: windowWidth < 768 }"
      >
        <h1>{{ detailObj.title }}</h1>
        <p>{{ detailObj.createTime ? toTime(detailObj.createTime) : '' }}</p>
        <div v-html="detailObj.content"></div>
        <fileListModule
          ref="fileListRef"
          :fileList="fileListArr"
          :isDown="detailObj.uploadFlag"
          :detailObj="detailObj"
        />
        <div class="readDiv">
          <div class="forDiv">
            已读人员：
            <div v-for="(item, index) in readList" :key="index" class="listDiv"
            >{{ item.userName }}
            </div>
          </div>
          <div class="forDiv">
            未读人员：
            <div v-for="(item, index) in notReadList" :key="index" class="listDiv"
            >{{ item.userName }}
            </div>
          </div>
        </div>
      </el-card>
    </div>
  </div>
  <!-- 表单弹窗：添加/修改 -->
  <NoticeForm ref="formRef" @success="getNotify" />
  <!-- 推送 -->
  <pushDialog ref="PushDialog" @success="getNotify"></pushDialog>
  <!-- 添加类型的弹窗 -->
  <el-dialog :title="addTypeDialogTitle" v-model="addTypeDialogVisible" width="500" :height="400">
    <el-form :model="addTypeForm" label-width="80px">
      <el-form-item label="类型名称">
        <el-input v-model="addTypeForm.name"></el-input>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="addTypeDialogVisible = false">取消</el-button>
      <el-button type="primary" @click="handleAddType">确定</el-button>
    </span>
  </el-dialog>

  <!--  已读未读统计弹窗-->
  <el-dialog
    :title="readActiveTab === 'read' ? '已读人员列表' : '未读人员列表'"
    v-model="readDialogVisible"
    width="50%"
    :close-on-click-modal="false"
  >
    <div class="header-tabs">
      <el-radio-group v-model="readActiveTab" size="large">
        <el-button
          label="read"
          text
          :type="readActiveTab === 'read' ? 'primary' : 'info'"
          @click="readActiveTab = 'read'"
        >
          已读 {{ readUsers.length }}人
        </el-button>
        <el-button
          label="unread"
          text
          :type="readActiveTab === 'unread' ? 'primary' : 'info'"
          @click="readActiveTab = 'unread'"
        >
          未读 {{ unreadUsers.length }}人
        </el-button>
      </el-radio-group>
    </div>

    <div class="users-grid">
      <template v-if="readActiveTab === 'read'">
        <div v-for="user in readUsers" :key="user.id" class="user-item">
          <el-avatar :size="50" :src="user.avatar" v-if="user.avatar"/>
          <el-avatar :size="50" v-else style="background: #3370FF">{{user.userName.substr(-2)}}</el-avatar>
          <span class="user-name">{{ user.userName }}</span>
        </div>
      </template>
      <template v-else>
        <div v-for="user in unreadUsers" :key="user.id" class="user-item">
          <el-avatar :size="50" :src="user.avatar" v-if="user.avatar"/>
          <el-avatar :size="50" v-else style="background: #3370FF">{{user.userName.substr(-2)}}</el-avatar>
          <span class="user-name">{{ user.userName }}</span>
        </div>
      </template>
    </div>
  </el-dialog>
</template>
<script type="ts" setup lang="ts">
import NoticeForm from '@/views/system/notice/NoticeForm.vue'
import pushDialog from '@/views/system/notice/pushDialog.vue'

defineOptions({ name: 'notifyListRecord' })
import i1 from '@/assets/imgs/banner1.png'
import { Icon } from '@/components/Icon'
import { EditPen, MoreFilled, Plus } from '@element-plus/icons-vue'
// import detail from '@/views/wflow/workspace/oa/fromsApp_new.vue'
import {
  recordHiddenSet
} from '@/api/notify'
import { DICT_TYPE } from '@/utils/dict'
import { useAppStore } from '@/store/modules/app'
import fileListModule from './fileListModule.vue'
import { CACHE_KEY, useCache } from '@/hooks/web/useCache'
import * as NoticeApi from '@/api/system/notice'
import * as DictDataApi from '@/api/system/dict/dict.data'

const noticeTypeData = ref<any>([])
const getNoticeType = async () => {
  try {
    const data = await DictDataApi.getDictDataList({dictType:DICT_TYPE.SYSTEM_NOTICE_TYPE})
    noticeTypeData.value = data
  } finally {
  }
}

const appStore = useAppStore()

const isAdmin = ref(0) //是否配置可见 1可见
const total = ref(0) // 列表的总页数
const activeIndex = ref('-1') //默认菜单展示-1 全部
let isShow = ref<any>('1') //默认展示 列表数据
const loading = ref(true) // 加载中
const pageNo = ref(1)
const pageSize = ref(5)
const title = ref('') //input 公告标题
const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化

const collapse = computed(() => appStore.getCollapse)
const isCollapse = ref(appStore.getCollapse) // 如果需要修改，应使用 ref

watch(collapse, (newPath) => {
  isCollapse.value = newPath // 更新响应式变量而不是计算属性
  console.log('collapse', newPath)
  console.log('isCollapse', isCollapse.value)
})

// 左侧菜单切换
const activeMenu = ref('')
const handleSelect = (e) => {
  if (e == '-1') {
    activeMenu.value = ''
  } else {
    activeMenu.value = e
  }
  pageNo.value = 1
  pageSize.value = 5
  getNotify()
}
// 详情返回
const backList = () => {
  isShow.value = 1
  //getNotify()
}
const handleTitle = () => {
  pageNo.value = 1
  pageSize.value = 5
  getNotify()
}
// 获取列表接口
const notifyData = ref<any>([])
const getNotify = async () => {
  //console.log(activeIndex.value)
  loading.value = true
  try {
    const res = await NoticeApi.getNoticePage({
      pageNo: pageNo.value,
      pageSize: pageSize.value,
      type: activeMenu.value,
      content: title.value,
      isAdmin: isAdmin.value
    })
    notifyData.value = res.list
    total.value = res.total
  } finally {
    loading.value = false
  }
}
// 翻页
const handleSizeChange = (val) => {
  pageSize.value = val
  getNotify()
}
const handleCurrentChange = (val) => {
  pageNo.value = val
  getNotify()
}
// 时间戳转时间类型
const toTime = (e) => {
  const date = new Date(e)
  const Y = date.getFullYear() + '-'
  const M = (date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1) + '-'
  const D = (date.getDate() < 10 ? '0' + date.getDate() : date.getDate()) + ' '
  const h = (date.getHours() < 10 ? '0' + date.getHours() : date.getHours()) + ':'
  const m = date.getMinutes() < 10 ? '0' + date.getMinutes() : date.getMinutes()
  //const s = date.getSeconds() < 10 ? '0' + date.getSeconds() : date.getSeconds()
  const time1 = Y + M + D + h + m
  // return time1.substring(5, 16)
  return time1
}
// 列表内span展示详情 截取字符串
const getHtml = (e) => {
  const tempDiv = document.createElement('div')
  tempDiv.innerHTML = e
  const plainText = tempDiv.innerText
  const truncatedText = plainText.substring(0, 40)
  return truncatedText
}
// 详情 html content
const detailObj = ref<any>({})
const fileListArr = ref([])
const handleDetail = (e) => {
  isShow.value = 2
  detailObj.value = e
  fileListArr.value = e.fileList ? e.fileList : []
  getRead(e.id)
}
const windowWidth = ref(window.innerWidth)
const handleResize = () => {
  windowWidth.value = window.innerWidth
}
// 查看已读未读人员
const readList = ref<any>([])
const notReadList = ref<any>([])
const getRead = async (e) => {
  readList.value = []
  notReadList.value = []
  try {
    const res = await NoticeApi.getNoticeReadUser({ noticeId: e })
    readList.value = res.read_users
    notReadList.value = res.unread_users
  } finally {
  }
}
// 删除
const handleDelete = async (id) => {
  try {
    await message.delConfirm()
    await NoticeApi.deleteNotice(id)
    message.success(t('common.delSuccess'))
    pageNo.value = 1
    await getNotify()
  } catch {}
}
// 置顶
const handleTop = async (row) => {
  try {
    await message.delConfirm(`确定「${row.topFlag == 1 ? '取消置顶' : '置顶'}」该条公告吗？`)
    await NoticeApi.noticeTopSet(row.id)
    message.success('操作成功')
    pageNo.value = 1
    await getNotify()
  } catch {}
}
// 隐藏
const handleHidden = async (row) => {
  try {
    await message.delConfirm(`确定「${row.topFlag == 1 ? '取消隐藏' : '隐藏'}」该条公告吗？`)
    await recordHiddenSet(row.id)
    message.success('操作成功')
    pageNo.value = 1
    await getNotify()
  } catch {}
}
// 更多下拉
const handleCommand = (i, row) => {
  switch (i) {
    case 'delete':
      handleDelete(row.id)
      break
    case 'topSet':
      handleTop(row)
      break
    case 'hidden':
      handleHidden(row)
      break
    case 'update':
      openForm('update', row.id)
      break
    case 'push':
      handlePush(row.id)
      break
    default:
      break
  }
}
/** 推送按钮操作 */
const PushDialog = ref()
const handlePush = async (id: string) => {
  PushDialog.value.open(id)
}
const getIfAdmin = () => {
  const { wsCache } = useCache()
  const permissions = wsCache.get(CACHE_KEY.USER).permissions
  const hasPermission = permissions.some((item) => item == 'system:notice:recordHidden')
  isAdmin.value = hasPermission ? 1 : 0
  getNotify()
}
/** 添加/修改操作 */
const formRef = ref()
const openForm = (type: string, id?: number) => {
  formRef.value.open(type, id)
}
const addTypeDialogVisible = ref(false)
const addTypeDialogTitle = ref('新建类型')
const addTypeForm = reactive({
  id:'',
  name: '' // 添加类型的表单数据
})
const openAddTypeDialog = () => {
  addTypeForm.id = ''
  addTypeForm.name = ''
  addTypeDialogTitle.value = '新建类型'
  addTypeDialogVisible.value = true
}
const handleAddType = async () => {
  // 处理添加类型
  //console.log('Add type:', addTypeForm.name)
  if(!addTypeForm.name){
    message.error('请输入类型名称')
    return
  }
  try {
    if (addTypeForm.id) {
      await NoticeApi.updateNoticeType({
        noticeTypeId: addTypeForm.id,
        noticeTypeName: addTypeForm.name
      })
    } else {
      await NoticeApi.createNoticeType({
        noticeTypeName: addTypeForm.name
      })
    }
    message.success('操作成功')
    // 这里可以调用 API 添加类型
    addTypeDialogVisible.value = false
    addTypeForm.name = '' // 清空表单
    addTypeForm.id = ''
    //todo
    getNoticeType()
    getNotify()
  } catch {
  }
}
const handleAddTypeCommand = (i, row) => {
  switch (i) {
    case 'delete':
      handleAddTypeDelete(row)
      break
    case 'update':
      handleAddTypeUpdate(row)
      break
    default:
      break
  }
}
const handleAddTypeDelete = async (row) => {
  //console.log(id)
  try {
    await message.delConfirm(`确定删除【`+row.label+`】该类型吗？`)
    await NoticeApi.deleteNoticeType(row.id)
    message.success('操作成功')
    getNoticeType()
  } catch {}
}
const handleAddTypeUpdate = (row) => {
  //console.log(id)
  addTypeForm.id = row.id
  addTypeForm.name = row.label
  // 处理修改类型
  addTypeDialogTitle.value = '修改类型'
  addTypeDialogVisible.value = true
}
//已读未读点击事件
const showReadDetail = async (item:any) => {
  //console.log(item.id)
  // todo 请求后端接口获取已读未读人员列表，赋值给readList和notReadList
  loading.value = true
  try {
    const res = await NoticeApi.getNoticeReadUser({
      noticeId: item.id
    })
    readUsers.value = res.read_users
    unreadUsers.value = res.unread_users
  } finally {
    loading.value = false
  }
  readDialogVisible.value = true
}
const readDialogVisible = ref(false)
const readActiveTab = ref('read')

const readUsers = ref<any>([])
const unreadUsers = ref<any>([])
onMounted(() => {
  window.addEventListener('resize', handleResize)
  getIfAdmin() //是否租户管理员 查权限 加入参
  getNoticeType()
  // getNotify()
})
onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
})
</script>
<style lang="less" scoped>
:deep(.el-menu) {
  border-right: none;
  font-size: 14px;
  color: #303133;
}

:deep(.el-menu-item.is-active) {
  background: #f3f4f7;
  border-right: 2px solid #3370ff;
  color: #303133;
}

:deep(.el-menu-item:hover) {
  background: #f3f4f7;
}

:deep(.el-menu-item) {
  // height: 40px;
  // line-height: 40px;
  padding: 0 25px;
}

.formsHeader {
  left: -15px;
  right: -15px;
  width: 100%;
  top: -15px;
  width: calc(100% + 30px);
  height: 70px;
  align-items: center;
  display: flex;
  justify-content: right;
  box-shadow: inset 0px -1px 0px 0px #eceded;

  .el-input {
    width: 360px;
    margin-right: 20px;
    height: 36px;

    :deep(.el-input__wrapper) {
      border-radius: 20px;
    }
  }
}

.rowBox {
  margin: 0 0 0 20px;
  position: fixed;
  left: 210px;
  right: 15px;
  top: 151px;
  bottom: 0;
}

.left-col {
  padding: 0 !important;
  background: #fff;
  border-right: 1px solid #eceded;
  box-sizing: border-box;
}

.isRight {
  margin-top: 10px;
}

.oaCard {
  :deep(.el-card__body) {
    padding: 0 20px;
  }
}

.notify-list-div {
  position: relative;
  padding: 20px 0;
  cursor: pointer;
  border-bottom: 1px solid #ebeef5;

  .p1 {
    display: flex;
    align-items: center;
    justify-content: left;
    padding: 0;
    margin: 0;
    margin-bottom: 16px;
  }

  .tag {
    background: #fff;
    margin-right: 10px;
    padding: 0 5px;
    height: 15px;
  }

  .span1 {
    color: #303133;
    font-size: 16px;
    // width: 85%;
    margin-right: 180px;
    font-family: auto;
  }

  .img {
    position: absolute;
    right: 0;
    top: 21px;
    width: 150px;
    height: 80px;
    border-radius: 6px;
  }

  .imgText {
    position: absolute;
    right: 0;
    top: 21px;
    width: 150px;
    height: 80px;
    border-radius: 6px;
    color: #fff;
    z-index: 10;
    padding: 12px 10px;
    box-sizing: border-box;
    font-size: 12px;

    span {
      display: -webkit-box;
      overflow: hidden;
      -webkit-line-clamp: 3;
      /* 指定显示的行数 */
      -webkit-box-orient: vertical;
      text-overflow: ellipsis;
    }
  }

  .span2 {
    color: #909399;
    font-size: 14px;
    display: block;
    margin-bottom: 10px;
    width: fit-content;
    margin-right: 160px;
  }

  .p2 {
    color: #909399;
    font-size: 12px;
    padding: 0;
    margin: 0;
    margin-right: 160px;
  }

  .span3 {
    color: #51749a;
    font-size: 12px;
    margin-right: 15px;

    .el-tag {
      color: #51749a;
      background: none;
      border: none;
      padding: 0;
      height: 13px;
      line-height: 13px;
      box-sizing: border-box;
    }
  }

  .span4 {
    margin-right: 15px;
  }

  .span5 {
    margin-right: 15px;
  }

  .span6 {
    color: #51749a;
    margin-right: 15px;
  }

  .span7 {
    color: #ff4141;
  }
}

.notify-list-div:nth-last-child(1) {
  border-bottom: none;
}

.el-button.is-round {
  padding: 16px 24px;
  position: absolute;
  left: 20px;
}

.headerText {
  margin: 0 auto;
  font-size: 18px;
  color: #303133;
}

.notify-html {
  background: #fff;
  border-right: 1px solid #eceded;
  border-radius: 0 !important;
  border: none !important;
  padding: 15px 16%;
  box-sizing: border-box;
  margin: 0;
  position: fixed;
  left: 200px;
  right: 0;
  top: 151px;
  bottom: 0;
  overflow: auto;
  box-sizing: border-box;
  width: 100%;

  :deep(.el-card__body) {
    padding: 0 !important;
  }

  h1 {
    font-size: 24px;
    margin: 10px 0;
  }

  p {
    margin: 0;
    font-size: 14px;
    color: #b2b2b2;
  }
}

.el-page {
  float: right;
  margin: 20px 0;
}

.isCol {
  left: 64px;
  margin-left: 0 !important;
}

.isNoCol {
  left: 0;
}

.isCBox-else {
  left: 0;
}

.el-col {
  height: 100%;
  overflow: auto;
}

.readDiv {
  border-top: 1px solid #c7c7cc;
  padding: 20px 0;
  position: relative;

  .forDiv {
    display: flex;
    margin-bottom: 10px;
    line-height: 150%;
    flex-wrap: wrap;
  }

  .listDiv {
    margin-right: 10px;
  }
}

.readDiv::before {
  // content: '';
  // width: 30%;
  // height: 1px;
  // left: 0;
  // top: 0;
  // position: absolute;
  // background: #c7c7cc;
}

.dropClass {
  position: relative;
  padding-left: 15px;

  .el-icon {
    color: #7a8189;
  }
}

.dropClass::before {
  position: absolute;
  content: '';
  width: 0px;
  height: 14px;
  background: #ecedee;
  left: 0;
  top: 1px;
}

.topFlagSpan {
  font-size: 11px;
  padding: 1px 6px;
  background: #fb5f2e;
  border-radius: 8px;
  color: #fff;
  margin-right: 4px;
}

.hiddenSpan {
  font-size: 11px;
  padding: 1px 6px;
  background: #bbb;
  border-radius: 8px;
  color: #fff;
  margin-right: 4px;
}
</style>
<style>
.notify-html table {
  border: 1px solid #303330;
  border-collapse: collapse;
}

.notify-html table td,
.notify-html table th {
  border: 1px solid #303330;
  border-collapse: collapse;
}

.notify-html table tr {
  height: 2rem;
}

/* 让菜单项内容分布在两端 */
.menu-item-with-dropdown {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

/* 图标容器 */
.icon-wrapper {
  display: inline-block;
  margin-left: 16px; /* 统一间距 */
}

/* 旋转图标 90° */
.rotate-icon {
  transform: rotate(90deg); /* 向右旋转 90° */
}

/* 添加类型菜单项的样式 */
.add-type-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

/* + 号图标的样式 */
.plus-icon {
  margin-left: 16px; /* 统一间距 */
  font-size: 14px; /* 调整图标大小 */
  color: #409eff; /* 调整图标颜色 */
}

/* 将按钮排列在右侧 */
.dialog-footer {
  display: flex;
  justify-content: flex-end; /* 将按钮推到右侧 */
  align-items: center; /* 垂直居中 */
}

.header-tabs {
  display: flex;
  justify-content: flex-start;
  margin-bottom: 20px;
}

.users-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(80px, 1fr));
  gap: 20px;
  padding: 10px;
}

.user-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

.user-name {
  font-size: 14px;
  color: #333;
  text-align: center;
}
</style>
