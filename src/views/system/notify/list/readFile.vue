<template>
  <Dialog v-model="dialogVisible" :title="fileObj.name" :width="width" append-to-body class="file-custom-dialog"
    :class="{ 'file-custom-dialog2': isFrom != 1 }">
    <icon name="el-icon-download" @click="downloadFile" v-if="isDown" title="下载"></icon>
    <img src="@/assets/image/newWindow.png" class="newWindow" title="打开新窗口" @click="openWindow" />
    <p class="timeName" v-if="isFrom != 1">最后更新：{{ detailObj.createTime ? toTime(detailObj.createTime) : '' }}
      {{ detailObj.creatorName }}
    </p>
    <iframe :src="isFrom == 1 ? fileObj.url : fileObj.previewUrl" width="100%" height="100%"
      class="iframe-style"></iframe>
  </Dialog>
</template>
<script lang="ts" setup>

const dialogVisible = ref(false)
const fileObj = ref({
  name: '',
  downloadUrl: '',
  url: '',
  previewUrl: ''
})
const width = ref('55%')
const screenWidth = ref(0)


const isDown = ref(false)
const isFrom = ref()//2消息模块查看附件详情
const detailObj = ref()
const open = (row, isAllow, from, obj) => {
  screenWidth.value = document.body.clientWidth
  window.onresize = () => {
    screenWidth.value = document.body.clientWidth
  }

  dialogVisible.value = true
  fileObj.value = row
  isDown.value = isAllow
  isFrom.value = from
  detailObj.value = obj
}

watch(screenWidth, (newValue) => {
  if (newValue > 1600) {
    width.value = '55%'
  } else if (newValue >= 1200) {
    width.value = '65%'
  } else if (newValue >= 700) {
    width.value = '75%'
  } else if (newValue < 700) {
    width.value = '95%'
  }
})

const downUrl = () => {
  let a = document.createElement('a')
  a.download = fileObj.value.name
  if (isFrom.value == 1) {
    a.href = fileObj.value.downloadUrl + '?response-content-type=application/octet-stream'
  } else if (isFrom.value == 2) {
    a.href = fileObj.value.downloadUrl
  } else {
    a.href = fileObj.value.url + '?response-content-type=application/octet-stream'
  }
  a.click()
}
const message = useMessage() // 消息弹窗
const downloadFile = () => {
  // 获取文件名
  let fileName = fileObj.value.name
  // 文件URL
  let fileUrl = ''
  if(isFrom.value == 1){
    fileUrl = fileObj.value.downloadUrl + '?response-content-type=application/octet-stream'
  }else if(isFrom.value == 2){
    fileUrl = fileObj.value.downloadUrl
  }else{
    fileUrl= fileObj.value.url + '?response-content-type=application/octet-stream'
  }
  if(fileUrl.startsWith('http://')){
    fileUrl = fileUrl.replace('http://', 'https://')
  }
  // 创建一个新的Blob对象，并设置其MIME类型
  fetch(fileUrl)
      .then(response => response.blob())
      .then(blob => {
        // 创建一个指向该Blob的URL
        const url = URL.createObjectURL(blob);
        const link = document.createElement('a')
        link.href = url
        link.download = fileName // 设置下载后的文件名
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
        URL.revokeObjectURL(url) // 释放创建的URL
      })
      .catch(error => message.error('下载文件失败:' + error))
}
const openWindow = () => {
  window.open(isFrom.value == 1 ? fileObj.value.url : fileObj.value.previewUrl)
}

// 时间戳转时间类型
const toTime = (e) => {
  const date = new Date(e)
  const Y = date.getFullYear() + '-'
  const M = (date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1) + '-'
  const D = (date.getDate() < 10 ? '0' + date.getDate() : date.getDate()) + ' '
  const h = (date.getHours() < 10 ? '0' + date.getHours() : date.getHours()) + ':'
  const m = date.getMinutes() < 10 ? '0' + date.getMinutes() : date.getMinutes()
  const s = date.getSeconds() < 10 ? '0' + date.getSeconds() : date.getSeconds()
  const time1 = Y + M + D + h + m
  // return time1.substring(5, 16)
  return time1
}

defineExpose({ open }) 
</script>
<style lang="less" scoped>
.iframe-style {
  border: medium none;
}

.el-icon-download {
  position: absolute;
  right: 129px;
  top: 35px;
  font-size: 16px;
  height: 16px;
  z-index: 10;
  color: #909399;
  cursor: pointer;
}

.newWindow {
  position: absolute;
  right: 96px;
  top: 37px;
  width: 16px;
  z-index: 10;
  color: #909399;
  cursor: pointer;
}
</style>
<style>
.file-custom-dialog {
  height: 750px;
  position: relative;
}

.file-custom-dialog .el-dialog__body {
  height: calc(100% - 85px);
}

.file-custom-dialog2 .el-dialog__header>div {
  height: 42px;
  font-size: 18px;
}

.file-custom-dialog2 .el-dialog__header>div>div {
  height: 54px;
  padding-top: 12px;
}

.file-custom-dialog2 .timeName {
  margin-top: 15px;
  position: absolute;
  top: 34px;
  font-size: 12px;
  color: #959595;
}
</style>