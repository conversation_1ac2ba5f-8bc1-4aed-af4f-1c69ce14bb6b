<template>
  <div>
    <el-dialog v-model="dialog" title="邀请成员" :before-close="drawerClose" class="custom-dialog" width="26%">
      <el-icon @click="drawerClose">
        <Close />
      </el-icon>
      <el-tabs v-model="activeName" @tab-click="handleClick" class="dialogTab" v-loading='loading'>
        <el-tab-pane label="分享链接" name="1" class="tab1">
          <p class="p1">复制邀请链接，粘贴至微信/QQ/短信等</p>
          <p class="p2">{{ link.text }}</p>
        </el-tab-pane>
        <el-tab-pane label="二维码邀请" name="2" class="tab2">
          <div class="tab2-content" ref="myElement" id="tab2-content">
            <p class="span1">{{ props.info.nickname }}邀请你加入</p>
            <p class="span2">{{ props.info.tenantName }}</p>
            <div class="codeDiv">
              <img :src='code' crossorigin="anonymous">
            </div>
          </div>
        </el-tab-pane>
      </el-tabs>
      <template #footer>
        <div>
          <el-button type="primary" v-if="activeName == 1" @click="copy" :disabled='loading'>复制</el-button>
          <el-button type="primary" v-if="activeName == 2" @click="save" :disabled='loading'>保存</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup type="ts">
import { Close } from '@element-plus/icons-vue'
import { qrcode, invite } from '@/api/hrManage/newStaff'
import useClipboard from "vue-clipboard3";//复制
import html2canvas from 'html2canvas'
const { toClipboard } = useClipboard();
const props = defineProps({
  id: {
    type: Number,
    default: ""
  },
  info: {
    type: Object,
    default: {}
  },
})

// const emit = defineEmits(["close"])
const loading = ref(true)//默认加载
const dialog = ref(false)//弹窗默认false
const link = ref('')//url
// const allLink =ref('')//拼接展示的文字+url
const open = async (tenantId) => {
  try {
    link.value = ''
    // allLink.value =''
    code.value = ''
    activeName.value = '1'
    dialog.value = true
    isFirst.value = true
    const res = await invite({ deptId: props.id,tenantId })
    link.value = res
    // allLink.value = props.info.nickname+'邀请你加入'+props.info.tenantName+' 我们都在用诺鑫办办沟通，点击链接立即申请加入，开启高效沟通新模式:'+res
  } finally {
    loading.value = false
  }
}
function drawerClose() {
  dialog.value = false
}
const activeName = ref('1')//tab默认展示链接
const handleClick = (e) => {
  if (e.index == 1) {
    getCode()
  }
}
// 查看二维码
const code = ref('')
const isFirst = ref(true)//是否首次打开
const getCode = () => {
  if (isFirst.value) {
    loading.value = true
    isFirst.value = false
    qrcode({ url: link.value.url}).then((res) => {
      loading.value = false
      code.value = res
    }).catch((e) => {
      loading.value = false
    })
  }
}
// 复制
const copy = async () => {
  try {
    await toClipboard(link.value.url)
    ElMessage.success("复制成功")
  } catch (e) {
    ElMessage.error(e)
  }
}
// 保存
const myElement = ref()//需要生成的div
const save = async () => {
  const divToConvert = myElement.value
  try {
    const canvas = await html2canvas(divToConvert, {
      backgroundColor: '#ffffff',
      useCORS: true,
    })
    const dataURL = canvas.toDataURL('image/png')
    const downloadLink = document.createElement('a')
    downloadLink.href = dataURL;
    downloadLink.download = '二维码邀请.png'
    downloadLink.click();
  } catch (error) {
    ElMessage.error(error)
  }
}
defineExpose({
  open, drawerClose
})
</script>
<style lang="less" scoped>
.el-icon {
  z-index: 99;
  position: absolute;
  right: 20px;
  top: 20px;
  color: #303133;
  font-size: 16px;
  cursor: pointer;
  font-weight: bold;
}

:deep(.el-dialog__header) {
  display: none;
}

:deep(.el-dialog__body) {
  padding: 0;
}

:deep(.dialogTab .el-tabs__content) {
  min-height: 300px;
}

.tab1 {
  padding: 0;

  .p1 {
    padding: 0;
    color: #303133;
    margin: 25px 0;
  }

  .p2 {
    border-radius: 10px;
    background: #f2f2f6;
    padding: 20px;
    word-break: break-all;
    line-height: 170%;
  }
}

.tab2 {
  .tab2-content {
    background: #fff;
    margin: 40px auto;
    width: 70%;
    border: 1px solid rgba(0, 0, 0, 0.1);
    // box-shadow: 0 0 8px 0px rgba(0, 0, 0, 0.1);
    border-radius: 4px;
    text-align: center;
  }

  .span1 {
    padding: 20px 0 8px;
    color: #a2a3a5;
    margin: 0;
  }

  .span2 {
    color: #303133;
    font-weight: bold;
    font-size: 18px;
    margin: 0 0 20px;
  }

  .codeDiv {
    border-top: 1px solid #dcdfe6;
    padding: 20px 0 40px;

    img {
      width: 200px;
      // box-shadow: 0 0 8px 0px rgba(0, 0, 0, 0.1);
      border: 1px solid rgba(0, 0, 0, 0.1);
    }
  }
}
</style>