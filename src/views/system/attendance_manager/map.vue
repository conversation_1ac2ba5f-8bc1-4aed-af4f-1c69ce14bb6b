<template>
  <Dialog v-model="dialogVisible" title="添加考勤地点">
    <div class="box">
      <div id="container" style="width: 100%; height: 400px; position: relative">
        <!-- <input
        class="keyword"
        id="keyword"
        placeholder="请输入搜索位置"
        style="position: absolute; z-index: 99"
      /> -->
        <el-input
          v-model="keyword"
          class="keyword"
          id="keyword"
          placeholder=""
          style="position: absolute; z-index: 99"
        ></el-input>
        <el-select
          v-model="metersValue"
          placeholder="Select"
          size="large"
          @change="seleMeters"
          style="width: 240px; position: absolute; z-index: 99; left: 250px"
        >
          <el-option v-for="item in options" :key="item.id" :label="item.Meters" :value="item.id" />
        </el-select>
      </div>
    </div>

    <template #footer>
      <el-button @click="dialogVisible = false">取消</el-button>
      <!-- <el-button :disabled="formLoading" type="primary" @click="submitForm">确 定</el-button> -->
      <el-button type="primary" :disabled="formLoading" @click="submitForm">确 定</el-button>
    </template>
  </Dialog>

  <!-- <ContentWrap class="h-1/1"> </ContentWrap> -->
</template>

<script lang="ts" setup>
import AMapLoader from '@amap/amap-jsapi-loader'
import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'
import * as RoleApi from '@/api/system/role'
import { getUserProfile } from '@/api/system/user/profile'
import { CaretTop, CaretBottom, ArrowRight } from '@element-plus/icons-vue'
import { tr } from 'element-plus/es/locale'
defineOptions({ name: 'orgstructure' })

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化
const dialogVisible = ref(false) // 弹窗的是否展示
const formLoading = ref(true)

const loading = ref(true) // 列表的加载中
const queryParams = reactive({
  id: ''
})

const options = ref([
  {
    id: 0,
    Meters: '0米'
  },
   {
    id: 20,
    Meters: '20米'
  },
  {
    id: 100,
    Meters: '100米'
  },
  {
    id: 200,
    Meters: '200米'
  },
  {
    id: 300,
    Meters: '300米'
  },

  {
    id: 400,
    Meters: '400米'
  },
  {
    id: 500,
    Meters: '500米'
  },
  {
    id: 600,
    Meters: '600米'
  },
  {
    id: 700,
    Meters: '700米'
  },
  {
    id: 800,
    Meters: '800米'
  },
  {
    id: 900,
    Meters: '900米'
  },
  {
    id: 1000,
    Meters: '1000米'
  },
  {
    id: 2000,
    Meters: '2000米'
  },
  {
    id: 3000,
    Meters: '3000米'
  }
])

const metersValue = ref(300)
const keyword = ref('')
const radiues = ref(300)
const objData = ref({})
console.log(metersValue.value)

const seleMeters = async () => {
  console.log(metersValue.value)
  radiues.value = metersValue.value
}
// 存储搜索用的数据
const form: any = reactive({
  address: ''
})
var map = null
const ininMap = () => {
  window._AMapSecurityConfig = {
    securityJsCode: '6b7155cc707ec47aed474972a96b1add' //密钥
  }
  AMapLoader.load({
    key: '63a96bbd4c3209cf856c96d61345a2b7', //api服务key--另外需要在public中使用安全密钥！！！
    version: '1.4.15' // 指定要加载的 JSAPI 的版本，缺省时默认为 1.4.15
    // plugins: ['AMap.PlaceSearch', 'AMap.AutoComplete'] // 需要使用的的插件列表
  })
    .then((AMap) => {
      map = new AMap.Map('container', {
        resizeEnable: true,
        zoom: 9.5, // 地图显示的缩放级别
        // center: [117.2, 31.8]
        center: [116.433322, 39.900256]
      })
      AMap.plugin(
        [
          'AMap.Autocomplete',
          'AMap.PlaceSearch',
          'AMap.Geocoder',
          'AMap.Circle',
          // 'AMap.Label',
          'AMap.Pixel',
          'AMap.Marker',
          'AMap.Text',
          'AMap.Geocoder'
        ],
        function () {
          const autoOptions = {
            input: 'keyword' // 使用联想输入的input的id
          }
          console.log(radiues.value)

          const circle = new AMap.Circle({
            // center: [116.433322, 39.900255],
            // map: map,
            center: map.getCenter(),
            // radius: 5000, //半径
            radius: radiues.value, // 初始半径, //半径
            borderWeight: 3,
            strokeColor: '#007acc',
            strokeOpacity: 1,
            strokeWeight: 0,
            fillOpacity: 0.4,
            strokeStyle: 'solid',
            strokeDasharray: [0, 0, 0],
            // 线样式还支持 'dashed'
            fillColor: '#1791fc',
            zIndex: 50
          })
          // var label = new AMap.Text({
          //   map: map,
          //   position: circle.getCenter(), // 标签初始位置设置为圆心
          //   text: '圆形跟随的文字', // 显示的文字
          //   offset: new AMap.Pixel(20, 0) // 文字的偏移量
          // })
          // var textMarker = new AMap.Marker({
          //   map: map,
          //   position: circle.getCenter(), // 文字提示的位置设置为圆心
          //   offset: new AMap.Pixel(-10, -10), // 设置文字提示的偏移量，使其位于圆形旁边
          //   content: '圆形提示文字' // 文字提示的内容
          // })
          map.add(circle)
          // map.setFitView([circle])

          // map.add(textMarker)
          // circle.setMap(map)
          // map.add(circle)
          // 监听圆形的移动事件
          // circle.on('move', function (event) {
          //   // 当圆形移动时，更新标签的位置
          //   label.setPosition(circle.getCenter())
          // })
          map.on('click', function (e) {
            console.log(e)
            formLoading.value = false
            objData.value.newAddress = e.lnglat
            // e.lnglat 获取点击的经纬度位置
            circle.setCenter(e.lnglat) // 设置圆的新中心点
            map.setFitView([circle])
            // 获取详细地址
            var geocoder = new AMap.Geocoder()
            geocoder.getAddress([e.lnglat.lng, e.lnglat.lat], function (status, result) {
              console.log(status);
              console.log(result);
              
              if (status === 'complete' && result.regeocode) {
                const cityValue = result.regeocode.formattedAddress
                objData.value.cityValues = cityValue
              } else {
              }
            })
          })

          watch(radiues, (newRadius) => {
            // map.setZoom(Math.log(radiues.value)*3.5);
            console.log(Math.log(radiues.value) * 3.5)
            console.log(Math.log(radiues.value))

            circle.setRadius(Number(newRadius))
          })
          // const label = new AMap.Label({
          //   position: circle.getCenter(),
          //   text: '这是一个圆形',
          //   offset: new AMap.Pixel(-50, -50),
          //   style: {
          //     fontSize: '16px',
          //     color: '#FF33FF'
          //   }
          // })
          // map.add(label);

          const autocomplete = new AMap.Autocomplete(autoOptions)
          const placeSearch = new AMap.PlaceSearch({
            map: map
          })
          autocomplete.on('select', select) //注册监听，当选中某条记录时会触发
          function select(e) {
            console.log(e, '搜搜eeee')
            formLoading.value = false

            objData.value.cityValues = ''
            objData.value.newAddress = e.poi
            circle.setCenter(e.poi.location) // 设置圆的新中心点
            map.setFitView([circle])
            placeSearch.setCity(e.poi.adcode)
            placeSearch.search(e.poi.name, function (status, result) {
              console.log(status)
              console.log(result)
            }) //关键字查询查询
          }
          // const geocoder = new AMap.Geocoder({
          //   radius: 1000,
          //   extensions: 'all'
          // })
          // AMap.event.addListener(autocomplete, 'select', function (e) {
          //   placeSearch.setCity(e.poi.adcode)
          //   placeSearch.search(e.poi.name, function (status, result) {
          //     const pois = result.poiList.pois
          //     for (let i = 0; i < pois.length; i++) {
          //       if (pois[i].name === e.poi.name) {
          //         console.log('搜索结果', pois[i])
          //         geocoder.getAddress(
          //           [pois[i].location.lng, pois[i].location.lat],
          //           function (status, result) {
          //             console.log(result)
          //             if (status === 'complete' && result.info === 'OK') {
          //               form.address = result.regeocode.formattedAddress
          //             } else {
          //               form.address = ''
          //             }
          //           }
          //         )
          //       }
          //     }
          //   })
          // })
        }
      )
    })
    .catch((e) => {})
}
const open = async (val) => {
  dialogVisible.value = true
  formLoading.value = true
  keyword.value = ''
  metersValue.value = 300
  radiues.value = 300
  ininMap()
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  objData.value.radius = radiues.value
  emit('success', objData.value)
  dialogVisible.value = false
}
/** 初始化 */
onMounted(() => {})
</script>

<style lang="scss" scoped>
.info {
  padding: 0.5rem 0.7rem;
  margin-bottom: 1rem;
  border-radius: 0.25rem;
  position: fixed;
  top: 1rem;
  background-color: white;
  width: auto;
  min-width: 15rem;
  border-width: 0;
  right: 1rem;
  box-shadow: 0 2px 6px 0 rgba(240, 131, 0, 0.5);
  .input-item {
    position: relative;
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    width: 100%;
    height: 2.2rem;
    border: 1px solid red;
    border-radius: 0.2rem;
    .input-item-prepend {
      margin-right: -1px;
    }
    .input-item-prepend {
      width: 35%;
      font-size: 13px;
      border-right: 1px solid red;
      height: 100%;
      display: flex;
      align-items: center;
      background: rgba(240, 131, 0, 0.1);
      span {
        text-align: center;
      }
    }
    input {
      width: 60%;
      background: #fff;
      padding: 0.2rem 0.6rem;
      margin-left: 0.3rem;
      border: none;
    }
  }
}
</style>
