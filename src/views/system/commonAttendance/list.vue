<template>
  <div class="app-main">
    <div>
      <el-form :inline="true" ref="queryFormRef" :model="queryParams" @keydown.enter.prevent>
        <el-form-item label="考勤月份" prop="month">

          <el-date-picker v-model="queryParams.month" value-format="YYYY-MM" type="month" placeholder="请选择月份"
            class="!w-220px" />
        </el-form-item>
        <el-form-item label="创建时间">

          <el-date-picker v-model="createdTime" value-format="YYYY-MM-DD" type="daterange" start-placeholder="开始日期"
            end-placeholder="结束日期" class="!w-220px" @change="selectCreatedTime" />

        </el-form-item>
        <el-form-item label="状态" prop="status" >
          <el-select v-model="queryParams.status" class="!w-220px" clearable>
            <el-option label="待确认" :value="1" />
            <el-option label="待审核" :value="2" />
            <el-option label="已确认" :value="3" />
          </el-select>
        </el-form-item>
        <el-form-item label="">
          <el-button type="primary" @click="handleSearch">查询</el-button>
          <el-button @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div style="margin-bottom: 20px;">
      <el-button type="primary" @click="handleAdd()">新增考勤</el-button>
    </div>
    <div>
      <el-table :data="list">
        <el-table-column label="序号" type="index" width="80"></el-table-column>
        <el-table-column label="考勤月份" prop="month"></el-table-column>
        <el-table-column label="创建时间" prop="createTime"></el-table-column>
        <el-table-column label="创建人" prop="createUser"></el-table-column>
        <el-table-column label="附件" prop="fileUrl">
          <template v-slot="{ row }">
            <el-button v-if="row.fileUrl" @click="fileOpen(row)" type="text">
              {{ row.month + '.' + row.fileUrl.split(".").pop() }}
            </el-button>
          </template>
        </el-table-column>
        <el-table-column label="状态" prop="fileUrl">
          <template v-slot="{ row }">
            <div v-if="row.status == 1"> 待确认 </div>
            <div v-if="row.status == 2"> 待审核 </div>
            <div v-if="row.status == 3"> 已确认 </div>
          </template>
        </el-table-column>
        <el-table-column label="操作">
          <template v-slot="{ row }">
            <el-button type="text" @click="showAttendanceDetail(row)">考勤详情</el-button>
            <el-button type="text" @click="showOperationRecord(row.id)">操作记录</el-button>
            <el-button v-if="row.status == 1" type="text" @click="deleteAttendance(row.id)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <Pagination :total="total" v-model:page="queryParams.current" v-model:limit="queryParams.size"
        @pagination="getList" />



    </div>



    <el-dialog title="新增考勤" v-model="addAttendanceVisible" width="500" top="30vh" destroy-on-close>
      <el-form :model="addAttendanceForm" label-position="left" label-width="120">
        <el-form-item label="考勤月份:">
          <el-date-picker v-model="addAttendanceForm.month" value-format="YYYY-MM" type="month" placeholder="请选择月份"
            class="!w-220px" />
        </el-form-item>
        <el-form-item label="上传考勤:">
          <!-- <el-upload action="" :auto-upload="false" :file-list="fileList" :on-change="onChange" :on-remove="onRemove"
              :limit="1" style="width: 220px">
              <div style="width: 300px">
                <el-button @click="fileList = []" size="default">点击上传</el-button>
              </div>
            </el-upload> -->
          <UploadFile v-model="addAttendanceForm.url" :fileType="['csv', 'xls', 'xlsx']" :limit="1"
            class="min-w-180px" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="addAttendanceVisible = false">取消</el-button>
          <el-button type="primary" @click="addAttendanceSubmit()"> 确认 </el-button>
        </div>
      </template>
    </el-dialog>


    <el-dialog title="操作记录" key="operationRecordDialog" destroy-on-close v-model="operationRecordVisible" width="800" top="10vh">
      <el-table :data="operationRecordList">
        <el-table-column label="序号" type="index" width="80"></el-table-column>
        <el-table-column align="center" label="操作人" prop="createUser" />
        <el-table-column align="center" label="操作时间" prop="createTime" />
        <el-table-column align="center" label="操作状态" prop="type">
          <template v-slot="{ row }">
            <div v-if="row.type == 1"> 上传 </div>
            <div v-if="row.type == 2"> 确认 </div>
            <div v-if="row.type == 3"> 审核通过 </div>
            <div v-if="row.type == 4"> 审核拒绝 </div>
          </template>

        </el-table-column>
        <el-table-column align="center" label="备注" prop="remark" />
      </el-table>
      <!-- 分页 -->
      <Pagination key="operationRecordDialog-Pagination" class="bt" v-model:limit="operationRecordParams.size" v-model:page="operationRecordParams.current"
        :total="operationRecordTotal" @pagination="getOperationRecordList" />

       <div style="margin-bottom:60px;"></div> 

      <template #footer>
        <el-button @click="operationRecordVisible = false">返 回</el-button>

      </template>
    </el-dialog>


    <el-dialog v-model="attendanceDetailVisible" width="90%" top="30vh" destroy-on-close>
      <template #header>
        <div class="center-align" style="font-size:18px;">
          <div style="color:#303133;margin-right:6px;">
            考勤详情
          </div>
          <div style="color:#f00;">
            --
            <span v-if="attendanceDetail.status == 1">待确认考勤</span>
            <span v-if="attendanceDetail.status == 2">待审核考勤</span>
            <span v-if="attendanceDetail.status == 3">已确认考勤</span>
          </div>


        </div>
      </template>

      <el-form :inline="true" ref="queryAttendanceDetailFormRef" :model="queryParams" @keydown.enter.prevent>
        <el-form-item label="员工姓名" prop="name">

          <el-input v-model="queryAttendanceDetailParams.name" placeholder="请输入员工姓名" class="!w-220px" />

        </el-form-item>
        <el-form-item label="手机号" prop="tel">

          <el-input v-model="queryAttendanceDetailParams.tel" placeholder="请输入手机号" class="!w-220px" />

        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-select v-model="queryParams.status" class="!w-140px" clearable>
            <el-option label="待确认" :value="1" />
            <el-option label="待审核" :value="2" />
            <el-option label="已确认" :value="3" />
          </el-select>
        </el-form-item>
        <el-form-item label="">
          <el-button type="primary" @click="handleAttendanceDetailSearch">查询</el-button>
          <el-button @click="resetAttendanceDetailQuery">重置</el-button>
        </el-form-item>
      </el-form>
      <!-- <div v-if="attendanceDetail.status == 1 && attendanceDetail.？？？">
        审核拒绝原因：{{ attendanceDetail.？？？ }}
      </div> -->
      <el-table :data="attendanceDetailList">
        <el-table-column label="序号" type="index"></el-table-column>
        <el-table-column align="center" label="员工姓名" prop="name" />

        <el-table-column align="center" label="手机号" prop="tel" width="110" />
        <el-table-column align="center" label="应出勤天数" prop="actualAttendanceDays">
          <template v-slot="{ row }">
            <div class="center-view">

              <el-button v-if="attendanceDetail.status == 1" type="text"
                @click="editAttendanceDetail(row, 'requiredAttendanceDays')">{{ row.requiredAttendanceDays
                }}</el-button>
              <div v-else>{{ row.requiredAttendanceDays }}</div>

              <img v-if="row.requiredAttendanceDaysLog && row.requiredAttendanceDaysLog.length > 0" class="more-icon"
                @click="showMore(row.requiredAttendanceDaysLog)" src="@/assets/image/more.png" />
            </div>
          </template>
        </el-table-column>


        <el-table-column align="center" label="实际出勤天数" prop="actualAttendanceDays">
          <template v-slot="{ row }">
            <div class="center-view">

              <el-button v-if="attendanceDetail.status == 1" type="text"
                @click="editAttendanceDetail(row, 'actualAttendanceDays')">{{ row.actualAttendanceDays
                }}</el-button>
              <div v-else>{{ row.actualAttendanceDays }}</div>

              <img v-if="row.actualAttendanceDaysLog && row.actualAttendanceDaysLog.length > 0" class="more-icon"
                @click="showMore(row.actualAttendanceDaysLog)" src="@/assets/image/more.png" />
            </div>
          </template>
        </el-table-column>
        <el-table-column align="center" label="事假天数" prop="leaveDays">
          <template v-slot="{ row }">
            <div class="center-view">

              <el-button v-if="attendanceDetail.status == 1" type="text"
                @click="editAttendanceDetail(row, 'leaveDays')">{{ row.leaveDays
                }}</el-button>
              <div v-else>{{ row.leaveDays }}</div>

              <img v-if="row.leaveDaysLog && row.leaveDaysLog.length > 0" class="more-icon"
                @click="showMore(row.leaveDaysLog)" src="@/assets/image/more.png" />
            </div>
          </template>
        </el-table-column>
        <el-table-column align="center" label="病假天数" prop="leaveDays">
          <template v-slot="{ row }">
            <div class="center-view">

              <el-button v-if="attendanceDetail.status == 1" type="text"
                @click="editAttendanceDetail(row, 'sickDays')">{{ row.sickDays
                }}</el-button>
              <div v-else>{{ row.sickDays }}</div>

              <img v-if="row.sickDaysLog && row.sickDaysLog.length > 0" class="more-icon"
                @click="showMore(row.sickDaysLog)" src="@/assets/image/more.png" />
            </div>
          </template>
        </el-table-column>
        <!-- 继续仿写其他列 -->
        <el-table-column align="center" label="带薪假天数" prop="paidDays">
          <template v-slot="{ row }">
            <div class="center-view">

              <el-button v-if="attendanceDetail.status == 1" type="text"
                @click="editAttendanceDetail(row, 'paidDays')">{{ row.paidDays
                }}</el-button>
              <div v-else>{{ row.paidDays }}</div>

              <img v-if="row.paidDaysLog && row.paidDaysLog.length > 0" class="more-icon"
                @click="showMore(row.paidDaysLog)" src="@/assets/image/more.png" />
            </div>
          </template>
        </el-table-column>
        <el-table-column align="center" label="有卡天数" prop="clockDays">
          <template v-slot="{ row }">
            <div class="center-view">

              <el-button v-if="attendanceDetail.status == 1" type="text"
                @click="editAttendanceDetail(row, 'clockDays')">{{ row.clockDays
                }}</el-button>
              <div v-else>{{ row.clockDays }}</div>

              <img v-if="row.clockDaysLog && row.clockDaysLog.length > 0" class="more-icon"
                @click="showMore(row.clockDaysLog)" src="@/assets/image/more.png" />
            </div>
          </template>
        </el-table-column>
        <el-table-column align="center" label="全卡天数" prop="fullClockDays">
          <template v-slot="{ row }">
            <div class="center-view">

              <el-button v-if="attendanceDetail.status == 1" type="text"
                @click="editAttendanceDetail(row, 'fullClockDays')">{{ row.fullClockDays
                }}</el-button>
              <div v-else>{{ row.fullClockDays }}</div>

              <img v-if="row.fullClockDaysLog && row.fullClockDaysLog.length > 0" class="more-icon"
                @click="showMore(row.fullClockDaysLog)" src="@/assets/image/more.png" />
            </div>
          </template>
        </el-table-column>
        <el-table-column align="center" label="0卡天数" prop="zeroClockDays">
          <template v-slot="{ row }">
            <div class="center-view">

              <el-button v-if="attendanceDetail.status == 1" type="text"
                @click="editAttendanceDetail(row, 'zeroClockDays')">{{ row.zeroClockDays
                }}</el-button>
              <div v-else>{{ row.zeroClockDays }}</div>

              <img v-if="row.zeroClockDaysLog && row.zeroClockDaysLog.length > 0" class="more-icon"
                @click="showMore(row.zeroClockDaysLog)" src="@/assets/image/more.png" />
            </div>
          </template>
        </el-table-column>
        <el-table-column align="center" label="异常天数" prop="abnormalDays">
          <template v-slot="{ row }">
            <div class="center-view">

              <el-button v-if="attendanceDetail.status == 1" type="text"
                @click="editAttendanceDetail(row, 'abnormalDays')">{{ row.abnormalDays
                }}</el-button>
              <div v-else>{{ row.abnormalDays }}</div>

              <img v-if="row.abnormalDaysLog && row.abnormalDaysLog.length > 0" class="more-icon"
                @click="showMore(row.abnormalDaysLog)" src="@/assets/image/more.png" />
            </div>
          </template>
        </el-table-column>
        <el-table-column align="center" label="旷工天数" prop="absenteeDays">
          <template v-slot="{ row }">
            <div class="center-view">

              <el-button v-if="attendanceDetail.status == 1" type="text"
                @click="editAttendanceDetail(row, 'absenteeDays')">{{ row.absenteeDays
                }}</el-button>
              <div v-else>{{ row.absenteeDays }}</div>

              <img v-if="row.absenteeDaysLog && row.absenteeDaysLog.length > 0" class="more-icon"
                @click="showMore(row.absenteeDaysLog)" src="@/assets/image/more.png" />
            </div>
          </template>
        </el-table-column>
        <el-table-column align="center" :label="'缺卡次数（减去补卡'+blueNumber1+'次/月）'" prop="missClockNumber">
          <template v-slot="{ row }">
            <div class="center-view">

              <el-button v-if="attendanceDetail.status == 1" type="text"
                @click="editAttendanceDetail(row, 'missClockNumber')">{{ row.missClockNumber
                }}</el-button>
              <div v-else>{{ row.missClockNumber }}</div>

              <img v-if="row.missClockNumberLog && row.missClockNumberLog.length > 0" class="more-icon"
                @click="showMore(row.missClockNumberLog)" src="@/assets/image/more.png" />
            </div>
          </template>
        </el-table-column>
        <el-table-column align="center" label="迟到次数" prop="lateNumber">
          <template v-slot="{ row }">
            <div class="center-view">

              <el-button v-if="attendanceDetail.status == 1" type="text"
                @click="editAttendanceDetail(row, 'lateNumber')">{{ row.lateNumber
                }}</el-button>
              <div v-else>{{ row.lateNumber }}</div>

              <img v-if="row.lateNumberLog && row.lateNumberLog.length > 0" class="more-icon"
                @click="showMore(row.lateNumberLog)" src="@/assets/image/more.png" />
            </div>
          </template>
        </el-table-column>
        <el-table-column align="center" label="早退次数" prop="earlyLeaveNumber">
          <template v-slot="{ row }">
            <div class="center-view">

              <el-button v-if="attendanceDetail.status == 1" type="text"
                @click="editAttendanceDetail(row, 'earlyLeaveNumber')">{{ row.earlyLeaveNumber
                }}</el-button>
              <div v-else>{{ row.earlyLeaveNumber }}</div>

              <img v-if="row.earlyLeaveNumberLog && row.earlyLeaveNumberLog.length > 0" class="more-icon"
                @click="showMore(row.earlyLeaveNumberLog)" src="@/assets/image/more.png" />
            </div>
          </template>
        </el-table-column>
        <el-table-column align="center" label="加班时长" prop="overtimeHours">
          <template v-slot="{ row }">
            <div class="center-view">

              <el-button v-if="attendanceDetail.status == 1" type="text"
                @click="editAttendanceDetail(row, 'overtimeHours')">{{ row.overtimeHours
                }}</el-button>
              <div v-else>{{ row.overtimeHours }}</div>

              <img v-if="row.overtimeHoursLog && row.overtimeHoursLog.length > 0" class="more-icon"
                @click="showMore(row.overtimeHoursLog)" src="@/assets/image/more.png" />
            </div>
          </template>
        </el-table-column>
        <el-table-column align="center" label="请假时长" prop="leaveHours">
          <template v-slot="{ row }">
            <div class="center-view">

              <el-button v-if="attendanceDetail.status == 1" type="text"
                @click="editAttendanceDetail(row, 'leaveHours')">{{ row.leaveHours
                }}</el-button>
              <div v-else>{{ row.leaveHours }}</div>

              <img v-if="row.leaveHoursLog && row.leaveHoursLog.length > 0" class="more-icon"
                @click="showMore(row.leaveHoursLog)" src="@/assets/image/more.png" />
            </div>
          </template>
        </el-table-column>
        <el-table-column align="center" label="调休时长" prop="adjustmentHours">
          <template v-slot="{ row }">
            <div class="center-view">

              <el-button v-if="attendanceDetail.status == 1" type="text"
                @click="editAttendanceDetail(row, 'adjustmentHours')">{{ row.adjustmentHours
                }}</el-button>
              <div v-else>{{ row.adjustmentHours }}</div>

              <img v-if="row.adjustmentHoursLog && row.adjustmentHoursLog.length > 0" class="more-icon"
                @click="showMore(row.adjustmentHoursLog)" src="@/assets/image/more.png" />
            </div>
          </template>
        </el-table-column>




      </el-table>
      <!-- 分页 -->
      <Pagination class="bt" v-model:limit="queryAttendanceDetailParams.size" v-model:page="queryAttendanceDetailParams.current"
        :total="attendanceDetailTotal" @pagination="getAttendanceDetailPage" />
       <div style="height:60px;"></div> 




      <template #footer>
        <el-button v-if="attendanceDetail.status == 1" @click="attendanceDetailVisible = false">取 消</el-button>
        <el-button v-if="attendanceDetailList.length > 0 && attendanceDetail.status == 1" type="primary"
          @click="handleConfirm()">确 定</el-button>

        <el-button v-if="attendanceDetailList.length > 0 && attendanceDetail.status == 2" @click="auditRejected()">
          审核拒绝</el-button>
        <el-button v-if="attendanceDetailList.length > 0 && attendanceDetail.status == 2" type="primary"
          @click="auditConfirm()">审核通过</el-button>

<!-- workAttendanceAudit -->

      </template>
    </el-dialog>


    <el-dialog title="修改考勤" v-model="editAttendanceDetailVisible" width="500" top="30vh" destroy-on-close>

      <el-form :inline="false" :rules="rules" ref="editAttendanceDetailFormRef" :model="editAttendanceDetailForm"
        label-width="120px">
        <el-form-item v-if="dynamicField.valueName" :label="dynamicField.name" :prop="dynamicField.value"
          :required="true">

          <el-input v-model="editAttendanceDetailForm[dynamicField.valueName]" type="number"
            :placeholder="'请输入' + dynamicField.name" class="!w-220px" />

        </el-form-item>
        <el-form-item label="修改原因" prop="remark">

          <el-input v-model="editAttendanceDetailForm.remark" placeholder="请输入修改原因" class="!w-220px" />

        </el-form-item>
        <el-form-item label="上传图片" prop="pic">

          <UploadImg v-model="editAttendanceDetailForm.pic" draggable="false" height="80px" width="100%"
            class="min-w-80px" />


        </el-form-item>




      </el-form>


      <template #footer>
        <el-button @click="editAttendanceDetailVisible = false">取 消</el-button>
        <el-button type="primary" @click="handleEditAttendanceDetailConfirm()">确 定</el-button>
      </template>
    </el-dialog>


    <el-dialog title="修改记录" v-model="editRecordVisible" width="1000" top="30vh" destroy-on-close>
      <el-table :data="editRecordList">
        <el-table-column label="序号" type="index" width="80"></el-table-column>
        <el-table-column align="center" label="员工姓名" prop="name" />
        <el-table-column align="center" label="手机号" prop="tel" />
        <el-table-column align="center" label="修改人" prop="createUser" />
        <el-table-column align="center" label="修改时间" prop="createTime" />
        <el-table-column align="center" label="修改原因" prop="remark" />
        <el-table-column align="center" label="修改图片" prop="pic">
          <template v-slot="{ row }">
            
            <el-image v-if="row.pic" :src="row.pic"  :preview-src-list="[row.pic]" fit="cover" width="100px" height="100px" />
            
          </template>

        </el-table-column>
        
      </el-table>




      <template #footer>
        <el-button @click="editRecordVisible = false">返 回</el-button>

      </template>
    </el-dialog>






  </div>
</template>
<script lang='ts' setup>
import { getWorkAttendancePage, workAttendanceAdd, deleteWorkAttendance, getWorkAttendanceOperatePage, getWorkAttendanceDetailPage, workAttendanceAudit, workAttendanceUpdateDetail } from '@/api/system/commonAttendance'
import { ref, reactive } from 'vue'
import { useRoute } from 'vue-router'
import { ElMessageBox } from 'element-plus'
const message = useMessage()
import * as commonAttendanceApi from '@/api/system/commonAttendance'

/**
 * 路由对象
 */
const route = useRoute()


onMounted(() => {
  getList()
})
/**
 * 数据部分
 */
const queryFormRef = ref()
const list = ref([])
const total = ref(0)
const createdTime = ref([])
const queryParams = reactive({
  current: 1,
  size: 10,
  month: undefined,
  startDate: undefined,
  endDate: undefined,
  status: undefined
})
// 搜索
const handleSearch = () => {
  queryParams.current = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  createdTime.value = []
  selectCreatedTime(null)
  queryFormRef.value.resetFields()

  handleSearch()
}

const getList = async () => {
  const res = await getWorkAttendancePage(queryParams)
  list.value = res.data.list

  total.value = res.data.total
}

const selectCreatedTime = async (val) => {
  console.log("selectCreatedTime", val)
  if (val) {
    queryParams.startDate = val[0]
    queryParams.endDate = val[0]
  } else {
    queryParams.startDate = undefined
    queryParams.endDate = undefined
  }


}

const fileOpen = (row) => {
  // 创建一个新的Blob对象，并设置其MIME类型
  fetch(row.fileUrl)
    .then(response => response.blob())
    .then(blob => {
      // 创建一个指向该Blob的URL
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a')
      link.href = url
      link.download = row.month + '.' + row.fileUrl.split(".").pop(); // 设置下载后的文件名
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      URL.revokeObjectURL(url) // 释放创建的URL
    })
    .catch(error => message.error('下载文件失败:' + error))
}



// 新增/编辑考勤标题
const addAttendanceVisible = ref(false)
const addAttendanceForm = reactive({
  month: '',
  url: "",
})

const handleAdd = async () => {
  addAttendanceForm.month = ""
  addAttendanceForm.url = ""
  addAttendanceVisible.value = true
}


const addAttendanceSubmit = async () => {
  if (addAttendanceForm.month == "") {
    message.error("请选择考勤月份")
    return
  }
  if (addAttendanceForm.url == "") {
    message.error("请上传考勤")
    return
  }

  workAttendanceAdd(addAttendanceForm).then(res => {
    if (res.code == 0) {
      message.success("新增成功")
      addAttendanceVisible.value = false
      getList()
    } else {
      message.error("新增失败")
    }
  }).catch(err => {
    message.error("新增失败")
  })
}

const deleteAttendance = async (id) => {
  // 添加确认弹框
  ElMessageBox.confirm('确定要删除这条考勤信息吗?', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    // 用户点击确定后执行删除操作
    const res = await deleteWorkAttendance(id);
    if (res.code === 0) {
      message.success('删除成功');
      getList();
    } else {
      message.error('删除失败');
    }
  }).catch(() => {
    // 用户点击取消后执行的操作
  });
};


const operationRecordVisible = ref(false)
const operationRecordParams = reactive({
  current: 1,
  size: 10,
  attendanceId: undefined,
})
const operationRecordList = ref([])
const operationRecordTotal = ref(0)
const getOperationRecordList = async () => {
  const res = await getWorkAttendanceOperatePage(operationRecordParams)
  operationRecordList.value = res.data.list
  operationRecordTotal.value = res.data.total
}

const showOperationRecord = (id) => {
  operationRecordParams.attendanceId = id
  operationRecordParams.current = 1
  getOperationRecordList()
  operationRecordVisible.value = true
}



const attendanceDetailVisible = ref(false)
const queryAttendanceDetailFormRef = ref()
const queryAttendanceDetailParams = reactive({
  current: 1,
  size: 10,
  attendanceId: undefined,
  name: undefined,
  tel: undefined,
})

const attendanceDetail = ref({})

const showAttendanceDetail = (val) => {

  attendanceDetail.value = val
  queryAttendanceDetailParams.attendanceId = val.id
  queryAttendanceDetailParams.current = 1
  attendanceDetailList.value = []
  getAttendanceDetailPage()
  attendanceDetailVisible.value = true
}


const attendanceDetailList = ref([])
const attendanceDetailTotal = ref(0)
const getAttendanceDetailPage = async () => {
  const res = await getWorkAttendanceDetailPage(queryAttendanceDetailParams)
  attendanceDetailList.value = res.data.list
  attendanceDetailTotal.value = res.data.total
}

const handleAttendanceDetailSearch = () => {
  queryAttendanceDetailParams.current = 1
  getAttendanceDetailPage()
}

const resetAttendanceDetailQuery = () => {
  queryAttendanceDetailFormRef.value.resetFields()
  queryAttendanceDetailParams.name = undefined
  queryAttendanceDetailParams.tel = undefined
  handleAttendanceDetailSearch()
}



const editAttendanceDetailVisible = ref(false)
const editAttendanceDetailForm = reactive({
  id: undefined,
  pic: undefined,
  remark: undefined,
})

const dynamicField = ref({
  name: '',
  valueName: "",
  value: null
})

const rules = {
  remark: [
    { required: true, message: '请输入修改原因', trigger: 'blur' },
  ],
};


const editAttendanceDetail = (val, field) => {
  dynamicField.value.valueName = field
  switch (field) {
    case "requiredAttendanceDays":
      dynamicField.value.name = "应出勤天数"
      break;
    case "actualAttendanceDays":
      dynamicField.value.name = "实际出勤天数"
      break;
    case "leaveDays":
      dynamicField.value.name = "事假天数"
      break;
    case "sickDays":
      dynamicField.value.name = "病假天数"
      break;
    case "paidDays":
      dynamicField.value.name = "带薪假天数"
      break;
    case "clockDays":
      dynamicField.value.name = "有卡天数"
      break;
    case "fullClockDays":
      dynamicField.value.name = "全卡天数"
      break;
    case "zeroClockDays":
      dynamicField.value.name = "0卡天数"
      break;
    case "abnormalDays":
      dynamicField.value.name = "异常天数"
      break;

    case "missClockNumber":
      dynamicField.value.name = "缺卡次数"
      break;
    case "lateNumber":
      dynamicField.value.name = "迟到次数"
      break;
    case "earlyLeaveNumber":
      dynamicField.value.name = "早退次数"
      break;
    case "overtimeHours":
      dynamicField.value.name = "加班时长"
      break;
    case "leaveHours":
      dynamicField.value.name = "请假时长"
      break;
    case "adjustmentHours":
      dynamicField.value.name = "调休时长"
      break;
    case "absenteeDays":
      dynamicField.value.name = "旷工天数"
      break;
  }

  editAttendanceDetailForm.id = val.id
  editAttendanceDetailForm.remark = undefined
  editAttendanceDetailForm.pic = undefined


  Object.keys(editAttendanceDetailForm).forEach((key) => {
    if (!['id', 'pic', 'remark'].includes(key)) {
      delete editAttendanceDetailForm[key];
    }
  });

  editAttendanceDetailForm[dynamicField.value.valueName] = val[dynamicField.value.valueName]

  editAttendanceDetailVisible.value = true


}

const editAttendanceDetailFormRef = ref()
const handleEditAttendanceDetailConfirm = async () => {

  await editAttendanceDetailFormRef.value.validate((valid, fields) => {
    if (valid) {
      console.log("editAttendanceDetailForm=", editAttendanceDetailForm)

      workAttendanceUpdateDetail(editAttendanceDetailForm).then(res => {
        if (res.code == 0) {
          message.success("修改成功")
          editAttendanceDetailVisible.value = false
          getAttendanceDetailPage()
        } else {
          message.error("修改失败")
        }
      }).catch(err => {
        message.error("修改失败")
      })





    } else {
      console.log('error submit!', fields)
    }
  })

}


const editRecordVisible = ref(false)
const editRecordList = ref([])




const showMore = (list) => {
  editRecordList.value = list
  editRecordVisible.value = true


}




const handleConfirm = () => {

  if (attendanceDetail.value.status == 1) {
    workAttendanceAudit({
      attendanceId: attendanceDetail.value.id, type: 2
    }).then(res => {

      if (res.code == 0) {
        message.success("提交成功")
        attendanceDetailVisible.value = false
        getList()
      } else {
        message.error("提交失败")
      }


    })


  }

  // workAttendanceAudit


}




const auditRejected = () => {
  ElMessageBox.prompt('请输入驳回原因:', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
    inputValidator: (value) => {
      if (!value) {
        return '请输入驳回原因';
      }
      return true;
    }
  }).then(async ({ value }) => {
    // 用户点击确定后执行驳回操作，value 是用户输入的拒绝原因
    const res = await workAttendanceAudit({
      attendanceId: attendanceDetail.value.id,
      type: 4,
      remark: value // 将拒绝原因传递给接口
    });
    if (res.code === 0) {
      message.success('驳回成功');
      attendanceDetailVisible.value = false;
      getList();
    } else {
      message.error('驳回失败');
    }
  }).catch(() => {
    // 用户点击取消后执行的操作
  });
}

const auditConfirm = () => {
  ElMessageBox.confirm('确定要通过这条考勤信息吗?', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    // 用户点击确定后执行通过操作
    const res = await workAttendanceAudit({
      attendanceId: attendanceDetail.value.id, type: 3
    });
    if (res.code === 0) {
      message.success('通过成功');
      attendanceDetailVisible.value = false
      getList();
    } else {
      message.error('通过失败');
    }
  }).catch(() => {
    // 用户点击取消后执行的操作
  });

}


const blueNumber1 = ref(0)
const getRule = async () => {
  
  const { data } = await commonAttendanceApi.getSalaryAttendanceRule({attendanceType:3})
  console.log("data=", data)
  blueNumber1.value = data.blueNumber1
  
}

getRule()



</script>
<style scoped lang='less'>
.app-main {
  padding: 20px;
  border-radius: 10px;
  background-color: #fff;
  min-height: calc(100vh - 150px);
}

.more-icon {
  width: 20px;
  height: 20px;
  margin-left: 5px;
  cursor: pointer;
}
</style>