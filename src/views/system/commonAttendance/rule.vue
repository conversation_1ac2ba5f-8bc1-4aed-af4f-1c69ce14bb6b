<template>
  <div class="app-main">
    <h3>考勤规则设置 </h3>
    <el-tabs v-model="attendanceType" @tab-change="handleTabChange" v-loading="loading">
      <el-tab-pane label="迟到" :name="1">
        <el-card shadow="never">
          <template #header>
            <span class="card-header">迟到规则设置</span>
          </template>
          <div>
            <div class="rule-line">
              <el-input v-model="ruleForm.blueNumber1" placeholder="起始时间"></el-input>分钟 < 迟到时间 ≤ <el-input
                v-model="ruleForm.blueNumber2" placeholder="结束时间"></el-input>分钟，当日延迟<el-input
                  v-model="ruleForm.blueNumber3" placeholder="延迟时间"></el-input>小时下班后不做考勤扣款
            </div>
            <div class="rule-line">
              <el-input v-model="ruleForm.blueNumber4" placeholder="起始时间"></el-input>分钟 < 迟到时间 ≤<el-input
                v-model="ruleForm.blueNumber5" placeholder="结束时间"></el-input>分钟，当日延迟<el-input
                  v-model="ruleForm.blueNumber6" placeholder="延迟时间"></el-input>小时下班后不做考勤扣款
            </div>
            <div class="rule-line">
              <el-input v-model="ruleForm.blueNumber7" placeholder="起始时间"></el-input>分钟 < 迟到时间 ≤<el-input
                v-model="ruleForm.blueNumber8" placeholder="结束时间"></el-input>分钟，当日延迟<el-input
                  v-model="ruleForm.blueNumber9" placeholder="延迟时间"></el-input>小时下班后不做考勤扣款
            </div>
            <div class="rule-line">
              <el-input v-model="ruleForm.blueNumber10" placeholder="起始时间"></el-input>分钟封顶，员工迟到未加班者，扣款，迟到半小时以上未请假按事假<el-input
                v-model="ruleForm.blueNumber11" placeholder="天"></el-input>天处理
            </div>
            
            <el-row>
              <el-button @click="submit()" type="primary">保存</el-button>
            </el-row>
          </div>
        </el-card>
      </el-tab-pane>
      <el-tab-pane label="早退" :name="2">
        <el-card shadow="never">
          <template #header>
            <span class="card-header">早退规则设置</span>
          </template>
          <div>
            <div class="rule-line">员工在规定下班之前，<el-input v-model="ruleForm.blueNumber1"
                placeholder=""></el-input>分钟（含）之内打卡而未请假者，视为早退。并扣款</div>
            <div class="rule-line">员工在规定下班之前，<el-input v-model="ruleForm.blueNumber2"
                placeholder=""></el-input>分钟以上而未请假者，且未走任何请假流程者。按事假<el-input v-model="ruleForm.blueNumber3"
                placeholder=""></el-input>天处理。</div>
            <el-row>
              <el-button @click="submit()" type="primary">保存</el-button>
            </el-row>
          </div>
        </el-card>
      </el-tab-pane>
      <el-tab-pane label="补卡" :name="3">
        <el-card shadow="never">
          <template #header>
            <span class="card-header">补卡规则设置</span>
          </template>
          <div>
            <div class="rule-line">员工每月有<el-input v-model="ruleForm.blueNumber1"
                placeholder="起始时间"></el-input>次补卡机会（考勤计算中自动免去），员工月度缺卡<el-input v-model="ruleForm.blueNumber1"
                placeholder="起始时间"></el-input>次以上考勤扣款</div>
            <div class="rule-line">备注：缺卡或迟到一次扣款50元</div>
            <el-row>
              <el-button @click="submit()" type="primary">保存</el-button>
            </el-row>
          </div>
        </el-card>
      </el-tab-pane>
      <el-tab-pane label="审批截止时间" :name="4">
        <el-card shadow="never">
          <template #header>
            <span class="card-header">审批截止时间设置</span>
          </template>
          <div>
            <div class="rule-line">*加班、出差、值班、调休、请假等事项统计办办相关审批流程，审批流程截止时间为次月<el-input v-model="ruleForm.blueNumber1"
                placeholder="起始时间"></el-input>号（包含当天，已提交时间为准，不考虑审核状态。）</div>
            <el-row>
              <el-button @click="submit()" type="primary">保存</el-button>
            </el-row>
          </div>
        </el-card>
      </el-tab-pane>
      <el-tab-pane label="请假调休" :name="5">
        <el-card shadow="never">
          <template #header>
            <span class="card-header">请假调休</span>
          </template>
          <div>
            <div class="rule-line">
              所有员工请假或调休，必须当天前提交办办流程，方可执行请假或调休，若员工未在规定时间内提交流程，私自休假，则按照旷工进行计算，请假最低1小时起步，不足1小时按照1小时计算。
            </div>
          </div>
        </el-card>
      </el-tab-pane>
      <el-tab-pane label="旷工" :name="6">
        <el-card shadow="never">
          <template #header>
            <span class="card-header">旷工规则设置</span>
          </template>
          <div>
            <div class="rule-line">1.旷工1天扣除2天工资，旷工2天扣除3天工资，连续旷工3天以上（含），则视为严重违纪，视为自动解除与公司的劳动关系且公司不支付任何补偿金/赔偿金。</div>
            <div class="rule-line">2.员工无故迟到、早退或擅自离岗半小时以上（含半小时）至2小时以内，按旷工半天处理，超过2小时（含2小时）按旷工一天处理。</div>
          </div>
        </el-card>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>
<script lang='ts' setup>
import { ref, reactive } from 'vue';
import { useRoute } from 'vue-router';

import * as commonAttendanceApi from '@/api/system/commonAttendance'

const message = useMessage() // 消息弹窗

const loading = ref(false) // 加载状态

// console.log("commonAttendanceApi=", commonAttendanceApi)
// getSalaryAttendanceRule

const attendanceType = ref(1)
const ruleForm = ref({
  attendanceType: 1,//考勤类型: 1-> 迟到, 2->早退, 3->补卡, 4->审批截止时间, 5->请假调休, 6->旷工
  blueNumber1: 0,
  blueNumber2: 0,
  blueNumber3: 0,
  blueNumber4: 0,
  blueNumber5: 0,
  blueNumber6: 0,
  blueNumber7: 0,
  blueNumber8: 0,
  blueNumber9: 0,
  blueNumber10: 0,
  blueNumber11: 0,
  blueNumber12: 0
})
const getRule = async () => {
  loading.value = true
  const { data } = await commonAttendanceApi.getSalaryAttendanceRule({attendanceType:attendanceType.value})
  // console.log("data=", data)
  loading.value = false
  ruleForm.value = data
}

onMounted(() => {
  getRule()
})

const handleTabChange = (val: any) => {
  // attendanceType.value = Number(tabName)
  getRule()
  
}

const submit = async () => {
  console.log("ruleForm.value=", ruleForm.value)
  loading.value = true
  const res = await commonAttendanceApi.salaryAttendanceRuleUpdate(ruleForm.value)
  console.log("salaryAttendanceRuleUpdate res=", res)
  loading.value = false
  if(res.code === 0){
    
    message.success('保存成功')

     getRule()
  }

 
  // ruleForm.value = data
}


</script>
<style lang="less">
.app-main {
  .el-card__header {
    padding: 10px;
  }
}

.rule-line {
  display: flex;
  align-items: center;
  font-size: 13px;
  margin-bottom: 10px;

  .el-input {
    width: 80px;
    margin: 0px 10px;
  }
}
</style>
<style scoped lang='less'>
.app-main {
  padding: 20px;
  border-radius: 10px;
  background-color: #fff;
}

.card-header {
  font-size: 15px;
  font-weight: bold;
  line-height: 16px;
}
</style>