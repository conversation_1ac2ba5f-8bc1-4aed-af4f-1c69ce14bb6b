<template>
  <div style="background:#fff;padding:20px">
    <el-row :gutter="20">
      <!-- 左侧部门树 -->
      <el-col :span="8" :xs="24">
        <!-- <div class="content_box">
      <FullCalendar :options="calendarOptions1"  v-if="calendarOptions1" />
    </div> -->
      </el-col>
      <el-col :span="24" :xs="24">
        <div class="content_box1">
          <!-- <FullCalendar  :options="calendarOptions"  v-if="calendarOptions" /> -->
        </div>
      </el-col>
    </el-row>

    <el-row :gutter="20">
      <!-- 左侧部门树 -->
      <el-col :span="4" :xs="24">
        <div class="leftCal">
          <vue-cal
            xsmall
            :time="false"
            hide-view-selector
            locale="zh-cn"
            active-view="month"
            :disable-views="['years', 'year', 'week', 'day']"
            @cell-focus="selectedDate = $event"
            class="vuecal--date-picker"
            style="max-width: 270px; height: 290px"
            :events="eventsList"
          />
        </div>
      </el-col>
      <el-col :span="20" :xs="24">
        <!-- <el-button  >更多选项</el-button> -->
        <div class="rightCal">
          <vue-cal
            :disable-views="['years', 'year']"
            active-view="month"
            style="height: 600px"
            locale="zh-cn"
            :selected-date="selectedDate"
            :time-step="30"
            @cell-click="logEvents($event)"
            @view-change="changeTimes($event)"
            :time-from="8 * 60"
            :time-to="19 * 60"
            :events="eventsList"
            events-on-month-view="short"
            :on-event-click="onEventClick"
            @event-delete="deleteEvents('event-delete', $event)"
          />
        </div>
      </el-col>
    </el-row>
  </div>
  <newSchedule ref="schedule" @success="getDateList" />
  <detailedSchedule ref="dSchedule" @success="getDateList" />
</template>
<script lang="ts" setup>
import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'
import { dateFormatter } from '@/utils/formatTime'
import download from '@/utils/download'
import * as RoleApi from '@/api/system/role'
//
import newSchedule from './newSchedule.vue'
import detailedSchedule from './detailedSchedule.vue'
import FullCalendar from '@fullcalendar/vue3'
import dayGridPlugin from '@fullcalendar/daygrid'
import timeGridPlugin from '@fullcalendar/timegrid'
import interactionPlugin from '@fullcalendar/interaction'

import VueCal from 'vue-cal'
import 'vue-cal/dist/vuecal.css'
import dayjs from 'dayjs'

defineOptions({ name: 'SystemRole' })

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化

const loading = ref(true) // 列表的加载中
const selectedEvent = ref({})
const eventsList = ref([])
const queryFormRef = ref() // 搜索的表单
const formData = ref({
  // userId: 1,
  // startTime: '2024-03-13 10:00',
  // endTime: '2024-03-13 11:00'
  startTime: '',
  endTime: ''
})

//新的  开始
const selectedDate = ref(null)
/** 查询日历事件 */
const getDateList = async () => {
  loading.value = true
  try {
    const data = await RoleApi.getDateList(formData.value)
    console.log(data)

    eventsList.value = data.map((item, i) => {
      return {
        start: item.eventStartTime,
        end: item.eventEndTime,
        title: item.eventName,
        class: 'health',
        id: item.id,
        type: item.type
      }
    })
  } finally {
    loading.value = false
  }
}
const schedule = ref()
const dSchedule = ref()
// 日历添加日程事件
const logEvents = (val) => {
  console.log(val)
  schedule.value.open(val)
}
const changeTimes = (val) => {
  let newStartDate = dayjs(val.startDate).format('YYYY-MM-DD')
  let newEndDate = dayjs(val.endDate).format('YYYY-MM-DD')
  formData.value.startTime = newStartDate
  formData.value.endTime = newEndDate
  getDateList()
}

// 查看日程详细
const onEventClick = (val) => {
  console.log(val)
  dSchedule.value.open(val)
}
// 删除日程详细
const deleteEvents = (i,val) => {
  console.log(val,'删除 测')
}

//结束

const matchList = ref<any>([]) //日历数据
const fullcalendarref = ref() //实例

// const handleClick = async (val) => {
//   console.log(val)
//   schedule.value.open(val)
// }
// const eventClick = (val) => {
//   console.log(val, 'eventClickeventClick')
// }

//这个calendarOptions是配置项 这里只是部分配置项
// const calendarOptions = ref({
//   plugins: [dayGridPlugin, interactionPlugin, timeGridPlugin], //插件  我目前用的是月视图插件
//   initialView: 'dayGridMonth', // 默认为那个视图（月：dayGridMonth，周：timeGridWeek，日：timeGridDay）
//   height: '780px',
//   locale: 'zh-cn', //语言汉化
//   firstDay: 1, // 设置一周中显示的第一天是哪天，周日是0，周一是1，类推
//   editable: true, //事件是否可编辑，可编辑是指可以移动, 改变大小等。
//   droppable: true, //是否可拖拽
//   headerToolbar: {
//     left: 'prev,next today',
//     center: 'title',
//     right: 'dayGridMonth,timeGridWeek,timeGridDay'
//   },
//   buttonText: {
//     today: '回到今天',
//     day: '日',
//     month: '月',
//     week: '周'
//   },
//   // 日程
//   businessHours: {
//     daysOfWeek: [1, 2, 3, 4], // Monday - Thursday
//     startTime: '10:00', // a start time (10am in this example)
//     endTime: '18:00' // an end time (6pm in this example)
//   },
//   // 强调日历上的特定时间段。默认为周一至周五，上午9点至下午5点。
//   selectConstraint: {
//     daysOfWeek: [1, 2, 3, 4], // Monday - Thursday
//     startTime: '10:00', // a start time (10am in this example)
//     endTime: '18:00' // an end time (6pm in this example)
//   },
//   dateClick: handleClick, //点击具体日期单元格时触发的事件
//   eventClick: eventClick, //点击具体日程events时触发的事件
//   aspectRatio: '0.1',
//   //   dropAccept: ".eventListItems", //可被拖进
//   // initialDate: dayjs().format('YYYY-MM-DD'), // 自定义设置背景颜色时一定要初始化日期时间
//   events: matchList.value //绑定展示事件
// })
// const init = async () => {
//   matchList.value = []
//   // const res = await getCalendarEvent({
//   //   status: '', //状态
//   //   bgColor: '', //背景颜色
//   // });
//   // let evenstList = res.map((item: any) => {
//   //   item.title = item.title;
//   //   item.eventId = item.id;
//   //   item.start = item.start_time;
//   //   item.end = item.end_time;
//   //   return item;
//   // });
//   //目前的数据是用死的
//   let evenstList = [
//     {
//       id: '0',
//       title: '节假日',
//       start: '2024-01-25',
//       end: '2024-01-25'
//     },
//     {
//       id: '1',
//       title: '双休日',
//       start: '2024-01-27',
//       end: '2024-01-27'
//     },
//     {
//       id: '2',
//       title: '双休日',
//       start: '2024-01-28',
//       end: '2024-01-28'
//     }
//   ]

//   matchList.value = [...evenstList]
//   calendarOptions.value.events = matchList.value
// }

// let calendarOptions = ref()
/** 初始化 **/
onMounted(() => {
  // init()
  getDateList()
})
</script>
<style  scoped>
/deep/ .fc .fc-non-business {
  background: transparent;
}
/deep/ .fc .fc-daygrid-day.fc-day-today {
  background: #e0f1ff !important;
}
/deep/.fc-next-button {
  margin-left: 10px;
}
/deep/ .fc-button:focus {
  border: none !important;
  box-shadow: none !important;
}
/deep/ .fc-icon-chevron-right {
  color: #333;
}
/deep/ .fc-icon-chevron-left {
  color: #333;
}
/deep/ .fc-today-button {
  background: transparent !important;
  border-color: transparent !important;
  color: #333 !important;
}
/deep/ .fc-today-button:hover {
  background: #eaeced !important;
}
/deep/.fc .fc-button-primary:disabled {
  background: transparent !important;
}
/deep/ .fc-prev-button {
  background: transparent;
  border-color: transparent;
}
/deep/ .fc-prev-button:hover {
  background: #eaeced !important;
  border: none;
  box-shadow: none;
}
/deep/ .fc-next-button {
  background: transparent;
  border-color: transparent;
}

/deep/ .fc-next-button:hover {
  background: #eaeced !important;
  border: none;
  box-shadow: none;
}

/* :deep  .vuecal--rounded-theme.vuecal--blue-theme:not(.vuecal--day-view) .vuecal__cell--today .vuecal__cell-content{
  background-color: #0089FF !important;
  color: #fff;
} */

:deep
  .leftCal
  .vuecal--date-picker:not(.vuecal--day-view)
  .vuecal__cell--today
  .vuecal__cell-content {
  border-color: #0089ff !important;
}

:deep
  .leftCal
  .vuecal--date-picker:not(.vuecal--day-view)
  .vuecal__cell--selected
  .vuecal__cell-content {
  background-color: #0089ff !important;
  color: #fff;
  border-color: transparent;
}

/* :deep .vuecal__cell--today{
  background-color: #0089FF !important;
} */
/* :deep.vuecal--blue-theme .vuecal__title-bar {
    background-color: #F5F7F9;
} */
/* :deep.vuecal--rounded-theme.vuecal--blue-theme:not(.vuecal--day-view) .vuecal__cell-content{
    background-color: transparent;
} */
/* 
:deep .vuecal--rounded-theme.vuecal--blue-theme:not(.vuecal--day-view) .vuecal__cell--selected .vuecal__cell-content{
  background-color: #0089FF !important;
  color: #fff;
} */
/* :deep .rightCal .vuecal__cell--selected {  新的改动
  background-color: #e0f1ff !important;
} */
/* :deep .rightCal .vuecal__cell--today, .vuecal__cell--current{
  border: 1px solid #42B982;
} */

:deep .vuecal__title-bar {
  background-color: transparent;
}
:deep .vuecal__view-btn--active {
  border-color: #0089ff;
}
:deep .vuecal__menu {
  background-color: transparent;
}
:deep .rightCal .vuecal__cell--selected {
  background: #e0f1ff;
}
:deep .rightCal .vuecal__cell--today {
  /* background-color: #f1faf7; */
  background-color: rgba(51, 112, 255, 0.1);
}

:deep .rightCal .vuecal__event.leisure {
  background-color: rgba(253, 156, 66, 0.9);
  border: 1px solid rgb(233, 136, 46);
  color: #fff;
}
:deep .rightCal .vuecal__event.health {
  /* background-color: rgba(164, 230, 210, 0.9); */
  /* border: 1px solid rgb(144, 210, 190); */
  background-color: rgba(51, 112, 255, 0.1);
  border: 1px solid rgba(51, 112, 255, 0.1);
  /* border: 1px solid #E0E0E2;  */

  margin-bottom: 6px;
  cursor: pointer;
}
:deep .rightCal .vuecal__event.sport {
  background-color: rgba(255, 102, 102, 0.9);
  border: 1px solid rgb(235, 82, 82);
  color: #fff;
}
:deep .rightCal .vuecal__cell-content {
  align-self: flex-start;
}
/* :deep .rightCal .vuecal__cell-date {
  text-align: right;
  padding: 4px;
} */

:deep .rightCal .vuecal__cell--selected:before {
  /* border-color: rgba(66, 185, 131, 0.5); */
  border-color: rgba(51, 112, 255, 0.1);
}

:deep .leftCal .vuecal__cell-events-count {
  width: 4px;
  min-width: 0;
  height: 4px;
  padding: 0;
  color: transparent;
  margin-top: 11px;
}
:deep.vuecal--short-events .vuecal__event-title {
  position: relative;
  padding-left: 20px;

  /* background: rgba(51,112,255,0.1); */
  border-radius: 4px;
  color: #303133;
}
:deep .vuecal--short-events .vuecal__event-title::before {
  content: '';
  position: absolute;
  left: 5%;
  top: 50%;
  transform: translateY(-50%);
  width: 5px;
  height: 5px;
  border-radius: 50%;
  background-color: #3370ff;
}
:deep .rightCal .vuecal__flex {
  /* background: #FFFFFF; */
}
:deep .rightCal .vuecal__cell:before {
  /* border: 1px solid red !important; */
}
/* :deep .vuecal__cell--has-events {
  background-color: #fffacd;
} */
/* :deep .vuecal__cell-events-count {
  display: none;
} */
</style>
