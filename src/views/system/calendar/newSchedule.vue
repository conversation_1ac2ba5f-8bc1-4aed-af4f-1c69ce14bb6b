<template>
  <Dialog v-model="dialogVisible" title="新建日程">
    <el-form ref="formRef" v-loading="formLoading" :model="formData" label-width="80px">
      <el-form-item>
        归属于
        <span class="no-border">
          <el-select
            :border="false"
            v-model="belongToValue"
            placeholder="归属于"
            style="width: 240px"
          >
            <el-option
              v-for="item in belongTo"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </span>
      </el-form-item>
      <el-form-item>
        <template #label>
          <!-- 这里可以放置图片 -->
          <el-tooltip class="box-item" effect="dark" content="标题" placement="right">
            <img src="../../../assets/imgs/schedule.svg" alt="" />
          </el-tooltip>
        </template>
        <!-- <el-tag>{{ formData.name }}</el-tag> -->
        <el-input
          v-model="inputValue"
          style="max-width: 600px"
          :placeholder="placeholderText"
          class="input-with-select"
        >
          <template #append>
            <el-select
              @change="changeValue"
              v-model="selectValue"
              placeholder="添加会议标题"
              style="width: 80px"
            >
              <el-option label="会议" value="1" />
              <el-option label="活动" value="2" />
            </el-select>
          </template>
        </el-input>
      </el-form-item>
      <el-form-item>
        <!-- <el-tag>{{ formData.code }}</el-tag> -->
        <template #label>
          <!-- 这里可以放置图片 -->
          <el-tooltip class="box-item" effect="dark" content="时间" placement="right">
            <img src="../../../assets/imgs/time.svg" alt="" />
          </el-tooltip>
        </template>

        <div class="block" v-if="!flag">
          <!-- <span class="demonstration">Default</span> -->
          <el-date-picker
            v-model="dateValue"
            type="date"
            :clearable="false"
            value-format="YYYY-MM-DD"
            placeholder="请选择"
            :size="size"
          />
          <span v-if="checked1" class="zhi">至</span>
          <el-date-picker
            v-if="checked1"
            v-model="dateValue1"
            value-format="YYYY-MM-DD"
            type="date"
            placeholder="请选择"
            :size="size"
          />
          <span v-if="!checked1">
            <el-time-select
              v-model="startTime"
              style="width: 240px"
              :max-time="endTime"
              class="mr-4"
              placeholder="开始"
              :clearable="false"
              start="00:00"
              step="00:5"
              end="24:00"
            />
            <span>至</span>
            <el-time-select
              v-model="endTime"
              style="width: 240px"
              :min-time="startTime"
              :clearable="false"
              placeholder="结束"
              start="00:00"
              step="00:5"
              end="24:00"
            />
          </span>
        </div>

        <div class="block isBlock" v-if="flag">
          <!-- <span class="demonstration">Default</span> -->
          <el-date-picker v-model="dateValue" type="date"  value-format="YYYY-MM-DD" placeholder="请选择" :size="size" />
          <el-time-select
            v-if="!checked1"
            v-model="startTime"
            style="width: 240px"
            :max-time="endTime"
            class="mr-4"
            placeholder="开始"
            start="00:00"
            step="00:5"
            end="24:00"
          />
          <span :class="checked1 ? 'zhi' : 'fZhi'">至</span>
          <el-date-picker v-model="dateValue1" type="date"  value-format="YYYY-MM-DD" placeholder="请选择" :size="size" />
          <el-time-select
            v-if="!checked1"
            v-model="endTime"
            style="width: 240px"
            :min-time="startTime"
            placeholder="结束"
            start="00:00"
            step="00:5"
            end="24:00"
          />
        </div>

        <!-- <div class="checkboxr" v-if="flag">
          <el-checkbox v-model="checked1" label="全天" @change="checked1Value" size="large" />
        </div>

        <div class="checkboxLft" v-if="!flag">
          <el-checkbox v-model="checked1" label="全天" @change="checked1Value" size="large" />
        </div> -->
      </el-form-item>

      <el-form-item v-if="flag">
        <!-- <el-select
          v-model="selectiveRepeat"
          placeholder="添加必须参与人1"
          style="width: 240px; margin-top: 10px"
          @change="repeatDate"
        >
          <el-option
            v-for="item in dateList"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select> -->
        <!-- <div class="checkboxr">
          <el-checkbox v-model="checked1" label="全天" @change="checked1Value" size="large" />
        </div> -->
      </el-form-item>

      <el-form-item v-show="belongToValue == 1">
        <template #label>
          <el-tooltip class="box-item" effect="dark" content="参与人" placement="right">
            <img src="../../../assets/imgs/people.svg" alt="" />
          </el-tooltip>
        </template>
        <div style="max-width: 350px">
          <el-button
            icon="el-icon-user"
            type="primary"
            size="default"
            @click="$refs.orgPicker.show()"
            >选择参与人</el-button
          >
          <org-picker type="user" multiple ref="orgPicker" :selected="Uservalue" @ok="selected" />
          <!-- <span class="placeholder"> {{ placeholder }}</span> -->
          <div style="margin-top: 5px">
            <el-tag
              size="small"
              style="margin: 5px"
              closable
              v-for="(dept, i) in Uservalue"
              @close="delDept(i)"
              >{{ dept.name }}</el-tag
            >
          </div>
        </div>
        <!-- <el-select
          v-model="checkboxValue"
          multiple
          filterable
          placeholder="添加必须参与人"
          style="width: 240px"
        >
          <el-option
            v-for="item in peoplees"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select> -->
      </el-form-item>
      <el-form-item label="">
        <!-- <Icon icon="ep:location" /> -->
        <!-- 这里可以放置图片 -->
        <template #label>
          <el-tooltip class="box-item" effect="dark" content="地点" placement="right">
            <img src="../../../assets/imgs/address.svg" alt="" />
          </el-tooltip>
        </template>

        <el-input v-model="addressValue" style="width: 240px" placeholder="请输入地址" clearable />
      </el-form-item>

      <div v-if="flag">
        <el-form-item>
          <template #label>
            <!-- 这里可以放置图片 -->
            <el-tooltip class="box-item" effect="dark" content="描述" placement="right">
              <img src="../../../assets/imgs/describe.svg" alt="" />
            </el-tooltip>
          </template>
          <el-input
            v-model="textarea"
            style="width: 240px"
            :autosize="{ minRows: 2, maxRows: 4 }"
            type="textarea"
            placeholder="添加会议描述"
          />
        </el-form-item>

        <!-- <el-form-item>
          <template #label>
            <el-tooltip class="box-item" effect="dark" content="日历" placement="right">
              <img src="../../../assets/imgs/calendar.svg" alt="" />
            </el-tooltip>
          </template>
          <el-select
            v-model="calendarValue"
            filterable
            placeholder="请选择日历"
            style="width: 240px"
          >
            <el-option
              v-for="item in calendarList"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item> -->

        <!-- <el-form-item @mouseenter="onMouseEnter" @mouseleave="onMouseleave"> 后期可能换成下面这个 -->
        <el-form-item>
          <template #label>
            <!-- 这里可以放置图片 -->
            <el-tooltip class="box-item" effect="dark" content="提醒" placement="right">
              <img src="../../../assets/imgs/remind.svg" alt="" />
            </el-tooltip>
          </template>
          <div>
            <el-button @click="addReminder(0)" v-if="!isReminder">
              <Icon class="mr-5px" icon="ep:plus" />
              添加提醒</el-button
            >
            <!-- <div v-for="(item, index) in selectedValues" :key="index" style="margin-bottom: 10px">后期可能换成下面这个 -->
            <div style="margin-bottom: 10px">
              <el-select
                v-model="remindMinutes"
                v-if="isReminder"
                filterable
                multiple
                placeholder="选择提醒时间"
                style="width: 240px"
              >
                <el-option
                  v-for="option in options"
                  :key="option.id"
                  :label="option.name"
                  :value="option.id"
                />
              </el-select>
              <!-- 后期可能换成下面这个 -->
              <!-- <el-select
                v-model="selectedValues[index]"
                v-if="isReminder"
                filterable
                placeholder="添加必须参与人"
                style="width: 240px"
              >
                <el-option
                  v-for="option in options"
                  :key="option.id"
                  :label="option.name"
                  :value="option.id"
                />
              </el-select>
              <el-button @click="deleteReminder(index, item)" v-show="isCloseButon" class="buttL">
                <Icon icon="ep:close" />
              </el-button>
              <el-button @click="addReminder(1)" v-show="isButon">
                <Icon icon="ep:plus" />
              </el-button> -->
              <!-- <span>ss</span> -->
            </div>
          </div>
        </el-form-item>
      </div>
    </el-form>
    <template #footer>
      <el-button @click="moreOptions" v-if="!flag">更多选项</el-button>
      <el-button :disabled="formLoading" type="primary" @click="submitForm">确 定</el-button>
    </template>
  </Dialog>
  <!-- <div>
    <Dialog v-model="repeatDateDialog" title="自定义周期" >
    </Dialog>
  </div> -->
  <el-dialog v-model="repeatDateDialog" title="自定义周期" width="500">
    <div class="demo-input-suffix">
      <span>重复频率：</span>
      <span>每 </span>
      <el-input-number
        v-model="num"
        :min="1"
        controls-position="right"
        size="large"
        @change="handleChange"
      />

      <el-select
        v-model="repeatDateValue"
        placeholder="请选择"
        style="width: 70px"
        @change="SelectWeekMonthYear"
      >
        <el-option
          v-for="item in repeatDateList"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        />
      </el-select>

      <div class="sundaycontainer" v-show="isWeekMonthYear == 2">
        <div
          class="toSunday"
          :class="item.isSelected ? 'highlight ' : ''"
          v-for="(item, index) in dateOptions"
          :key="index"
          @click="cutTabClick(item, index)"
        >
          {{ item.text }}
        </div>
      </div>
    </div>

    <div class="demo-input-suffix">
      <span>结束重复：</span>
      <el-select
        v-model="endRepetitionValue"
        placeholder="请选择"
        style="width: 120px"
        @change="endRepetition"
      >
        <el-option
          v-for="item in endRepetitionList"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        />
      </el-select>

      <el-input-number
        v-show="flagValue == 3"
        v-model="num"
        :min="1"
        controls-position="right"
        size="large"
        @change="handleChange"
      />
      <span v-show="flagValue == 3">次后</span>
    </div>

    <div class="demo-input-suffix">
      <span>日程时长：</span>
      <el-input v-model="duration" style="width: 120px" disabled />
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="repeatDateDialog = false">取消</el-button>
        <el-button type="primary" @click="dialogVisible = false"> 确定 </el-button>
      </div>
    </template>
  </el-dialog>
  <org-picker
    title="选择要转交的人员"
    ref="orgPicker"
    type="user"
    multiple
    :selected="[]"
    @ok="selected"
  />
</template>

<script lang="ts" setup>
import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'

import { defaultProps, handleTree } from '@/utils/tree'
import * as RoleApi from '@/api/system/role'
import * as MenuApi from '@/api/system/menu'
import * as PermissionApi from '@/api/system/permission'
import { Icon } from '@/components/Icon'
import OrgPicker from '@/components/common/OrgPicker.vue'

defineOptions({ name: 'SystemRoleAssignMenuForm' })
// 新的
import type { CheckboxValueType } from 'element-plus'
import dayjs from 'dayjs'

import { fa } from 'element-plus/es/locale'
import { log } from 'console'
const selectValue = ref('1')
const inputValue = ref('')
const dateValue = ref('')
const dateValue1 = ref('')
const size = ref<'default' | 'large' | 'small'>('default')
const startTime = ref('')
const endTime = ref('')
const addressValue = ref('')
const textarea = ref('')
const selectList = ref([]) // 存储下拉框列表
const selectedValues = ref([]) // 存储选中的值
const placeholderText = ref('添加会议标题')
const checkboxValue = ref([])
const flag = ref(false) // 弹窗的是否展示
const isReminder = ref(false) // 弹窗的是否展示
const checked1 = ref(false)
const isButon = ref(false)
const isCloseButon = ref(false)
const repeatDateDialog = ref(false)
const count = ref(2)
const num = ref(1)
const repeatDateValue = ref('1')
const endRepetitionValue = ref('2')
const flagValue = ref('0')
const duration = ref('60分钟')
const actiove = ref(0)
const isWeekMonthYear = ref(0)
const selectiveRepeat = ref('1')
const belongToValue = ref('1')
const calendarValue = ref('1')
const remindMinutes = ref('')
// const remindMinutes = ref('15')

const dateOptions = ref([
  { id: 1, text: '一', isSelected: false },
  { id: 2, text: '二', isSelected: false },
  { id: 3, text: '三', isSelected: false },
  { id: 3, text: '四', isSelected: false },
  { id: 3, text: '五', isSelected: false },
  { id: 3, text: '六', isSelected: false },
  { id: 3, text: '日', isSelected: false }
])
const options = ref([
  // { id: '1', name: '开始时' },
  { id: '5', name: '开始前5分钟' },
  { id: '15', name: '开始前15分钟' },
  { id: '30', name: '开始前30分钟' },
  { id: '60', name: '开始前1小时' },
  { id: '120', name: '开始前2小时' },
  { id: '1440', name: '开始前1天' },
  { id: '2880', name: '开始前2天' },
  { id: '10080', name: '开始前7天' }
]) // 定义选项数组对象
const dateList = ref([
  { value: '1', label: '不重复' },
  { value: '2', label: '每天' },
  { value: '3', label: '每周星期一' },
  { value: '4', label: '每月' },
  { value: '5', label: '每年3月4号' },
  { value: '6', label: '每个工作日（星期...' }
  // { value: '7', label: '自定义...' }
]) // 定义选项数组对象
const repeatDateList = ref([
  { value: '1', label: '天' },
  { value: '2', label: '周' },
  { value: '3', label: '月' },
  { value: '4', label: '年' }
]) // 定义选项数组对象
const endRepetitionList = ref([
  { value: '1', label: '终止于某天' },
  { value: '2', label: '无限重复' },
  { value: '3', label: '限定次数' }
]) // 定义选项数组对象
const belongTo = ref([
  { value: '1', label: '个人' },
  { value: '2', label: '部门' }
]) // 定义选项数组对象
const peoplees = ref([
  {
    value: '1',
    label: '测试'
  },
  {
    value: '2',
    label: '测试1'
  },
  {
    value: '3',
    label: '测试2'
  },
  {
    value: '4',
    label: '测试3'
  },
  {
    value: '5',
    label: '测试4'
  },
  {
    value: '6',
    label: '测试5'
  }
])

const Uservalue = ref([])
const calendarList = ref([
  { value: '1', label: '我的日历' },
  { value: '2', label: '其它日历' }
]) // 定义选项数组对象
//
/** 更多选项 */
const moreOptions = () => {
  // checkboxValue.value = ['1']
  console.log(checkboxValue.value)
  flag.value = true
  // this.$forceUpdate()//强制更新
}
// 选择会议
const changeValue = (val) => {
  console.log(val)
  placeholderText.value = val == 1 ? '添加会议标题' : '添加活动标题'
}
// 添加提醒
const addReminder = (val) => {
  // console.log(val)
  console.log(remindMinutes.value)
  remindMinutes.value = '15'
  if (val == 0) {
    isReminder.value = true
  }
  // count.value++
  // console.log(selectList.value.length)
  // const newIndex = selectList.value.length + 1 // 获取新下拉框的索引
  // selectList.value.push(newIndex)
  // selectedValues.value.push(newIndex)
  // if(){

  // }
  // console.log(newIndex)
  if (count.value == options.value.length - 1) {
    // console.log(newIndex, 'newIndexnewIndexnewIndex')
    isButon.value = false
    // return
  }
  const newIndex = count.value++ // 获取新下拉框的索引
  console.log(newIndex)

  // selectList.value.push(newIndex) // 将新下拉框添加到列表中
  // selectedValues.value.push(''); //
  // const item = options.value.find((option) => option) // 从数组对象中查找对应的选项
  // if (item) {
  selectedValues.value.push(options.value[newIndex].id) // 将选中的值设置为找到的选项的id
  // }
  console.log(selectList.value)
  console.log(selectedValues.value)
}
// 删除提醒
const deleteReminder = (index, val) => {
  console.log(index)
  console.log(val)
  console.log(selectList.value)
  console.log(selectedValues.value)
  //  count.value--
  // const item = selectList.value.find((item1) => item1 == val)
  // for (let i = 0; i <= selectList.value.length; i++) {
  //   if (selectList.value[i] == val) {
  //     console.log(selectList.value[i],'vallll');
  //     console.log(i,'ssss');
  //     selectList.value.splice(i, 1) // 将选中的值设置为找到的选项的id
  //   }
  // }

  const newVa = options.value.find((item) => {
    // console.log(item);
    return item.id == val
  })
  console.log(newVa)

  // if (item) {
  // selectList.value.splice(val, 1) // 将选中的值设置为找到的选项的id
  selectedValues.value.splice(index, 1) // 将选中的值设置为找到的选项的id
  // }

  if (selectedValues.value.length == 0 && selectList.value.length == 0) {
    isReminder.value = false
    count.value = 2
  }
}

// 选择重复时间
const repeatDate = (val) => {
  console.log(val)
  return
  if (val != 1) {
    repeatDateDialog.value = true
  }
}
// 重复频率
const SelectWeekMonthYear = (val) => {
  // console.log(val)
  isWeekMonthYear.value = val
  switch (val) {
    case '1':
      console.log('天')
      break
    case '2':
      console.log('周')
      break
    case '3':
      console.log('月')
      break
    case '4':
      console.log('年')
      break
  }
}

const cutTabClick = (item, index) => {
  // console.log(val)
  actiove.value = index
  //然后通过这个属性判断是否选中点亮和取消
  item.isSelected = !item.isSelected
}

// 结束重复
const endRepetition = (val) => {
  console.log(val)
  flagValue.value = val
}

const handleChange = (value: number) => {
  console.log(value)
}
const checked1Value = () => {
  console.log(checked1.value)
  if (checked1.value) {
    dateValue1.value = dateValue.value
  }
}
const onMouseleave = () => {
  // console.log('鼠标离开')
  isButon.value = false
  isCloseButon.value = false
}

const onMouseEnter = () => {
  // console.log('鼠标悬浮在 el-select 上')
  // console.log(count.value, '鼠标悬浮在 el-select 上')
  if (count.value == options.value.length) {
    isButon.value = false
  } else {
    isButon.value = true
  }
  isCloseButon.value = true
  // 在这里执行您想要的操作
}

const selected = (va) => {
  console.log(va, 'vvvvvvvvvvvvvv')
  Uservalue.value = va.map((item, i) => {
    return {
      name: item.name,
      id: item.id
    }
  })
  console.log(Uservalue.value)
}
const delDept = (i) => {
  console.log(Uservalue.value, 'Uservalue.valueUservalue.value')

  Uservalue.value.splice(i, 1)
}

// 新的结束地方

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formData = reactive({
  id: 0,
  name: '',
  code: '',
  menuIds: []
})
const formRef = ref() // 表单 Ref
const menuOptions = ref<any[]>([]) // 菜单树形结构
const menuExpand = ref(false) // 展开/折叠
const treeRef = ref() // 菜单树组件 Ref
const treeNodeAll = ref(false) // 全选/全不选

/** 打开弹窗 */
// const open = async (row: RoleApi.RoleVO) => {
const open = async (val) => {
  console.log(val)
  dateValue.value = dayjs(val).format('YYYY-MM-DD')
  dateValue1.value = dayjs(val).format('YYYY-MM-DD')
  console.log(dateValue1.value);
  

  dialogVisible.value = true
  flag.value = false
  // 获取当前时间
  const currentTime = dayjs()
  // 将当前时间往后推一小时
  const startTimeHour = currentTime.add(1, 'hour')
  const endTimeHour = currentTime.add(2, 'hour')
  startTime.value = startTimeHour.format('HH:00')
  endTime.value = endTimeHour.format('HH:00')
  console.log(startTimeHour.format('HH:00'))
  selectList.value = []
  selectedValues.value = []
  isReminder.value = false
  inputValue.value = ''
  addressValue.value = ''
  textarea.value = ''
  checkboxValue.value = []
  Uservalue.value = []
  belongToValue.value = '1'
  remindMinutes.value = ''
  return
  resetForm()
  // 加载 Menu 列表。注意，必须放在前面，不然下面 setChecked 没数据节点
  menuOptions.value = handleTree(await MenuApi.getSimpleMenusList())
  // 设置数据
  formData.id = row.id
  formData.name = row.name
  formData.code = row.code
  formLoading.value = true
  try {
    formData.value.menuIds = await PermissionApi.getRoleMenuList(row.id)
    // 设置选中
    formData.value.menuIds.forEach((menuId: number) => {
      treeRef.value.setChecked(menuId, true, false)
    })
  } finally {
    formLoading.value = false
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  console.log(111)
  console.log(dateValue.value)
  console.log(remindMinutes.value)
  // return
  // 校验表单
  // if (!formRef) return
  // const valid = await formRef.value.validate()
  // if (!valid) return
  // // 提交请求
  if (!inputValue.value) return message.error('标题不能为空')
  if (belongToValue.value == '1') {
    if (Uservalue.value.length <= 0) return message.error('参与人不能为空')
  }

  formLoading.value = true
  try {
    const data = {
      // userId: 1,
      // eventStartTime: checked1.value ? `${dateValue.value}` : `${dateValue.value} ${startTime.value}`,
      // eventEndTime: checked1.value ? `${dateValue.value}` : `${dateValue.value} ${endTime.value}`,
      eventStartTime: `${dateValue.value} ${startTime.value}`,
      eventEndTime: `${dateValue1.value} ${endTime.value}`,
      eventName: inputValue.value,
      requireUserIds: Uservalue.value.map((item) => item.id).join(','),
      eventAddress: addressValue.value,
      eventType: selectValue.value,
      remindMinutes: remindMinutes.value ? remindMinutes.value : '-1',
      eventDescription: textarea.value
    }
    console.log(data)
    console.log(selectValue.value)
    // console.log(belongToValue.value)
    // console.log(checkboxValue.value, 'checkboxValuecheckboxValue')

    // return
    if (belongToValue.value == '1') {
      await RoleApi.addUserCalenderEvent(data)
    } else {
      //  data.deptId = 103
      await RoleApi.addDepartmentCalenderEvent(data)
    }
    message.success(t('common.createSuccess'))
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
  return

  // 校验表单
  if (!formRef) return
  // const valid = await formRef.value.validate()
  if (!valid) return
  // 提交请求
  formLoading.value = true
  try {
    const data = {
      roleId: formData.id,
      menuIds: [
        ...(treeRef.value.getCheckedKeys(false) as unknown as Array<number>), // 获得当前选中节点
        ...(treeRef.value.getHalfCheckedKeys() as unknown as Array<number>) // 获得半选中的父节点
      ]
    }
    await PermissionApi.assignRoleMenu(data)
    message.success(t('common.updateSuccess'))
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  return
  // 重置选项
  treeNodeAll.value = false
  menuExpand.value = false
  // 重置表单
  formData.value = {
    id: 0,
    name: '',
    code: '',
    menuIds: []
  }
  treeRef.value?.setCheckedNodes([])
  formRef.value?.resetFields()
}

/** 全选/全不选 */
const handleCheckedTreeNodeAll = () => {
  treeRef.value.setCheckedNodes(treeNodeAll.value ? menuOptions.value : [])
}

/** 展开/折叠全部 */
const handleCheckedTreeExpand = () => {
  const nodes = treeRef.value?.store.nodesMap
  for (let node in nodes) {
    if (nodes[node].expanded === menuExpand.value) {
      continue
    }
    nodes[node].expanded = menuExpand.value
  }
}
</script>
<style lang="scss" scoped>
.cardHeight {
  width: 100%;
  max-height: 400px;
  overflow-y: scroll;
}
:deep .el-select__wrapper {
  background-color: #ffffff !important;
}
:deep .block .el-select {
  width: 100px !important;
  margin-left: 16px;
}
:deep .block .el-input__prefix-icon {
  display: none;
}
.checkboxLft {
  margin-left: 70px;
}
.zhi {
  margin: 0 10px;
}
:deep .isBlock .el-input {
  width: 130px;
}
:deep .isBlock .el-input__wrapper {
  flex-grow: 0 !important;
  width: 130px;
}
.fZhi {
  margin-right: 13px;
}
.checkboxr {
  margin-left: 23px;
  //margin-top: 10px;
}
.buttL {
  margin-left: 10px;
}
:deep .el-form-item__label {
  display: flex;
  align-items: center;
}
:deep .el-input-number--large {
  width: 116px;
  height: 32px;
  margin: 0 10px;
}
.demo-input-suffix {
  margin-bottom: 15px;
}
:deep .el-input-number.is-controls-right[class*='large'] [class*='increase'] {
  --el-input-number-controls-height: 14px;
}
.sundaycontainer {
  display: flex;
  justify-content: center;
  margin-top: 10px;
}
.toSunday {
  width: 26px;
  border: 1px solid #edeff0;
  border-radius: 28px;
  text-align: center;
  margin-right: 10px;
  height: 26px;
  line-height: 26px;
}
.highlight {
  background: #0089ff;
  color: #fff;
}
.no-border {
  margin-top: -1px;
}
/* 去掉边框线的样式 */
:deep .no-border .el-select__wrapper {
  box-shadow: 0 0 0 0px !important;
}
</style>
