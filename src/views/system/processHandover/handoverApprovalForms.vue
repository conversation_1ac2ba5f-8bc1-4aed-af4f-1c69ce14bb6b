<template>
  <div class="rowBox" :class="{ isCol: isCollapse, 'isCBox-else': windowWidth < 768 }"> </div>

  <el-button class="riButton" @click="batchTransfer()"> 批量转交 </el-button>
  <el-form :inline="true">
    <el-form-item label="当前审批人">
      <el-select
        v-model="currentApprover"
        placeholder="请选择类型"
        class="!w-240px JuF"
        @change="secetApprover"
      >
        <el-option
          v-for="item in resignationEmployed"
          :key="item.id"
          :label="item.textName"
          :value="item.textName"
        />
      </el-select>
      <div @click="inputFocus" class="deptClass">
        <el-input readonly placeholder="请选择" class="!w-240px" />
        <!-- 在职选择人员 -->
        <org-picker
          title="请选择"
          ref="resignation"
          :selected="targetUser"
          @ok="selectedTargetUser"
          type="user"
          v-if="quitFlag == '0'"
        />
        <!-- 离职选择人员 -->
        <quitOrgPicker
          v-if="quitFlag == '1'"
          :selected="targetUser"
          @ok="selectedTargetUser"
          type="user"
          ref="resignation1"
        />
        <div v-if="targetUser.length > 0" class="tagItem" ref="tagItemRef">
          <el-tag type="info" v-for="(item, i) in targetUser" :key="i" @close="closeTag(item)">{{
            item.name
          }}</el-tag>
        </div>
      </div>
    </el-form-item>
  </el-form>

  <el-table
    v-loading="loading"
    :data="approvalData"
    @selection-change="handleSelectionChange"
    @row-click="showProcess"
    empty-text='暂无数据,请先选择当前审批人'
  >
    <el-table-column type="selection" width="55" />
    <el-table-column align="center" label="审批编号" prop="instanceId" />
    <el-table-column align="center" label="审批类型" prop="processDefName" />
    <el-table-column align="center" label="发起人" prop="createTime">
      <template #default="scope">
        <div style="color: #3370ff">
          {{ scope.row.owner.name }}
        </div>
      </template>
    </el-table-column>
    <el-table-column align="center" label="发起时间" prop="createTime" />
    <el-table-column align="center" label="操作" prop="createTime">
      <template #default="scope">
        <div @click.stop>
        <el-button link type="primary" @click="transfer(scope.row.instanceId)"> 转交 </el-button>
        </div>
      </template>
    </el-table-column>
  </el-table>
  <!-- 分页 -->
  <Pagination
    v-model:limit="queryParams.pageSize"
    v-model:page="queryParams.pageNo"
    :total="total"
    @pagination="getList"
  />
  <!-- 转交审批单 -->
  <Dialog v-model="transferVisible" title="转交审批单">
    <el-form
      ref="formRef"
      v-loading="formLoading"
      :model="formData"
      label-width="80px"
      label-position="top"
    >
      <el-form-item label="把审批流程交接出去的人（被交接人）">
        <el-input disabled v-model="currentApprover" class="!w-80px JuF" />
        <el-input disabled v-model="displayValue" placeholder="请选择" class="!w-240px" />
      </el-form-item>
      <el-form-item label="接收审批流程的人（交接人）">
        <div @click="inputFocus1" class="deptClass">
          <el-input readonly placeholder="请选择" class="!w-240px" />
          <org-picker
            title="请选择"
            ref="resignation2"
            :selected="transferredPerson"
            @ok="selectedTargetPerson"
            type="user"
          />
          <div v-if="transferredPerson.length > 0" class="tagItem" ref="tagItemRef">
            <el-tag
              type="info"
              v-for="(item, i) in transferredPerson"
              :key="i"
              @close="closeTag(item)"
              >{{ item.name }}</el-tag
            >
          </div>
        </div>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="transferVisible = false">取 消</el-button>
      <el-button :disabled="formLoading" type="primary" @click="submitForm">确 定</el-button>
    </template>
  </Dialog>
  <el-drawer
    :size="isMobile ? '100%' : '560px'"
    direction="rtl"
    title="审批详情"
    :z-index="1000"
    v-model="processVisible"
    class="custom-detail-header"
  >
    <instance-preview
      v-if="processVisible"
      :instance-id="selectInstance"
      @handler-after="handlerAfter"
    />
  </el-drawer>
</template>
<script lang="ts" setup>
import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'
import { getUserProfile } from '@/api/system/user/profile'
import { Plus, ArrowRight } from '@element-plus/icons-vue'
import { tr } from 'element-plus/es/locale'
import { useAppStore } from '@/store/modules/app'
import * as handoverApi from '@/api/system/processHandover'
import { dateFormatter } from '@/utils/formatTime'
import OrgPicker from '@/components/common/OrgPicker.vue'
import quitOrgPicker from '@/components/common/quitOrgPicker.vue'
import InstancePreview from '../../wflow/workspace/approval/ProcessInstancePreview.vue'

import dayjs from 'dayjs'
defineOptions({ name: 'orgstructure' })
import { factory } from 'typescript'
const isMobile = computed(() => window.screen.width < 450)
const currentApprover = ref('在职')
const selectInstance = ref('')
const processVisible = ref(false)
const resignationEmployed = ref([
  {
    id: 1,
    textName: '在职'
  },
  {
    id: 2,
    textName: '离职'
  }
])
const targetUser = ref([]) //当前审批人在职/离职下拉
const targetQuitUser = ref([]) //当前审批人在职/离职下拉
const transferredPerson = ref([])

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化
const total = ref(0) // 列表的总页数
const loading = ref(false)
const formLoading = ref(false)
const transferVisible = ref(false)

const topData = [
  {
    text: '审批流程交接'
  },

  {
    text: '待处理审批单交接'
  }
]

const approvalData = ref([])
const selectedRows = ref([])

const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  userId: ''
})
const formData = ref({
  templateValue: '1'
})
const tableIndex = ref(0)
const dialogVisible = ref(false)

const showProcess = (row) => {
  processVisible.value = true
  selectInstance.value = row.instanceId
}
// 表格数据
const getList = async () => {
  loading.value = true
  queryParams.userId = targetUser.value[0].id
  const data = await handoverApi.userTodoList(queryParams)
  loading.value = false
  console.log(data)
  approvalData.value = data.records
  total.value = data.total
}
// 转交

const selectedInstanceId = ref('')
const transfer = (v) => {
  console.log(v)
  selectedInstanceId.value = v
  transferredPerson.value = []
  transferVisible.value = true
}
// 批量转交
const batchTransfer = () => {
  selectedInstanceId.value = ''
  if (selectedRows.value.length <= 0) return message.warning('未选中数据哦')
  transferredPerson.value = []
  transferVisible.value = true
}
const handleSelectionChange = (val) => {
  selectedRows.value = val
}
// 弹窗确认
const submitForm = async () => {
  if (transferredPerson.value.length <= 0) return message.warning('请选择交接人')
  console.log(selectedRows.value, 'dsa')
  let obj = {
    userId: targetUser.value[0].id,
    targetUserId: transferredPerson.value[0].id,
    instanceIds: selectedInstanceId.value
      ? selectedInstanceId.value
      : selectedRows.value.map((item) => item.instanceId).join(','),
    applyUserStatus: quitFlag.value
  }
  formLoading.value = true
  await handoverApi.replaceTodoProcess(obj)
  formLoading.value = false
  transferVisible.value = false
  getList()
  message.success('操作成功')
}

const quitFlag = ref('0')
const secetApprover = (v) => {
  // targetUser.value = []
  if (v == '在职') {
    quitFlag.value = '0'
  } else {
    quitFlag.value = '1'
  }
}
// 当前审批人选择
const resignation = ref()
const resignation1 = ref()
const resignation2 = ref()
const inputFocus = (e) => {
  if (e.target.tagName.toLowerCase() === 'input') {
    return
  }
  if (quitFlag.value == '0') {
    resignation.value.show()
  } else {
    resignation1.value.show()
  }
}

const inputFocus1 = (e) => {
  if (e.target.tagName.toLowerCase() === 'input') {
    return
  }
  // resignation.value.show()
  resignation2.value.show()
}

// 当前在职审批人选择
const displayValue = ref('')
const selectedTargetUser = (users) => {
  targetUser.value = []
  users.forEach((item) => {
    targetUser.value.push({
      id: item.id,
      name: item.name
      // type: item.type
    })
    displayValue.value = item.name
  })
  getList()
}


// 被交接人选择
const selectedTargetPerson = (users) => {
  transferredPerson.value = []
  users.forEach((item) => {
    transferredPerson.value.push({
      id: item.id,
      name: item.name,
      type: item.type || ''
    })
  })
}

// 监听折叠面板
const appStore = useAppStore()
const collapse = computed(() => appStore.getCollapse)
const isCollapse = computed(() => appStore.getCollapse)
watch(
  () => collapse.value,
  (newPath, oldPath) => {
    isCollapse.value = newPath
  }
)
// 监听浏览器宽度
const windowWidth = ref(window.innerWidth)
const handleResize = () => {
  windowWidth.value = window.innerWidth
}

/** 初始化 */
onMounted(async () => {
  // getList()
  window.addEventListener('resize', handleResize)
})
onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
})
</script>

<style lang="scss" scoped>
.riButton {
  float: right;
  margin-bottom: 16px;
}
.rTop {
  background: #fff;
  border-radius: 2px;
  padding: 13px 16px;
  // position: sticky;
  //   top: 0;
  //   bottom: 0;
  //   z-index: 99;
  span {
    margin-right: 32px;
    cursor: default;
    font-size: 14px;
    font-weight: 400;
  }
}
.dx {
  border-bottom: 1px solid #303133;
  padding: 8px 0;
  font-weight: 500;
}
.center {
  margin-top: 45px;
}
.taUser {
  display: flex;
  align-items: center;
  .taUserLeft {
    width: 28px;
    height: 28px;
    background: #3370ff;
    border-radius: 5px;
    line-height: 28px;
    font-weight: 500;
    font-size: 12px;
    color: #ffffff;
    margin-right: 5px;
    text-align: center;
  }
  .taUser1 {
    > div:nth-of-type(1) {
      font-weight: 500;
      font-size: 14px;
      color: #303133;
      text-align: left;
    }
    > div:nth-of-type(2) {
      font-weight: 400;
      font-size: 14px;
      color: #ffa826;
      cursor: pointer;
    }
  }
}
.rowBox {
  position: fixed;
  left: 200px;
  right: 0px;
  top: 81px;
}
.isCol {
  left: 64px;
  margin-left: 0 !important;
}
.isCBox-else {
  left: 0;
}
:deep.deptClass {
  position: relative;
  // padding: 0 10px;
  .el-input {
    pointer-events: none;
  }
  .tagItem {
    position: absolute;
    left: 10px;
    top: 0;
    .el-tag {
      margin-right: 5px;
    }
    .el-tag__content {
      padding: 0 6px;
    }
  }
}
.JuF {
  margin-right: 10px;
}
</style>
