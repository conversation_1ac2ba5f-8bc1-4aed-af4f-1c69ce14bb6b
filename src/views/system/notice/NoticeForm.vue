<template>
  <Dialog v-model="dialogVisible" :title="dialogTitle" width="900">
    <el-form
      ref="formRef"
      v-loading="formLoading"
      :model="formData"
      :rules="formRules"
      label-width="80px"
    >
      <el-form-item label="公告标题" prop="title">
        <el-input v-model="formData.title" placeholder="请输入公告标题" />
      </el-form-item>
      <el-form-item label="公告内容" prop="content" class="editor-div">
        <Editor v-model="formData.content" height="400px" class="isEditor" />
      </el-form-item>
      <el-form-item label="附件" prop="file">
        <el-upload
          action=""
          :auto-upload="false"
          multiple
          :file-list="fileList"
          :on-change="onChange"
          :on-remove="onRemove"
        >
          <el-button size="default">点击上传</el-button>
          <template #tip>
            <span class="el-upload__tip"> 请选择附件</span>
          </template>
        </el-upload>
      </el-form-item>
      <el-form-item label="" class="FileDiv">
        <div v-for="(item, index) in successFile" :key="index" class="el-upload-list__item">
          <div class="el-upload-list__item-info">
            <a class="el-upload-list__item-name">
              <icon name="el-icon-document" />
              <span class="el-upload-list__item-file-name">{{ item.name }}</span>
            </a>
          </div>
          <icon name="el-icon-close el-icon--close" @click="successFile.splice(index, 1)" />
        </div>
      </el-form-item>

      <el-form-item
        label=""
        prop="uploadFlag"
        class="flagDiv"
        v-if="Object.keys(fileList).length > 0 || successFile.length > 0"
      >
        <el-checkbox v-model="formData.uploadFlag">允许下载公告附件</el-checkbox>
      </el-form-item>
      <el-form-item label="公告类型" prop="type">
        <el-select v-model="formData.type" clearable placeholder="请选择公告类型">
          <el-option
            v-for="dict in noticeTypeData"
            :key="parseInt(dict.value as any)"
            :label="dict.label"
            :value="parseInt(dict.value as any)"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="发布对象" prop="user">
        <el-radio-group v-model="formData.toUserType">
          <el-radio label="全体用户" :value="1">全体用户</el-radio>
          <el-radio label="指定用户" :value="2" @click="selectToUserType">指定用户</el-radio>
        </el-radio-group>
        <div v-if="formData.toUserType === 2" class="selected-info">
          <div v-if="formData.selectedDept.length > 0">
            已选部门：{{ formData.selectedDept.map(dept => dept.name).join('、') }}
          </div>
          <div v-if="formData.selectedUser.length > 0">
            已选用户：{{ formData.selectedUser.map(user => user.name).join('、') }}
          </div>
        </div>
      </el-form-item>

      <el-form-item label="状态" prop="status">
        <el-select v-model="formData.status" clearable placeholder="请选择状态">
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.COMMON_STATUS)"
            :key="parseInt(dict.value as any)"
            :label="dict.label"
            :value="parseInt(dict.value as any)"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="备注" prop="remark">
        <el-input v-model="formData.remark" placeholder="请输备注" type="textarea" />
      </el-form-item>

      <el-form-item label="高级设置">
        <div style="margin-top: -4px">
          <el-checkbox
            v-model="checkedDING"
            label="发 BAN 通知（发送 BAN 消息，重要通知确保对方收到）"
            size="large"
          />
        </div>
        <div style="margin-top: -8px">
          <el-checkbox
            v-model="topFlag"
            label="设为置顶公告（可将重要公告置顶，始终处于列表顶部）"
            size="large"
          />
        </div>
        <div style="margin-top: -12px">
          <el-checkbox
            v-model="readFlag"
            label="公告必读（强制查看公告后才可继续使用办办）"
            size="large"
          />
        </div>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button :disabled="formLoading" type="primary" @click="submitForm">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
  
  <!-- 添加 OrgPicker 组件 -->
  <OrgPicker
    ref="orgPickerRef"
    title="选择发布对象"
    type="org"
    :multiple="true"
    :selected="[...formData.selectedDept, ...formData.selectedUser]"
    @ok="handleOrgPickerOk"
  />
</template>
<script lang="ts" setup>
import * as FileApi from '@/api/infra/file'
import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'
import { CommonStatusEnum } from '@/utils/constants'
import * as NoticeApi from '@/api/system/notice'
import { Editor } from '@/components/Editor'
import OrgPicker from '@/components/common/OrgPicker.vue'

defineOptions({ name: 'SystemNoticeForm' })
import * as DictDataApi from '@/api/system/dict/dict.data'

const noticeTypeData = ref<any>([])
const getNoticeType = async () => {
  try {
    const data = await DictDataApi.getDictDataList({ dictType: DICT_TYPE.SYSTEM_NOTICE_TYPE })
    noticeTypeData.value = data
  } finally {
  }
}

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const checkedDING = ref(false) //ban推送
const topFlag = ref(false) //公告置顶
const readFlag = ref(false) //公告必读
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const formData = ref<any>({
  id: undefined,
  title: '',
  type: undefined,
  content: '',
  status: CommonStatusEnum.ENABLE,
  remark: '',
  uploadFlag: false,
  banFlag: 0,
  topFlag: 0,
  toUserType: 1, //1全体用户 2指定用户
  selectedDept: [], // 选中的部门
  selectedUser: [] // 选中的用户
})
const formRules = reactive({
  title: [{ required: true, message: '公告标题不能为空', trigger: 'blur' }],
  type: [{ required: true, message: '公告类型不能为空', trigger: 'change' }],
  status: [{ required: true, message: '状态不能为空', trigger: 'change' }],
  content: [{ required: true, message: '公告内容不能为空', trigger: 'blur' }],
  toUserType: [{ required: true, message: '发布对象不能为空', trigger: 'change' }]
})
const formRef = ref() // 表单 Ref
const successFile = ref<any>([])
/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  getNoticeType()
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  checkedDING.value = false
  topFlag.value = false
  readFlag.value = false
  resetForm()
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      formData.value = await NoticeApi.getNotice(id)
      checkedDING.value = formData.value.banFlag == 0 ? false : true
      topFlag.value = formData.value.topFlag == 0 ? false : true
      readFlag.value = formData.value.readFlag == 0 ? false : true
      successFile.value = formData.value.fileList ? formData.value.fileList : []
    } finally {
      formLoading.value = false
    }
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const fileList = ref([]) //el-upload绑定的fileList
const endResult = ref<any>([]) //最终提交的fileList
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  if (!formRef) return
  const valid = await formRef.value.validate()
  if (!valid) return
  // 提交请求
  formLoading.value = true

  if (fileList.value.length > 0) {
    const uploadQueue: any = []
    fileList.value.forEach((file: any) => {
      uploadQueue.push(FileApi.updateFile({ file: file.raw }))
    })
    Promise.all(uploadQueue)
      .then(async (res) => {
        endResult.value = []
        let isFault = false
        res.forEach((item: any) => {
          if (item.code === 0) {
            endResult.value.push(item.data)
          } else {
            isFault = true
          }
        })
        if (isFault) {
          ElMessage.error('附件上传失败，请重新上传')
          formLoading.value = false
          return
        }
        submitAll()
      })
      .catch(() => {
        ElMessage.error('附件上传失败，请重新上传')
        formLoading.value = false
      })
  } else {
    submitAll()
  }
}

const resetForm = () => {
  formData.value = {
    id: undefined,
    title: '',
    type: undefined,
    content: '',
    status: CommonStatusEnum.ENABLE,
    remark: '',
    uploadFlag: false,
    banFlag: 0,
    topFlag: 0,
    toUserType: 1, //1全体用户 2指定用户
    selectedDept: [], // 选中的部门
    selectedUser: [] // 选中的用户
  }
  formRef.value?.resetFields()
  fileList.value = []
  successFile.value = []
  endResult.value = []
}

const submitAll = async () => {
  try {
    formData.value.banFlag = checkedDING.value ? 1 : 0
    formData.value.topFlag = topFlag.value ? 1 : 0
    formData.value.readFlag = readFlag.value ? 1 : 0
    const data: any = formData.value as unknown as NoticeApi.NoticeVO
    
    // 处理部门和用户数据
    if (formData.value.toUserType === 2) {
      data.deptIds = formData.value.selectedDept.map(dept => dept.id).join(',')
      data.userIds = formData.value.selectedUser.map(user => user.id).join(',')
    } else {
      data.deptIds = ''
      data.userIds = ''
    }
    
    const newArr: any = []
    if (successFile.value.length > 0) {
      successFile.value.forEach((item: any) => {
        newArr.push(item.url)
      })
    }
    newArr.push(...endResult.value)
    data.filePaths = newArr.length > 0 ? newArr.join(',') : ''
    
    if (formType.value === 'create') {
      await NoticeApi.createNotice(data)
      message.success(t('common.createSuccess'))
    } else {
      await NoticeApi.updateNotice(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    emit('success')
  } finally {
    formLoading.value = false
  }
}

const onRemove = (file, list) => {
  fileList.value = list
}
const onChange = (file, list) => {
  fileList.value = list
}

// OrgPicker 组件引用
const orgPickerRef = ref()

//选择用户
const selectToUserType = () => {
  orgPickerRef.value?.show()
}

// 处理选择结果
const handleOrgPickerOk = (selected) => {
  const depts = []
  const users = []
  selected.forEach(item => {
    if (item.type === 'dept') {
      depts.push(item)
    } else if (item.type === 'user') {
      users.push(item)
    }
  })
  formData.value.selectedDept = depts
  formData.value.selectedUser = users
}
</script>
<style lang="less" scoped>
.isEditor {
  width: 100%;
  // min-height: 300px;
}

.el-upload__tip {
  color: #909399;
  margin-left: 20px;
}

:deep(.el-form-item__content > div) {
  width: 100%;
}

.flagDiv {
  margin-top: -10px;
}

:deep(.el-upload-list__item) {
  border: 1px solid #dcdfe6;
}

:deep(.el-upload-list__item:hover) {
  background: #fff;
}

.FileDiv {
  margin-top: -18px;
}

.selected-info {
  margin-top: 10px;
  color: #606266;
  font-size: 14px;
  
  > div {
    margin-bottom: 5px;
  }
}
</style>
