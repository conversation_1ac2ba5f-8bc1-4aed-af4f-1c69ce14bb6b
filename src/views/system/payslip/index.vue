<template>
  <ContentWrap v-if="flag == 0" class="customHeight">
    <div style="margin-bottom: 20px">工资条管理</div>
    <div class="payroll" @click="upPayroll" v-hasPermi="['system:salary-bill:create']">
      <el-icon style="margin-right: 5px">
        <Plus />
      </el-icon>上传工资表
    </div>
    <div class="block">
      <el-date-picker value-format="YYYY-MM" v-model="queryParams.month" @change="changeMonth" :clearable="false"
        :default-time="new Date()" type="month" placeholder="请选择月份" />
    </div>
    <div class="center" v-loading="loading">
      <div class="content" v-for="(item, i) in dataList" :key="i" @mouseover="showXian(i)" @mouseleave="hideXian">
        <div class="xian" v-if="isVisible && tabIndex == i"></div>

        <div class="characters">
          <el-row :gutter="20">
            <el-col :span="8">
              <div class="charfirst">{{ item.name }}</div>
            </el-col>
            <el-col :span="3">
              <div class="charsecond">
                <p>已发送</p>
                <p>{{ item.sendNum }}</p>
              </div>
            </el-col>

            <el-col :span="3">
              <div class="charsecond">
                <p>已查看</p>
                <p>{{ item.viewNum }}</p>
              </div>
            </el-col>

            <el-col :span="5">
              <div class="charsecond">
                <p>已确认</p>
                <p>{{ item.confirmNum }}</p>
              </div>
            </el-col>

            <el-col :span="5">
              <div class="charthird">
                <el-button link type="primary" @click="goToSend(3, item.id, '设置', item.deleteFlag)"
                  v-hasPermi="['system:salary-bill:update']">
                  设置
                </el-button>
                <!-- <el-button link type="primary"  @click="deleteBill(item.id)" v-if="item.deleteFlag"> -->

                <el-tooltip v-if="!item.deleteFlag" class="box-item" effect="dark" content="需撤回所有工资条后，才能删除"
                  placement="right">
                  <el-button link type="primary" :style="{ color: item.deleteFlag ? '' : '#D7D7D7' }"
                    :disabled="item.deleteFlag ? false : true" @click="deleteBill(item.id)"
                    v-hasPermi="['system:salary-bill:delete']">
                    删除
                  </el-button>
                </el-tooltip>
                <el-button v-else link type="primary" :style="{ color: item.deleteFlag ? '' : '#D7D7D7' }"
                  :disabled="item.deleteFlag ? false : true" @click="deleteBill(item.id)"
                  v-hasPermi="['system:salary-bill:delete']">
                  删除
                </el-button>
                <el-button type="primary" plain @click="goToSend(1, item.id, item.name)" v-if="item.sendFlag">
                  前往发送
                </el-button>
                <el-button plain @click="goToSend(1, item.id, item.name)" v-else>
                  查看发送
                </el-button>
              </div>
            </el-col>
          </el-row>
        </div>
      </div>
    </div>
    <!-- 分页 -->
    <div class="fenye" v-if="total > 0">
      <Pagination v-model:limit="queryParams.pageSize" v-model:page="queryParams.pageNo" :total="total"
        @pagination="getSalaryBill" />
    </div>
    <div v-if="dataList.length == 0" class="placeholderImage">
      <img src="@/assets/image/payslip-bg.png" />
      <p>{{ currentYearMonth }}月没有创建的工资条</p>
      <ul>
        <li>点击【上传工资表】创建工资条</li>
        <li>切换月份查看已经发出的工资条</li>
      </ul>
    </div>
  </ContentWrap>
  <!-- 前往发送 -->
  <toSend v-if="flag == 1" ref="toSendRef" :idVal="idVal" :titleName="titleName" @success="goToSend(0)"
    @fanhui="fanhui" />
  <!-- 上传工资表 -->
  <uploadPayroll v-if="flag == 2" @refreshTable="refreshTable" ref="uploadPayrollRef" @success="goToSend(0)" />
  <ContentWrap v-if="flag == 3">
    <setUpPayroll :idVal="idVal" :titleName="titleName" :isSendFlag="isSendFlag" ref="setUpPayrollRef"
      @success="goToSend(0)" @refreshTable="refreshTable" />
  </ContentWrap>
</template>
<script lang="ts" setup>
import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'
import { getUserProfile } from '@/api/system/user/profile'
import { Plus, CaretBottom, ArrowRight } from '@element-plus/icons-vue'
import { tr } from 'element-plus/es/locale'
import { useAppStore } from '@/store/modules/app'
import * as payslipApi from '@/api/system/payslip'
import toSend from './toSend.vue'
import uploadPayroll from './uploadPayroll.vue'
import setUpPayroll from './setUpPayroll.vue'
import moment from 'moment'
import dayjs from 'dayjs'
import type { DropdownInstance, ElMessageBox } from 'element-plus'
defineOptions({ name: 'orgstructure' })
import { factory } from 'typescript'
const total = ref(0) // 列表的总页数

const userMessage = ref([]) // 消息弹窗
const loading = ref(false)
const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  month: undefined
})
const currentYearMonth =ref('')
let lastMonth = moment().subtract(1, 'months')
currentYearMonth.value = lastMonth.format('YYYY年MM')


const dataList = ref([])
const idVal = ref('')
const titleName = ref('')
const isSendFlag = ref()

// const monthVal = ref('')
const toSendRef = ref()
const uploadPayrollRef = ref()
const flag = ref(0)
const isVisible = ref(false)
const tabIndex = ref(null)

// 前往发送
const goToSend = async (val, id, name, sendFlag) => {
  console.log(1111)
  flag.value = val
  idVal.value = id
  titleName.value = name
  isSendFlag.value = sendFlag
}
// 下一步提交完刷新表格
const refreshTable = async () => {
  flag.value = 0
  await getSalaryBill()
}

const fanhui = async () => {
  await getSalaryBill()
}

// 鼠标悬浮
const showXian = async (i) => {
  tabIndex.value = i
  isVisible.value = true
}
// 鼠标离开
const hideXian = async () => {
  isVisible.value = false
}
// 上传工资表
const upPayroll = async () => {
  flag.value = 2
}
// 获取表格数据
const getSalaryBill = async () => {
  try {
    loading.value = true
    const data = await payslipApi.salaryBill(queryParams)
    console.log(data)
    dataList.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}
// 选取月份
const changeMonth = async (v) => {
  console.log(v, 'dasasasasasasas')
  if (!v) {
    queryParams.month = undefined
  } else {
    queryParams.month = v + '-01'
    currentYearMonth.value  = v.replace(/-/g, '年')
  }
  getSalaryBill()
}
// 删除
const deleteBill = async (id) => {
  await message.delConfirm()
  await payslipApi.deleteSalaryBill(id)
  message.success(t('common.delSuccess'))
  await getSalaryBill()
}

// 获取用户个人登录信息
// const getUserInfo = async () => {
//   const users = await getUserProfile()
//   userInfo.value = users
// }

// 监听折叠面板
const appStore = useAppStore()
const collapse = computed(() => appStore.getCollapse)
const isCollapse = computed(() => appStore.getCollapse)
watch(
  () => collapse.value,
  (newPath, oldPath) => {
    isCollapse.value = newPath
  }
)
// 监听浏览器宽度
const windowWidth = ref(window.innerWidth)
const handleResize = () => {
  windowWidth.value = window.innerWidth
}

/** 初始化 */
onMounted(() => {
  let now = new Date()
  let year = now.getFullYear()
  let month = (now.getMonth() + 1 + '').padStart(2, '0')

  // 创建一个新的日期对象，以避免改变原始日期对象
  let newDate = new Date(year, month - 1)
  // 将月份往前推一个月
  newDate.setMonth(newDate.getMonth() - 1)
  // 如果新的月份是1月，那么将年份也往前推一年
  if (newDate.getMonth() === 0) {
    newDate.setFullYear(newDate.getFullYear() - 1)
  }
  // 更新年份和月份
  year = newDate.getFullYear()
  month = (newDate.getMonth() + 1).toString().padStart(2, '0')

  queryParams.month = `${year}-${month}-01`
  getSalaryBill()
  window.addEventListener('resize', handleResize)
})
onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
})
</script>

<style lang="scss" scoped>
.payroll {
  width: 135px;
  height: 36px;
  display: flex;
  justify-content: center;
  align-items: center;
  background: #3370ff;
  color: #ffffff;
  line-height: 36px;
  // margin-top: 20px;
  margin-bottom: 15px;
  cursor: pointer;
  border-radius: 5px;
  font-size: 14px;
}

.center {
  margin-top: 20px;

  .content {
    position: relative;
    width: 100%;
    height: 120px;
    background: #ffffff;
    border-radius: 12px;
    border: 1px solid #dcdfe6;
    margin-bottom: 20px;

    .characters {
      // display: flex;
      // justify-content: space-between;
      // align-items: center;
    }

    .xian {
      width: 4px;
      height: 40px;
      background: #3370ff;
      border-radius: 0px 2px 2px 0px;
      position: absolute;
      top: 33%;
    }
  }

  .charfirst {
    line-height: 120px;
    margin-left: 30px;
    font-size: 18px;
    color: #303133;
    font-weight: 500;
  }

  .charsecond {
    margin-top: 30px;

    >p:nth-of-type(1) {
      font-weight: 400;
      font-size: 14px;
      color: #909399;
    }

    >p:nth-of-type(2) {
      font-weight: bold;
      font-size: 24px;
      color: #303133;
    }
  }

  .charthird {
    text-align: center;
    line-height: 120px;
  }

  .content:hover {
    box-shadow: 0 0 10px rgba(0, 123, 255, 0.5);
    // box-shadow: 0 0 10px rgba(255, 255, 255, 0.5)
  }
}

// :deep .el-card{
//   background: #F6F6F6 !important;
// }
.fenye .el-pagination {
  display: flex !important;
  padding-bottom: 20px;
}

.customHeight {
  position: absolute;
  right: 15px;
  left: 15px;
  bottom: 15px;
  box-sizing: border-box;
  top: 15px;
  bottom: 0;
  overflow-y: auto;
}

.placeholderImage {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  font-size: 16px;
  color: #303133;

  p {
    margin: 40px 0 10px;
  }

  ul {
    font-size: 14px;
    color: #909399;
    list-style: disc;

    li {
      margin-bottom: 4px;
    }
  }
}
</style>
