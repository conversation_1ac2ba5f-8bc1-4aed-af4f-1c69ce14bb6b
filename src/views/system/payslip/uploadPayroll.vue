<template>
  <ContentWrap v-loading="loading">
    <div class="top">
      <el-button icon="el-icon-arrowleft" @click="fanhui">返回 </el-button>
      <span>上传工资表</span>
    </div>
    <el-steps
      class="custom-steps"
      style="max-width: 806px; margin: 0 auto; margin-top: 40px"
      :active="active"
      finish-status="success"
    >
      <el-step title="上传Excel工资表"></el-step>
      <el-step title="预览表格数据"></el-step>
      <el-step title="设置工资条"></el-step>
    </el-steps>
    <div class="suo" v-if="flag == 0">
      <div class="uploadCenter">
        <el-upload
          ref="upload"
          :auto-upload="false"
          class="upload-demo"
          :on-exceed="handleExceed"
          :before-upload="onBeforeUpload"
          :on-change="uploadFile"
          :limit="1"
          drag
        >
          <img src="@/assets/imgs/exce.svg" alt="" />
          <!-- <Icon class="el-icon--upload" icon="ep:upload-filled" /> -->
          <div class="el-upload__text">
            <div class="uploadPrompt">直接上传你现有的Excel工资表，无需模版</div>
            <el-button type="primary" style="margin-top: 16px" > 选择工资表 </el-button>
          </div>
          <div @click.stop>
            <el-button link type="primary" style="margin-top: 16px" @click="xiazaishilie">
              下载示例工资表
            </el-button>
          </div>
          <!-- <template #tip>
            <div class="el-upload__tip" style="color: red"> 提示：仅允许导入“excel”格式文件！ </div>
          </template> -->
        </el-upload>
      </div>
      <div class="shuoming">
        <div>说明:</div>
        <ul>
          <!-- <li style="color: red">匹配员工方式:通过用户编号匹配员工</li> -->
          <li>你上传的文件只有你和工资条主管理员能查看</li>
          <li>如需下载示例模版或者获取诺鑫办办通讯录员工信息 </li>
          <li>确保Excel工资表中包含【姓名】和【实发工资】 </li>
          <li>如有员工不在诺鑫办办通讯中，请先将员工添加到诺办办通讯录 </li>
        </ul>
      </div>
    </div>
    <!-- 表格 -->
    <previewTable
      v-if="flag == 1"
      ref="previewTableRef"
      :valId="valId"
      @success="nextStep"
      @reUpload="reUpload"
    />
    <!-- 设置工资条 -->
    <setUpPayroll
      v-if="flag == 2"
      ref="setUpPayrollRef"
      :idVal="valId"
      :isSendFlag="true"
      @reUpload="reUpload"
      @success="nextStep"
      @refreshTable="refreshTable"
    />
  </ContentWrap>
</template>
<script lang="ts" setup>
import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'
import { getUserProfile } from '@/api/system/user/profile'
import { Search, CaretBottom, ArrowRight } from '@element-plus/icons-vue'
import { tr } from 'element-plus/es/locale'
import { useAppStore } from '@/store/modules/app'
import * as payslipApi from '@/api/system/payslip'
import previewTable from './previewTable.vue'
import setUpPayroll from './setUpPayroll.vue'
import download from '@/utils/download'

import dayjs from 'dayjs'
import { genFileId } from 'element-plus'
import type {
  DropdownInstance,
  ElMessageBox,
  UploadInstance,
  UploadProps,
  UploadRawFile
} from 'element-plus'
defineOptions({ name: 'orgstructure' })
import { couldStartTrivia, factory } from 'typescript'
// import { emit } from 'process'

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化
const active = ref(0)
const loading = ref(false) // 列表的加载中
const upload = ref<UploadInstance>()
const flag = ref(0)
const valId = ref('')
const dataList = ref([
  {
    nickname: '哈哈'
  }
])
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  username: ''
})

const emit = defineEmits(['success', 'refreshTable']) // 定义 success 事件，用于操作成功后的回调
// 返回
const fanhui = async () => {
  console.log(valId.value);
  if(valId.value){
    await payslipApi.deleteSalaryBill(valId.value)
  }
  emit('success')
}
const refreshTable = () => {
  emit('refreshTable')
}

const handleExceed: UploadProps['onExceed'] = (files) => {
  // console.log(files);
  upload.value!.clearFiles()
  const file = files[0] as UploadRawFile
  file.uid = genFileId()
  upload.value!.handleStart(file)
}
const onBeforeUpload = (rawFile) => {
  // console.log(rawFile,'filesfiles');
}
const uploadFile = async (rawFile) => {
  try {
    console.log(rawFile, 'filesfiles11111111')
    loading.value = true
    let form = new FormData()
    form.append('file', rawFile.raw)
    const data = await payslipApi.importSalaryBill(form)
    message.success('上传成功')
    console.log(data)
    valId.value = data.data
    if (active.value++ > 2) active.value = 0
    flag.value = 1
  } finally {
    loading.value = false
  }
}
const nextStep = () => {
  if (active.value++ > 2) active.value = 0
  flag.value = 2
}
// 重新上传
const reUpload = async (v) => {
  console.log(v)
  if (v == '第二步') {
    active.value = 0
    flag.value = 0
    await payslipApi.deleteSalaryBill(valId.value)
  } else {
    ElMessageBox.confirm('填写的数据将全部清除,是否重新上传？', '系统提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
      .then(async () => {
        await payslipApi.deleteSalaryBill(valId.value)
        active.value = 0
        flag.value = 0
      })
      .catch(() => {})
  }
}
// 下载示例工资表
const xiazaishilie = async () => {
  try {
    loading.value = true
    const data = await payslipApi.getImportTemplate()
    download.excel(data, '示例工资表模板.xls')
  } finally {
    loading.value = false
  }
}
// 发送
const send = () => {}
// 编辑
const edit = () => {}
// 获取列表数据
const getList = () => {}
// 获取用户个人登录信息
// const getUserInfo = async () => {
//   const users = await getUserProfile()
//   userInfo.value = users
// }

// 监听折叠面板
const appStore = useAppStore()
const collapse = computed(() => appStore.getCollapse)
const isCollapse = computed(() => appStore.getCollapse)
watch(
  () => collapse.value,
  (newPath, oldPath) => {
    isCollapse.value = newPath
  }
)
// 监听浏览器宽度
const windowWidth = ref(window.innerWidth)
const handleResize = () => {
  windowWidth.value = window.innerWidth
}

/** 初始化 */
onMounted(() => {
  window.addEventListener('resize', handleResize)
})
onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
})
</script>

<style lang="scss" scoped>
.top {
  display: flex;
  border-bottom: 1px solid #EFF0F0;
  padding: 10px 0;
  span {
    margin: 0 auto;
    font-weight: bold;
    font-size: 14px;
    color: #303133;
  }
}

.suo {
  padding: 40px 116px;
  .uploadCenter {
    width: 50%;
    margin: 0 auto;
    .uploadPrompt {
      font-size: 14px;
      color: #909399;
    }
  }
  .shuoming {
    border-radius: 5px;
    border: 1px solid #dcdfe6;
    width: 50%;
    margin: 0 auto;
    padding: 22px 0 22px 41px;
    box-sizing: border-box;
    margin-top: 24px;

    > div:nth-of-type(1) {
      font-weight: 500;
      font-size: 14px;
      color: #606266;
      margin-left: -17px;
    }
    > ul {
      list-style-type: disc;
      > li {
        font-weight: 400;
        font-size: 14px;
        color: #909399;
        margin-top: 10px;
      }
    }
  }
}

// :deep .custom-steps .el-step__line {
//   margin-top: 10px;
// }
// :deep .custom-steps .el-step__title {
//   text-align: center; /* 修改标题文字居中 */
//   margin-top: -20px; /* 将标题上移，以对齐线条 */
// }
</style>
