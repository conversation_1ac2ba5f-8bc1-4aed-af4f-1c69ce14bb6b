<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form
      class="-mb-15px"
      :model="queryParams"
      ref="queryFormRef"
      :inline="true"
      label-width="68px"
    >
      <el-form-item label="版本标题" prop="title">
        <el-input
          v-model="queryParams.title"
          placeholder="请输入版本标题"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <!-- <el-form-item label="公告状态" prop="status">
        <el-select
          v-model="queryParams.status"
          placeholder="请选择公告状态"
          clearable
          class="!w-240px"
        >
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.COMMON_STATUS)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item> -->
      <el-form-item>
        <el-button @click="handleQuery"> <Icon icon="ep:search" class="mr-5px" /> 搜索 </el-button>
        <el-button @click="resetQuery"> <Icon icon="ep:refresh" class="mr-5px" /> 重置 </el-button>
        <el-button
          type="primary"
          plain
          @click="openForm('create')"
          v-hasPermi="['system:notice:create']"
        >
          <Icon icon="ep:plus" class="mr-5px" /> 新增
        </el-button>
        <!-- <el-button
          type="primary"
          plain
          @click="handleNotifyList"
          v-hasPermi="['system:notice:recordList']"
        >
          公告列表
        </el-button> -->
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table v-loading="loading" :data="list">
      <el-table-column label="版本编号" align="center" prop="versionNo" />
      <el-table-column label="版本标题" prop="title" />
      <!-- <el-table-column label="版本类型" align="center" prop="type" >
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.SYSTEM_NOTICE_TYPE" :value="scope.row.type" />
        </template>
      </el-table-column> -->
      <el-table-column label="封面" align="center" prop="coverPic"> 
         <template #default="scope">
          <el-image
            v-if="scope.row.coverPic"
            class="h-80px w-80px"
            lazy
            :src="scope.row.coverPic"
            :preview-src-list="[scope.row.coverPic]"
            preview-teleported
            fit="cover"
          />
        </template>
      </el-table-column>
      <el-table-column label="更新图片" align="center" prop="updatePic"> 
         <template #default="scope">
          <el-image
            v-if="scope.row.updatePic"
            class="h-80px w-80px"
            lazy
            :src="scope.row.updatePic"
            :preview-src-list="[scope.row.updatePic]"
            preview-teleported
            fit="cover"
          />
        </template>
      </el-table-column>
      <el-table-column label="版本内容" align="center" prop="content"> 
         <template #default="scope">
          <div v-html="scope.row.content"></div>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" prop="createTime" width="240" :formatter="dateFormatter" />
      <el-table-column label="操作">
        <template #default="scope">
          <el-button
            link
            type="primary"
            @click="openForm('update', scope.row.id)"
            v-hasPermi="['system:notice:update']"
          >
            编辑
          </el-button>
          <el-button
            link
            type="danger"
            @click="handleDelete(scope.row.id)"
            v-hasPermi="['system:notice:delete']"
          >
            删除
          </el-button>
          <!-- <el-button
            link
            @click="handlePush(scope.row.id)"
            v-hasPermi="['system:notice:update']"
            :disabled="scope.row.status == 1"
          >
            推送
          </el-button> -->
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </ContentWrap>

  <!-- 表单弹窗：添加/修改 -->
  <versionInformationFrom ref="formRef" @success="getList" />
  <org-picker title="选择要推送的人员" ref="orgPicker" multiple :selected="[]" @ok="selected" />
</template>
<script lang="ts" setup>
import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'
import { dateFormatter } from '@/utils/formatTime'
import * as VersionApi from '@/api/system/versionInformation'
import versionInformationFrom from './versionInformationFrom.vue'
import { Icon } from '@/components/Icon'
import OrgPicker from '@/components/common/OrgPicker.vue'
defineOptions({ name: 'SystemVersionInformation' })
import { useRouter } from 'vue-router'
const router = useRouter()
const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化

const loading = ref(true) // 列表的加载中
const total = ref(0) // 列表的总页数
const list = ref([]) // 列表的数据
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  title: '',
  type: undefined,
  status: undefined
})
const queryFormRef = ref() // 搜索的表单

/** 查询公告列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await VersionApi.getVersionPage(queryParams)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 添加/修改操作 */
const formRef = ref()
const openForm = (type: string, id?: number) => {
  formRef.value.open(type, id)
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await VersionApi.deleteVersion(id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch {}
}

/** 推送按钮操作 */
const orgPicker = ref()
const myId = ref('')
const handlePush = async (id: string) => {
  myId.value = id
  orgPicker.value.show()
  // return
  // try {
  //   // 推送的二次确认
  //   await message.confirm('是否推送所选中通知？')
  //   // 发起推送
  //   await VersionApi.pushNotice(id)
  //   message.success(t('推送成功'))
  // } catch {}
}
let targetUser = ref<any[]>([])
const selected = (users: any) => {
  console.log('users', users)
  targetUser.value = []
  users.forEach((item) => {
    targetUser.value.push({
      id: item.id,
      type: item.type
    })
  })
  // targetUser.value=users
  console.log(targetUser.value)
  handleSub()
}
const handleSub = async () => {
  console.log(myId.value)
  console.log(targetUser.value)
  try {
    // 推送的二次确认
    await message.confirm('是否推送所选中通知？')
    // 发起推送
    await VersionApi.pushNotice2(myId.value, targetUser.value)
    message.success(t('推送成功'))
  } catch {}
}
const handleNotifyList = () => {
  router.push('/notify/list/recordList')
}
/** 初始化 **/
onMounted(() => {
  getList()
})
</script>
