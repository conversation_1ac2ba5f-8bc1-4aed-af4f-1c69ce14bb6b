<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form
      ref="queryFormRef"
      :inline="true"
      :model="queryParams"
      class="-mb-15px"
      label-width="68px"
    >
      <el-form-item label="组织筛选">
        <div style="max-width: 350px">
          <el-button
            type="primary"
            size="default"
            @click="$refs.orgPicker.show(), pleaseSelectOrganization()"
            >请选择所属组织</el-button
          >
          <org-picker type="dept" multiple ref="orgPicker" :selected="Uservalue" @ok="selected" />
          <div style="margin-top: 5px">
            <el-tag
              size="small"
              style="margin: 5px"
              closable
              v-for="(dept, i) in Uservalue"
              @close="delDept(i)"
              >{{ dept.name }}</el-tag
            >
          </div>
        </div>
      </el-form-item>
      <div class="rightButton">
        <el-form-item>
          <el-button type="primary" @click="importSchedule">
            <img src="../../../assets/imgs/ic9.svg" alt="" />
            导入日程
          </el-button>
        </el-form-item>
      </div>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table v-loading="loading" :data="list">
      <el-table-column align="center" label="日程名称" prop="eventName" />
      <el-table-column align="center" label="所属组织" prop="departmentName" />
      <el-table-column align="center" label="开始时间" prop="eventStartTime" />
      <el-table-column align="center" label="结束时间" prop="eventEndTime" />
      <el-table-column align="center" label="地址" prop="eventAddress" />

      <el-table-column align="center" label="描述" prop="eventDescription" />
      <!-- <el-table-column
        :formatter="dateFormatter"
        align="center"
        label="提醒时间"
        prop="createTime"
        width="180"
      /> -->
      <el-table-column align="center" label="是否过期" prop="eventNowState">
        <template #default="scope">
          <el-tag
            :type="
              scope.row.eventNowState == 1
                ? 'success'
                : scope.row.eventNowState == 2
                ? 'primary'
                : 'info'
            "
            >{{
              scope.row.eventNowState == 1
                ? '未开始'
                : scope.row.eventNowState == 2
                ? '进行中'
                : '已过期'
            }}</el-tag
          >
        </template>
      </el-table-column>
      <el-table-column label="操作">
        <template #default="scope">
          <el-button link type="primary" @click="viewDetails(scope.row.id)"> 查看详情 </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      v-model:limit="queryParams.pageSize"
      v-model:page="queryParams.pageNo"
      :total="total"
      @pagination="getList"
    />
  </ContentWrap>
  <Dialog v-model="dialogVisible" title="导入日程">
    <el-form ref="formRef" v-loading="formLoading" :model="formData" label-width="80px">
      <el-form-item label="选择文件:">
        <div>
          <div>
            <el-button type="primary" link @click="downloadTemplate">点击下载模板</el-button>
            <!-- <el-button link disabled style="color: #525354">填写后并上传</el-button> -->
            <span>, 填写后并上传</span>
          </div>
          <el-upload
            ref="upload"
            class="upload-demo"
            :limit="1"
            :on-exceed="handleExceed"
            :before-upload="onBeforeUpload"
            :on-change="uploadFile"
            :auto-upload="false"
          >
            <template #trigger>
              <el-button type="primary" style="margin-top: 10px">点击上传</el-button>
            </template>
            <!-- <template #tip>
              <div class="el-upload__tip text-red">
                limit 1 file, new file will cover the old file
              </div>
            </template> -->
          </el-upload>

          <!--  -->

          <!-- <el-upload
            ref="upload"
            :auto-upload="false"
            class="upload-demo"
            :on-exceed="handleExceed"
            :before-upload="onBeforeUpload"
            :on-change="uploadFile"
            :limit="1"
            accept=".xlsx"
            drag
          >
            <Icon class="el-icon--upload" icon="ep:upload-filled" />
            <div class="el-upload__text"> 将文件拖到此处，或 <em>点击上传</em></div>
            <template #tip>
              <div class="el-upload__tip" style="color: red">
                提示：仅允许导入“excel”格式文件！
              </div>
            </template>
          </el-upload> -->
        </div>
      </el-form-item>

      <el-form-item label="日程类型:">
        <el-select
          :border="false"
          v-model="importParams.eventType"
          placeholder="请选择"
          style="width: 240px"
        >
          <el-option
            v-for="item in scheduleType"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="所属组织:">
        <div style="max-width: 350px">
          <el-button type="primary" size="default" @click="$refs.orgPicker.show()"
            >选择所属组织</el-button
          >
          <org-picker type="dept" multiple ref="orgPicker" :selected="Uservalue1" @ok="selected" />
          <div style="margin-top: 5px">
            <el-tag
              size="small"
              style="margin: 5px"
              closable
              v-for="(dept, i) in Uservalue1"
              @close="delDept(i)"
              >{{ dept.name }}</el-tag
            >
          </div>
        </div>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="dialogVisible = false">取消</el-button>
      <el-button :disabled="formLoading" type="primary" @click="submitForm">确 定</el-button>
    </template>
  </Dialog>
  <org-picker
    title="选择所属组织"
    ref="orgPicker"
    type="dept"
    multiple
    :selected="flag ? Uservalue1 : Uservalue"
    @ok="selected"
  />
  <!--  -->
  <el-dialog v-model="detailsVisible" title="详情" width="1000">
    <el-table v-loading="loading" :data="detailsList">
      <el-table-column align="center" label="日程名称" prop="eventName" />
      <el-table-column align="center" label="日程时间" prop="eventStartTime" />
      <el-table-column align="center" label="是否过期" prop="eventNowState">
        <template #default="scope">
          <el-tag
            :type="
              scope.row.eventNowState == 1
                ? 'success'
                : scope.row.eventNowState == 2
                ? 'primary'
                : 'info'
            "
            >{{
              scope.row.eventNowState == 1
                ? '未开始'
                : scope.row.eventNowState == 2
                ? '进行中'
                : '已过期'
            }}</el-tag
          >
        </template>
      </el-table-column>
      <el-table-column align="center" label="所属组织" prop="deptName" />
      <el-table-column align="center" label="参与人员" prop="nickname" />
      <el-table-column align="center" label="完成状态" prop="confirmState">
        <template #default="scope">
          <el-tag :type="scope.row.confirmState == 1 ? 'success' : 'info'">{{
            scope.row.confirmState == 1 ? '已完成' : '未完成'
          }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column align="center" label="完成时间" prop="confirmTime">
        <template #default="scope">
          {{ scope.row.confirmTime ? scope.row.confirmTime : '-' }}
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      class="bt"
      v-model:limit="detailsParams.pageSize"
      v-model:page="detailsParams.pageNo"
      :total="total1"
      @pagination="viewDetails"
    />
  </el-dialog>
</template>

<script lang="ts" setup>
import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'
import OrgPicker from '@/components/common/OrgPicker.vue'

import { defaultProps, handleTree } from '@/utils/tree'
import * as RoleApi from '@/api/system/role'
import * as MenuApi from '@/api/system/menu'
import * as PermissionApi from '@/api/system/permission'
import { Icon } from '@/components/Icon'
import download from '@/utils/download'

defineOptions({ name: 'SystemRoleAssignMenuForm' })
// 新的
import { genFileId } from 'element-plus'
import type { UploadInstance, UploadProps, UploadRawFile } from 'element-plus'
import dayjs from 'dayjs'

import { fa } from 'element-plus/es/locale'
import { log } from 'console'
const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const dialogVisible = ref(false)
const detailsVisible = ref(false)
const Uservalue = ref([])
const Uservalue1 = ref([])
const fileList = ref([])
const detailsList = ref([])
const scheduleTypeValue = ref('')
const scheduleType = ref([
  {
    value: '1',
    label: '会议'
  },
  {
    value: '2',
    label: '活动'
  }
])
const upload = ref<UploadInstance>()
const formData = reactive({
  id: 0,
  name: '',
  code: '',
  menuIds: []
})
const loading = ref(false) // 列表的加载中
const flag = ref(false)
const list = ref([
  {
    id: 101,
    name: '测试账号',
    code: 'test',
    sort: 0,
    status: 0,
    type: 2,
    remark: '我想测试',
    dataScope: 2
  }
]) // 列表的数据
const total = ref(0) // 列表的总页数
const total1 = ref(0)
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  departmentIdsStr: ''
  // code: '',
  // name: '',
  // status: undefined,
  // createTime: []
})
const detailsParams = reactive({
  pageNo: 1,
  pageSize: 10,
  departmentCalendarEventId: ''
})
const importParams = reactive({
  deptId: '',
  eventType: '',
  excelFile: ''
})

/** 查询列表数据 */
const getList = async () => {
  // loading.value = true
  queryParams.departmentIdsStr = Uservalue.value.map((item) => item.id).join(',')
  try {
    const data = await RoleApi.selectDepartmentCalenderEventList(queryParams)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}
/** 导入日程操作 */
const importSchedule = () => {
  Uservalue1.value = []
  flag.value = true
  importParams.excelFile = ''
  dialogVisible.value = true
}
const pleaseSelectOrganization = () => {
  flag.value = false
}

const handleExceed: UploadProps['onExceed'] = (files) => {
  // console.log(files);
  upload.value!.clearFiles()
  const file = files[0] as UploadRawFile
  file.uid = genFileId()
  upload.value!.handleStart(file)
}

const onBeforeUpload = (rawFile) => {
  // console.log(rawFile,'filesfiles');
}
const uploadFile = (rawFile) => {
  // console.log(rawFile, 'filesfiles11111111')
  importParams.excelFile = rawFile.raw
}
const selected = (va) => {
  // console.log(va, 'vvvvvvvvvvvvvv')
  if (flag.value) {
    Uservalue1.value = va.map((item, i) => {
      return {
        name: item.name,
        id: item.id
      }
    })
  } else {
    Uservalue.value = va.map((item, i) => {
      return {
        name: item.name,
        id: item.id
      }
    })
    getList()
  }

  // console.log(Uservalue.value)
}
const delDept = (i) => {
  // console.log(Uservalue.value, 'Uservalue.valueUservalue.value')
  if (flag.value) {
    Uservalue1.value.splice(i, 1)
  } else {
    Uservalue.value.splice(i, 1)
    getList()
  }
}

const submitForm = async () => {
  let form = new FormData()
  form.append('deptId', Uservalue1.value.map((item) => item.id).join(','))
  form.append('eventType', importParams.eventType)
  form.append('excelFile', importParams.excelFile)
  try {
    const data = await RoleApi.importDepartmentCalenderEvent(form)
    dialogVisible.value = false
    if (data.code != 0) return message.error(data.msg)
    getList()
    message.success('导入成功')
  } finally {
    loading.value = false
  }
}
// 下载模板
const downloadTemplate = async () => {
  try {
    const data = await RoleApi.dowCalenderEventExcel()
    download.excel(data, '日历事件模板.xls')
  } finally {
    loading.value = false
  }
}

const viewDetails = async (departmentCalendarEventId) => {
  detailsVisible.value = true
  detailsParams.departmentCalendarEventId = departmentCalendarEventId
  try {
    const data = await RoleApi.selectDepartmentEventUserConfirmList(detailsParams)
    // console.log(data)
    detailsList.value = data.list
    total1.value = data.total
  } finally {
  }
}

/** 初始化 **/
onMounted(() => {
  getList()
})
</script>
<style lang="scss" scoped>
.rightButton {
  float: right;
}
.bt {
  float: none !important;
  display: flex;
  justify-content: flex-end;
}
</style>
