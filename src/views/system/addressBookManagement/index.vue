<template>
  <doc-alert title="用户体系" url="https://doc.iocoder.cn/user-center/" />
  <doc-alert title="三方登陆" url="https://doc.iocoder.cn/social-user/" />
  <doc-alert title="Excel 导入导出" url="https://doc.iocoder.cn/excel-import-and-export/" />

  <el-row :gutter="20">
    <!-- 左侧部门树 -->
    <el-col :span="4" :xs="24">
      <ContentWrap class="h-1/1 leftTreeBox yincang">
        <DeptTree @node-click="handleDeptNodeClick" />
      </ContentWrap>
    </el-col>
    <el-col :span="20" :xs="24">
      <ContentWrap>
        <div class="topes">
          <div class="topH">
            <span>{{
              currentListes && currentListes.length > 0 ? currentListes[0].name : ''
            }}</span>
          </div>
          <el-button type="primary" size="default" @click="addUsers"> 添加成员 </el-button>
          <el-button size="default" @click="handleAdd" v-show="wholeName != '全部'">
            邀请成员
          </el-button>
        </div>
        <el-table v-loading="loading" :data="list" @cell-click="handleCellClick">
          <!-- <el-table v-loading="loading" :data="list" @row-click="handleCellClick"> -->
          <!-- <el-table-column type="selection" width="55"></el-table-column> -->
          <el-table-column label="名称" prop="nickname" width="300">
            <template #default="scope">
              <div class="rightBottom">
                <div class="rightBottomFF">
                  <div class="userYuan">
                    {{
                      scope.row.nickname.length >= 3
                        ? scope.row.nickname.substring(1, 3)
                        : scope.row.nickname
                    }}
                  </div>
                  <span style="margin-left: 4px">{{ scope.row.nickname }} </span>
                  <span
                    class="labeltext"
                    v-for="(item, i) in scope.row.labels"
                    :key="i"
                    :style="{ border: `1px solid ${item.color}`, color: `${item.color}` }"
                    >{{ item.label }}</span
                  >
                </div>
              </div>
            </template>
          </el-table-column>
          <!-- <el-table-column
            label="账号类型"
            prop="nickname"
            align="center"
            :show-overflow-tooltip="true"
          /> -->
          <el-table-column label="账号状态" key="status">
            <template #default="scope">
              <div @click.stop>
                <el-switch
                  v-model="scope.row.status"
                  :active-value="0"
                  :inactive-value="1"
                  @change="handleStatusChange(scope.row)"
                />
              </div>
            </template>
          </el-table-column>
          <el-table-column
            label="职位"
            prop="postName"
            align="center"
            :show-overflow-tooltip="true"
          />
          <el-table-column label="工号" prop="" align="center" :show-overflow-tooltip="true" />
          <el-table-column label="邮箱" prop="email" align="center" width="150" />
          <el-table-column :width="100" label="操作">
            <template #default="scope">
              <div class="flex items-center justify-left" @click.stop>
                <el-dropdown @command="(command) => handleCommand(command, scope.row)">
                  <el-button type="primary" link> 更多 </el-button>
                  <template #dropdown>
                    <el-dropdown-menu>
                      <el-dropdown-item command="handleEditUser"> 编辑人员信息 </el-dropdown-item>
                      <el-dropdown-item command="handleDelete"> 删除 </el-dropdown-item>
                      <el-dropdown-item command="handleRole" v-if="wholeName != '全部'">
                        {{ scope.row.isManager ? '取消设为主管' : '设置为主管' }}
                      </el-dropdown-item>
                    </el-dropdown-menu>
                  </template>
                </el-dropdown>
              </div>
            </template>
          </el-table-column>
          <!-- <el-table-column label="状态" key="status">
            <template #default="scope">
              <el-switch v-model="scope.row.status" :active-value="0" :inactive-value="1"
                @change="handleStatusChange(scope.row)" />
            </template>
          </el-table-column> -->
        </el-table>
        <Pagination
          :total="total"
          v-model:page="queryParams1.pageNo"
          v-model:limit="queryParams1.pageSize"
          @pagination="getList"
        />
      </ContentWrap>
    </el-col>
  </el-row>

  <div class="bianji">
    <el-drawer v-model="drawer" :title="isti" size="500px">
      <!-- title="I am the title"  -->

      <!-- <template #header>
      <h4>set title by slot</h4>
    </template> -->
      <!-- <el-form ref="formRef"  label-width="100px" label-position="top"> -->
      <el-tabs v-model="activeName" class="demo-tabs" @tab-click="handleClick">
        <el-tab-pane label="基础信息" name="first">
          <!-- <div class="drawtop">
            <div>手机端展示信息</div>
            <div></div>
            <div>自定义手机端展示的信息</div>
          </div> -->
          <!-- <el-alert type="info" show-icon class="alertTl">
            <div
              >[智能人事] 提供更丰富的员工档案信息,可自动生成员工成长记录
              <span class="zhineng">
                <el-button type="primary" link @click="allConfirmed"> 确定 </el-button>
              </span>
            </div>
          </el-alert> -->
          <div class="biaodan">
            <el-form
              class="-mb-15px"
              :model="queryParams"
              ref="queryFormRef"
              :rules="formRules"
              label-width="100px"
              label-position="top"
            >
              <el-form-item label="姓名" prop="nickname">
                <!-- <el-input
                  v-model="queryParams.nickname"
                  clearable
                  placeholder="请输入"
                  class="!w-240px"
                /> -->
                <el-input v-model="queryParams.nickname" clearable placeholder="请输入" />
              </el-form-item>
              <el-form-item label="邮箱">
                <el-input v-model="queryParams.email" placeholder="请输入" clearable />
              </el-form-item>

              <el-form-item label="部门" prop="deptId">
                <!-- <el-tree-select
                  v-model="queryParams.deptId"
                  :data="deptList"
                  :props="defaultProps"
                  check-strictly
                  node-key="id"
                   class="!w-240px"
                  placeholder="请选择部门"
                /> -->

                <!-- <el-select
                  v-model="queryParams.deptId"
                  filterable
                  multiple
                  placeholder="用户状态"
                  @focus="openDialog"
                  class="!w-240px"
                >
                  <el-option
                    v-for="item in Uservalue"
                    :key="item.id"
                    :label="item.name"
                    :value="item.id"
                  />
                </el-select> -->

                <div style="max-width: 350px">
                  <el-button type="primary" size="default" round @click="$refs.orgPicker.show()"
                    >请选择部门</el-button
                  >
                  <div style="margin-top: 5px">
                    <el-tag
                      size="small"
                      style="margin: 5px"
                      closable
                      v-for="(dept, i) in Uservalue"
                      @close="delDept(i, 1)"
                      >{{ dept.name }}</el-tag
                    >
                  </div>
                </div>
              </el-form-item>
              <el-form-item label="直属主管">
                <div style="max-width: 350px">
                  <el-button type="primary" size="default" round @click="$refs.orgPicker1.show()"
                    >请选择直属主管</el-button
                  >
                  <div style="margin-top: 5px">
                    <el-tag
                      size="small"
                      style="margin: 5px"
                      closable
                      v-for="(dept, i) in Uservalue1"
                      @close="delDept(i, 2)"
                      >{{ dept.name }}</el-tag
                    >
                  </div>
                </div>
              </el-form-item>
              <el-form-item label="职位">
                <el-select v-model="queryParams.postIds" multiple placeholder="请选择">
                  <el-option
                    v-for="item in postList"
                    :key="item.id"
                    :label="item.name"
                    :value="item.id"
                  />
                </el-select>
              </el-form-item>
              <el-form-item label="手机" prop="username">
                <!-- <el-input v-model="queryParams.mobile"  suffix-icon="el-icon-view" @suffix-click="handleSuffixClick" placeholder="请输入" clearable /> -->
                <el-input
                  v-if="isti == '添加成员'"
                  v-model="queryParams.username"
                  placeholder="请输入"
                  clearable
                />
                <el-input
                  v-else
                  v-model="queryParams.username"
                  disabled
                  placeholder="请输入"
                  clearable
                >
                </el-input>
                <div class="yijihuo">已激活的用户，无法修改手机号。</div>

                <!-- <div @click="handleIconClick" v-else>
                  <el-input
                    v-model="queryParams.mobile"
                    disabled
                    placeholder="请输入"
                    class="my-input"
                    style="pointer-events:none"
                    clearable
                  >
                    <template #append>
                    <i class="el-icon-search" @click="handleIconClick"></i>
                  </template>
                  </el-input>
                </div> -->
              </el-form-item>
              <el-form-item label="分机号">
                <el-input v-model="queryParams.telePnone" placeholder="请输入" clearable />
              </el-form-item>
              <el-form-item label="办公地点">
                <el-input v-model="queryParams.address" clearable placeholder="请输入" />
              </el-form-item>
              <div>
                <el-form-item label="备注">
                  <el-input v-model="queryParams.remark" clearable placeholder="请输入" />
                </el-form-item>
                <div class="tiems">
                  <el-form-item label="入职时间">
                    <el-date-picker
                      v-model="queryParams.entryTime"
                      type="date"
                      placeholder="请选择"
                      value-format="YYYY-MM-DD"
                    />
                  </el-form-item>
                </div>

                <el-form-item label="工号">
                  <el-input v-model="queryParams.workNo" clearable placeholder="请输入" />
                </el-form-item>
                <el-form-item label="角色">
                  <el-select v-model="queryParams.roleIds" multiple placeholder="请选择">
                    <el-option
                      v-for="item in roleList"
                      :key="item.id"
                      :label="item.name"
                      :value="item.id"
                    />
                  </el-select>
                </el-form-item>
                <el-form-item label="高管模式">
                  <div style="color: #b9babb; margin-right: 178px; margin-top: -15px"
                    >若开启，手机号码对所有员工隐藏</div
                  >
                  <el-switch
                    v-model="queryParams.seniorManagerFlag"
                    active-value="1"
                    size="large"
                    inactive-value="0"
                    style="margin-top: -15px"
                  />
                </el-form-item>
                <el-form-item v-if="queryParams.seniorManagerFlag == 1">
                  <div class="tonghua">
                    <el-checkbox v-model="checked1" class="moren" disabled label="屏蔽普通电话" />
                    <el-checkbox v-model="checked2" label="屏蔽视频通话" />
                    <el-checkbox v-model="checked3" label="屏蔽语音通话" />
                  </div>
                </el-form-item>
              </div>
            </el-form>
          </div>
          <!-- <div class="bott">
            <div>手机端不展示信息</div>
            <div> </div>
          </div> -->
          <!-- <div class="biaodan">
            <el-form
              class="-mb-15px"
              :model="queryParams"
              ref="queryFormRef"
              :rules="formRules"
              label-width="68px"
            >
              <el-form-item label="角色" prop="nickname">
                <el-input
                  v-model="queryParams.nickname"
                  placeholder="请输入员工账号"
                  clearable
                  @keyup.enter="handleQuery"
                  class="!w-240px"
                />
              </el-form-item>
              <el-form-item label="工号" prop="mobile">
                <el-input
                  v-model="queryParams.mobile"
                  placeholder="请输入手机号码"
                  clearable
                  @keyup.enter="handleQuery"
                  class="!w-240px"
                />
              </el-form-item>
              <el-form-item label="分机号" prop="deptId">
                <el-select
                  v-model="queryParams.deptId"
                  placeholder="用户状态"
                  clearable
                  class="!w-240px"
                >
                  <el-option
                    v-for="dict in getIntDictOptions(DICT_TYPE.COMMON_STATUS)"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  />
                </el-select>
              </el-form-item>
              <el-form-item label="办公地点">
                <el-input
                  v-model="queryParams.username"
                  clearable
                  @keyup.enter="handleQuery"
                  class="!w-240px"
                />
              </el-form-item>
              <el-form-item label="入职时间">
                <el-input
                  v-model="queryParams.username"
                  clearable
                  @keyup.enter="handleQuery"
                  class="!w-240px"
                />
              </el-form-item>
              <el-form-item label="备注">
                <el-input
                  v-model="queryParams.username"
                  clearable
                  @keyup.enter="handleQuery"
                  class="!w-240px"
                />
              </el-form-item>
              <el-form-item label="对外职位">
                <el-radio-group v-model="appointment" class="ml-4">
                  <el-radio value="1" size="large">与内部职位同步</el-radio>
                  <el-radio value="2" size="large">设置专属对外职位</el-radio>
                </el-radio-group>
              </el-form-item>
              <el-form-item label="号码隐藏">
                <el-radio-group v-model="numberHidden" class="ml-4">
                  <el-radio value="1" size="large">开</el-radio>
                  <el-radio value="2" size="large">关</el-radio>
                </el-radio-group>
              </el-form-item>
              <el-form-item label="高管模式">
                <el-radio-group v-model="executive" class="ml-4">
                  <el-radio value="1" size="large">开</el-radio>
                  <el-radio value="2" size="large">关</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-form>
          </div> -->
        </el-tab-pane>
        <!-- <el-tab-pane label="Config" name="second">Config</el-tab-pane> -->
        <!-- <el-tab-pane label="Role" name="third">Role</el-tab-pane> -->
        <!-- <el-tab-pane label="Task" name="fourth">Task</el-tab-pane> -->
      </el-tabs>
      <!-- </el-form> -->
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="drawer = false">取消</el-button>
          <el-button type="primary" @click="allConfirmed"> 确定 </el-button>
        </div>
      </template>
    </el-drawer>
  </div>

  <org-picker multiple ref="orgPicker" type="dept" :selected="Uservalue" @ok="selected" />
  <org-picker multiple ref="orgPicker1" type="user" :selected="Uservalue1" @ok="selected1" />
  <org-picker multiple ref="orgPicker2" type="user" :selected="Uservalue2" @ok="selected2" />
  <!-- 邀请人员 -->
  <addDrawer :id="ides" :info="userInfo" ref="addDrawerRef"></addDrawer>

  <!-- 添加或修改用户对话框 -->
  <!-- <UserForm ref="formRef" @success="getList" /> -->
  <!-- 用户导入对话框 -->
  <!-- <UserImportForm ref="importFormRef" @success="getList" /> -->
  <!-- 分配角色 -->
  <!-- <UserAssignRoleForm ref="assignRoleFormRef" @success="getList" /> -->
</template>
<script lang="ts" setup>
import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'
import { checkPermi } from '@/utils/permission'
import { dateFormatter } from '@/utils/formatTime'
import download from '@/utils/download'
import { defaultProps, handleTree } from '@/utils/tree'
import { CommonStatusEnum } from '@/utils/constants'
import * as UserApi from '@/api/system/user'
import { FormRules } from 'element-plus'
import * as DeptApi from '@/api/system/dept'
import * as PostApi from '@/api/system/post'
import OrgPicker from '@/components/common/OrgPicker.vue'
import * as RoleApi from '@/api/system/role'
import { getUserProfile } from '@/api/system/user/profile'

import addDrawer from '../../system/orgstructure/addUser.vue'

// updateDept

// import { nextTick } from 'vue';
import * as BookApi from '@/api/system/addressBookManagement'
// import UserForm from './UserForm.vue'
// import UserImportForm from './UserImportForm.vue'
// import UserAssignRoleForm from './UserAssignRoleForm.vue'
import DeptTree from './DeptTree.vue'
import { Icon } from '@/components/Icon'
import { constant } from 'lodash-es'
import { el } from 'element-plus/es/locale'
defineOptions({ name: 'SystemUser' })
const formRules = reactive<FormRules>({
  // username: [{ required: true, message: '手机号不能为空', trigger: 'blur' }],
  nickname: [{ required: true, message: '员工姓名不能为空', trigger: 'blur' }],
  // password: [{ required: true, message: '登录密码不能为空', trigger: 'blur' }],
  deptId: [{ required: true, message: '归属部门不能为空', trigger: 'change' }],
  // postIds: [{ required: true, message: '岗位不能为空', trigger: 'change' }],
  // email: [
  //   {
  //     type: 'email',
  //     message: '请输入正确的邮箱地址',
  //     trigger: ['blur', 'change']
  //   }
  // ],
  username: [
    {
      required: true,
      pattern: /^(?:(?:\+|00)86)?1(?:3[\d]|4[5-79]|5[0-35-9]|6[5-7]|7[0-8]|8[\d]|9[189])\d{8}$/,
      message: '请输入正确的手机号码',
      trigger: 'blur'
    }
  ]
})
const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化
const deptList = ref<Tree[]>([]) // 树形结构
const postList = ref([] as PostApi.PostVO[]) // 岗位列表
const roleList = ref([] as RoleApi.RoleVO[]) // 角色的列表

const loading = ref(true) // 列表的加载中
const drawer = ref(false)
const isTableClicked = ref(false)
const total = ref(0) // 列表的总页数
const list = ref([]) // 列表的数
const checkList = ref(['普通通话', '视频通话', '语音通话'])

const checked1 = ref(true)
const checked2 = ref(true)
const checked3 = ref(true)

const Uservalue = ref([])
const Uservalue1 = ref([])
const Uservalue2 = ref([])
const currentListes = ref([])
const orgPicker = ref(null)

const rowValue = ref('')

const wholeName = ref('全部')

const appointment = ref('')
const isti = ref('')
const numberHidden = ref('')
const executive = ref('')
const activeName = ref('first')
const queryParams = reactive({
  // pageNo: 1,
  // pageSize: 10,
  username: undefined,
  // mobile: undefined,
  // status: undefined,
  deptId: [],
  // createTime: [],
  //
  nickname: undefined,
  email: undefined,
  postIds: [],
  telePnone: undefined,
  address: undefined,
  remark: undefined,
  entryTime: undefined,
  workNo: undefined,
  roleIds: undefined,
  managerId: undefined,
  seniorManagerFlag: '0'
})
const queryParams1 = reactive({
  pageNo: 1,
  pageSize: 10,
  username: undefined,
  mobile: undefined,
  status: undefined,
  deptId: undefined,
  createTime: []
})
const queryFormRef = ref() // 的表单

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await UserApi.getUserPage(queryParams1)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams1.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  handleQuery()
}

/** 处理部门被点击 */
const handleDeptNodeClick = async (row) => {
  // console.log(row)
  if (row.name == '全部') {
    wholeName.value = row.name
    await getUserInfo()
  } else {
    wholeName.value = ''
    ides.value = row.id
  }
  queryParams1.deptId = row.id
  await getList()
}

/** 添加/修改操作 */
const formRef = ref()
const openForm = (type: string, id?: number) => {
  formRef.value.open(type, id)
}

/** 用户导入 */
const importFormRef = ref()
const handleImport = () => {
  importFormRef.value.open()
}

/** 修改用户状态 */
const handleStatusChange = async (row: UserApi.UserVO) => {
  try {
    // 修改状态的二次确认
    const text = row.status === CommonStatusEnum.ENABLE ? '启用' : '停用'
    await message.confirm('确认要"' + text + '""' + row.username + '"用户吗?')
    // 发起修改状态
    await UserApi.updateUserStatus(row.id, row.status)
    // 刷新列表
    await getList()
  } catch {
    // 取消后，进行恢复按钮
    row.status =
      row.status === CommonStatusEnum.ENABLE ? CommonStatusEnum.DISABLE : CommonStatusEnum.ENABLE
  }
}

/** 导出按钮操作 */
const exportLoading = ref(false)
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await UserApi.exportUser(queryParams)
    download.excel(data, '用户数据.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}

/** 操作分发 */
const handleCommand = (command, row, event) => {
  isTableClicked.value = false
  // event.stopPropagation()
  switch (command) {
    case 'handleDelete':
      handleDelete(row.id)
      break
    case 'handleEditUser':
      handleEditUser(row.id, row.nickname)
      break
    case 'handleRole':
      handleRole(row)
      break
    default:
      break
  }
}
// 获取用户个人登录信息
const userInfo = ref({})
const ides = ref('')

const getUserInfo = async () => {
  const users = await getUserProfile()
  userInfo.value = users
  ides.value = users.dept.id
}
// 邀请成员
const addDrawerRef = ref()
const handleAdd = async () => {
  addDrawerRef.value.open()
}
// 编辑人员信息
const EditUserId = ref('')
const handleEditUser = (id, nickname) => {
  // console.log(id)

  // drawer.value = true
  isti.value = nickname
  getUser(id)
  EditUserId.value = id
}
const getUser = async (id) => {
  const data = await UserApi.getUser(id)
  // queryFormRef.value?.resetFields()
  queryFormRef.value?.resetFields()

  // console.log(data)
  queryParams.nickname = data.nickname
  queryParams.email = data.email
  queryParams.postIds = data.postIds
  // queryParams.mobile = data.mobile
  queryParams.username = data.username
  queryParams.telePnone = data.telePnone
  queryParams.address = data.address
  queryParams.remark = data.remark
  queryParams.entryTime = data.entryTime
  queryParams.workNo = data.workNo
  queryParams.roleIds = data.roleIds
  checked1.value = data.commonCallFlag == 0 ? false : true
  checked2.value = data.videoCallFlag == 0 ? false : true
  checked3.value = data.voiceCallFlag == 0 ? false : true
  queryParams.seniorManagerFlag = data.seniorManagerFlag + ''
  Uservalue.value = []
  data.depts.map((item) => {
    Uservalue.value.push({
      name: item.deptName,
      id: item.deptId + ''
    })
  })
  // Uservalue.value = [
  //   {
  //     name: data.deptName,
  //     id: data.deptId + ''
  //   }
  // ]
  drawer.value = true
}
const handleIconClick = () => {
  // console.log(5555)
}
//添加人员信息
const addUsers = () => {
  queryFormRef.value?.resetFields()
  queryParams.username = undefined
  // queryParams.mobile = undefined
  queryParams.deptId = []
  queryParams.nickname = undefined
  queryParams.email = undefined
  queryParams.postIds = []
  queryParams.telePnone = undefined
  queryParams.address = undefined
  queryParams.remark = undefined
  queryParams.entryTime = undefined
  queryParams.workNo = undefined
  queryParams.roleIds = undefined
  queryParams.managerId = undefined
  queryParams.seniorManagerFlag = '0'
  Uservalue.value = []
  isti.value = '添加成员'
  drawer.value = true
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await UserApi.deleteUser(id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch {}
}

/** 重置密码 */
const handleResetPwd = async (row: UserApi.UserVO) => {
  try {
    // 重置的二次确认
    const result = await message.prompt(
      '请输入"' + row.username + '"的新密码',
      t('common.reminder')
    )
    const password = result.value
    // 发起重置
    await UserApi.resetUserPwd(row.id, password)
    message.success('修改成功，新密码是：' + password)
  } catch {}
}

/** 是否设为主管 */
const assignRoleFormRef = ref()
const handleRole = async (row) => {
  // console.log(row)
  // BookApi.setDeptLeader()
  try {
    // BookApi.setDeptLeader({ userId: row.id, deptId: row.deptId, isManager: row.isManager })
    BookApi.setDeptLeader({ userId: row.id, deptId: ides.value, isManager: row.isManager })
    message.success('设置成功')
    getList()
  } finally {
  }

  // return
  // const data = await DeptApi.updateDept({ id: row.deptId, leaderUserId: row.id })
}
// 点击表格某一列触发

const handleCellClick = async (row) => {
  // console.log(row)
  isti.value = row.nickname
  getUser(row.id)
  EditUserId.value = row.id

  if (isTableClicked.value) {
  } else {
    isTableClicked.value = true
  }
  // rowValue.value = row.id
  // queryParams.nickname = row.nickname
  // queryParams.email = row.email
  // queryParams.postIds = row.postIds
  // queryParams.mobile = row.mobile
  // queryParams.telePnone = row.telePnone
  // queryParams.address = row.address
  // queryParams.remark = row.remark
  // queryParams.entryTime = row.entryTime
  // queryParams.workNo = row.workNo
  // queryParams.roleIds = row.roleIds
  // drawer.value = true
}
const handleClick = (tab, event) => {
  // console.log(tab, event)
}

// 加载部门树
const getSimpleDeptList = async () => {
  deptList.value = handleTree(await DeptApi.getSimpleDeptList())
}
// 加载职位列表
const getSimplePostList = async () => {
  postList.value = await PostApi.getSimplePostList()
}
// 获得角色列表
const getSimpleRoleList = async () => {
  roleList.value = await RoleApi.getSimpleRoleList()
}

const openDialog = async () => {
  // console.log(11111111111111)
  if (orgPicker.value) {
    orgPicker.value.show()
  }
}

const selected = (va) => {
  Uservalue.value = va.map((item, i) => {
    return {
      name: item.name,
      id: item.id,
      type: item.type
    }
  })
}
const selected1 = (va) => {
  Uservalue1.value = va.map((item, i) => {
    return {
      name: item.name,
      id: item.id,
      type: item.type
    }
  })
}
const selected2 = (va) => {
  Uservalue2.value = va.map((item, i) => {
    return {
      name: item.name,
      id: item.id,
      type: item.type
    }
  })
}
const delDept = (i, n) => {
  if (n == 1) {
    Uservalue.value.splice(i, 1)
  } else if (n == 2) {
    Uservalue1.value.splice(i, 1)
  } else {
    Uservalue2.value.splice(i, 1)
  }
}
// 确认弹窗
const allConfirmed = async () => {
  console.log(checkList.value)

  // queryParams.commonCallFlag = checked1.value ? 1 : 0 //普通通话
  queryParams.commonCallFlag = 1 //普通通话
  queryParams.videoCallFlag = checked2.value ? 1 : 0 //视频通话
  queryParams.voiceCallFlag = checked3.value ? 1 : 0 //语音通话
  // console.log(queryParams, 'queryParamsqueryParams')

  // return

  // console.log(Uservalue.value)
  if (!queryParams.nickname) return message.error('请输入姓名')
  if (!queryParams.username) return message.error('请输入手机号')
  if (Uservalue.value.length <= 0) return message.error('请选择部门')
  // return
  Uservalue.value.map((item) => {
    queryParams.deptId.push(item.id)
  })
  // queryParams.deptId = Uservalue.value.map((item) => item.id).join(',')
  queryParams.managerId = Uservalue1.value.map((item) => item.id).join(',')
  // console.log(queryParams)
  // return
  if (isti.value == '添加成员') {
    await BookApi.addUsers(queryParams)
    message.success('添加成功')
  } else {
    // console.log(EditUserId.value)
    queryParams.id = EditUserId.value
    await BookApi.editUser(queryParams)
    message.success('操作成功')
  }
  getList()
  drawer.value = false
}

/** 初始化 */
onMounted(() => {
  getList()
  getSimpleDeptList()
  getSimplePostList()
  getSimpleRoleList()
  getUserInfo()
  currentListes.value = JSON.parse(localStorage.getItem('currentList'))
})
</script>
<style lang="less" scoped>
.leftTreeBox {
  height: 764px;
  overflow-y: auto;
}
.drawtop {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20px;
  > div:nth-of-type(2) {
    width: 200px;
    height: 0px;
    border: 0.1px solid #efefef;
  }
}
.zhineng {
  margin-left: 15px;
}
:deep .el-alert--info.is-light {
  color: #0089ff;
}
:deep.el-alert--info {
  background: #ecf6fe;
  border: 1px solid #cae6fc;
}
:deep.el-alert .el-alert__close-btn {
  top: 16px;
}
.alertTl {
  margin-bottom: 20px;
}
.biaodan {
  // display: flex;
  // justify-content: center;
}
.biaodan .el-form-item--default {
  // margin-bottom: 30px;
}
.bott {
  display: flex;
  align-items: center;
  > div:nth-of-type(2) {
    width: 70%;
    height: 0px;
    border: 0.1px solid #efefef;
    margin-left: 5px;
  }
}
.topH {
  margin-bottom: 20px;
}
.topes {
  margin-bottom: 20px;
}
.rightBottom {
  // display: flex;
  // align-items: center;
  // justify-content: space-between;
  // border-radius: 5px;
  // padding: 8px 6px;
  // margin: 8px 10px;
  // cursor: pointer;
  // font-size: 14px;

  // img {
  //   vertical-align: middle;
  //   width: 36px;
  //   height: 36px;
  // }

  .rightBottomFF {
    display: flex;
    align-items: center;
  }

  .userYuan {
    width: 36px;
    height: 36px;
    background: #3370ff;
    border-radius: 5px;
    text-align: center;
    line-height: 36px;
    color: #ffffff;
    box-sizing: border-box;
    font-size: 12px;
  }
  .labeltext {
    width: 48px;
    height: 18px;
    border-radius: 4px;
    font-size: 10px;
    text-align: center;
    line-height: 18px;
    margin-left: 8px;
    padding: 1px;
    font-weight: bold;
  }
}
.yijihuo {
  color: #a2a3a5;
  font-size: 12px;
}
:deep.yincang .el-tree-node__label {
  white-space: nowrap !important;
  overflow: hidden;
  text-overflow: ellipsis;
}
.tonghua {
  width: 100%;
  background: #f2f4f8;
  border-radius: 5px;
  padding: 10px;
  margin: 0 auto;
  margin-top: -8px;
  :deep.el-checkbox__label {
    color: #333333;
    font-weight: bold;
  }
}
</style>

<style lang="less">
.bianji .el-date-editor.el-input,
.bianji.el-date-editor.el-input__wrapper {
  width: 100% !important;
}
</style>