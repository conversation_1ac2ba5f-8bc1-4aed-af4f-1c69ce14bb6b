<template>
  <ContentWrap>
    <el-form :model="queryParams" ref="queryFormRef" :inline="true">
      <el-form-item>
        <el-date-picker
          v-model="timeValue"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          @change="timeChange"
          :clearable="false"
          class="!w-220px"
          value-format="YYYY-MM-DD"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-select v-model="selectType" class="!w-140px" @change="selectChange">
          <el-option label="全公司" value="0" />
          <el-option label="考勤组" value="1" />
          <el-option label="部门/人员" value="2" />
          <!-- <el-option label="已离职人员" value="3" /> -->
        </el-select>
      </el-form-item>
      <el-form-item v-if="selectType == 1">
        <el-select
          v-model="groupIds"
          class="!w-280px"
          filterable
          multiple
          @visible-change="groupVisible"
          @remove-tag="removeTag"
          @change="groupChange"
        >
          <el-option
            :label="item.name"
            :value="item.id"
            v-for="(item, index) in groupList"
            :key="index"
          />
        </el-select>
      </el-form-item>
      <el-form-item v-if="selectType == 2">
        <div @click="inputFocus" class="deptClass">
          <el-input
            readonly
            :placeholder="targetUser.length > 0 ? '' : '请选择部门/人员'"
            :style="{ width: dynamicWidth + 'px', height: inputHeight + 'px' }"
          />
          <org-picker
            title="请选择"
            ref="orgPicker"
            multiple
            :selected="targetUser"
            @ok="selected"
          />
          <div v-if="targetUser.length > 0" class="tagItem" ref="tagItemRef">
            <!-- <el-tag type="info">{{ targetUser[0].name }}</el-tag> -->
            <el-tag
              type="info"
              closable
              v-for="(item, i) in targetUser"
              :key="i"
              @close="closeTag(item)"
              >{{ item.name }}</el-tag
            >
            <!-- <el-tooltip effect="light">
              <template #content>
                <el-tag
                  type="info"
                  v-for="(item, index) in targetUser.slice(1)"
                  :key="index"
                  class="custom-tagHover"
                >
                  {{ item.name }}
                </el-tag>
              </template>
              <el-tag type="info" class="isAddBox" v-if="targetUser.length > 1">+1</el-tag>
            </el-tooltip> -->
          </div>
        </div>
      </el-form-item>
    </el-form>
    <el-button type="primary" @click="exportGroup" class="mBT">导出报表</el-button>
    <!-- <el-button  @click="exportGroup" class="mBT">导出记录</el-button> -->
    <el-table v-loading="loading" :data="Bglist">
      <el-table-column align="center" label="姓名" prop="userName" />
      <el-table-column align="center" label="考勤组" prop="groupName" />
      <el-table-column align="center" label="考勤班次" prop="shiftName" />
      <el-table-column align="center" label="部门" prop="deptName" />
      <el-table-column align="center" label="工号" prop="jobNumber" />
      <el-table-column align="center" label="职位" prop="jobTitle" />
      <el-table-column align="center" label="考勤日期" width="180" prop="workDate" />
      <el-table-column align="center" label="考勤时间" width="180" prop="planCheckTime" />
      <el-table-column align="center" label="打卡时间" prop="userCheckTime" />
      <el-table-column align="center" label="打卡结果" prop="userCheckResult" />
      <el-table-column align="center" label="打卡地址" prop="userLocation" />
      <el-table-column align="center" label="打卡备注" prop="userRemark" />
      <el-table-column align="center" label="异常打卡原因" prop="checkExceptionReason" />
      <!-- <el-table-column :width="300" label="操作">
        <template #default="scope">
          <el-button link type="primary" @click="openForm(scope.row.id)"> 编辑 </el-button>
          <el-button link type="danger" @click="handleDelete(scope.row.id)"> 删除 </el-button>
        </template>
      </el-table-column> -->
    </el-table>
    <!-- 分页 -->
    <Pagination
      v-model:limit="queryParams.pageSize"
      v-model:page="queryParams.pageNo"
      :total="total"
      @pagination="listData"
    />
  </ContentWrap>
  <!--  -->
</template>

<script lang="ts" setup>
import * as bgApi from '@/api/system/reportManagement'
import * as bcApi from '@/api/system/attendance_bc'

import { Icon } from '@/components/Icon'
import { Back } from '@element-plus/icons-vue'
import download from '@/utils/download'
import OrgPicker from '@/components/common/OrgPicker.vue'

defineOptions({ name: 'SystemRoleAssignMenuForm' })
// 新的
import type { UploadInstance, UploadProps, UploadRawFile, ElMessageBox } from 'element-plus'
import dayjs from 'dayjs'

import { el, fa, tr } from 'element-plus/es/locale'
import { log } from 'console'
import { iteratee, kebabCase } from 'lodash-es'
const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗
const loading = ref(false) // 列表的加载中
const total = ref(0) // 列表的总页数
const Bglist = ref([])
const selectType = ref('0') //类型
const timeValue = ref([]) //时间范围
const groupList = ref([]) //考勤组下拉
const groupIds = ref([]) //考勤组所选
const targetUser = ref([]) //部门人员下拉
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  fromDate: '',
  toDate: '',
  groups: [],
  departments: [],
  users: []
})
// 获取表格数据
const listData = async () => {
  loading.value = true
  try {
    const data = await bgApi.attendanceRecord(queryParams)
    console.log(data)
    Bglist.value = data.records
    total.value = data.total
  } finally {
    loading.value = false
  }
}
// 导出报表
const exportGroup = async () => {
  loading.value = true
  try {
    const data = await bgApi.exportAttendanceRecord(queryParams)
    download.excel(data, '报表数据.xls')
  } finally {
    loading.value = false
  }
}

// 获取近十五天
function getRecent15Days() {
  const today = new Date()
  const recent15Days = []
  for (let i = 0; i < 15; i++) {
    const date = new Date(today)
    date.setDate(today.getDate() - i)
    const formattedDate = date.toISOString().split('T')[0]
    recent15Days.unshift(formattedDate)
  }
  return recent15Days
}
// 日期范围选择
const timeChange = (e) => {
  queryParams.fromDate = e[0]
  queryParams.toDate = e[1]
  queryParams.pageNo = 1
  queryParams.pageSize = 10
  listData()
}
// 考勤组change事件
const isChange = ref(false)
const groupChange = (e) => {
  console.log(e)
  if (e) {
    isChange.value = true
  } else {
    isChange.value = false
  }
  // if(e.length<=0){
  //   queryParams.groups = []
  //   handleSearch()

  // }
}
// 考勤组筛选的数据点击x号触发
const removeTag = (e) => {
  console.log(e, '哈哈哈')
  queryParams.groups = queryParams.groups.filter((item) => item.groupId != e)
  handleSearch()
}
// 部门人员筛选的数据点击x号触发
const closeTag = (it) => {
  console.log(it, '哈哈哈item')

  // queryParams.departments.push({
  //       deptId: item.id,
  //       name: item.id
  //     })
  // queryParams.users.push({
  //       userId: item.id,
  //       name: item.id
  //     })
  targetUser.value.map((item, i) => {
    console.log(item, 'caca')

    if (it.type == 'dept') {
      if (item.id == it.id) {
        targetUser.value.splice(i, 1)
      }
    } else if (it.type == 'user') {
      if (item.id == it.id) {
        targetUser.value.splice(i, 1)
      }
    }
  })

  if (it.type == 'dept') {
    queryParams.departments = queryParams.departments.filter((item) => item.deptId != it.id)
  } else if (it.type == 'user') {
    queryParams.users = queryParams.users.filter((item) => item.userId != it.id)
  }
  handleSearch()
  nextTick(() => {
    inputHeight.value = tagItemRef.value.getBoundingClientRect().height + 5
  })
}

// 考勤组下拉框隐藏时
const groupVisible = (e) => {
  if (isChange && !e) {
    console.log(groupIds.value)
    queryParams.groups = groupIds.value
      .filter((item1) => groupList.value.some((item2) => item1 == item2.id))
      .map((item1) => {
        const item2 = groupList.value.find((item2) => item1 == item2.id)
        return {
          groupId: item2.id,
          name: item2.name
        }
      })
    handleSearch()
  }
}
// 点击选择部门
const orgPicker = ref()
const inputFocus = (e) => {
  console.log(11111111111111111111111111)
  console.log(e)

  if (e.target.tagName.toLowerCase() === 'input') {
    return
  }
  orgPicker.value.show()
}
// 类型change
const isFirst = ref(true)
const selectChange = (e) => {
  groupIds.value = []
  targetUser.value = []
  queryParams.groups = []
  queryParams.departments = []
  queryParams.users = []
  if (e == 0) {
    handleSearch()
  }
  if (e == 1 && isFirst) {
    isFirst.value = false
    getGroupList()
  }
  inputHeight.value = 33.38
}
// 搜索
const handleSearch = () => {
  queryParams.pageNo = 1
  queryParams.pageSize = 10
  // const dates = getRecent15Days()
  // timeValue.value = [dates[0], dates[dates.length - 1]]
  // queryParams.fromDate = dates[0]
  // queryParams.toDate = dates[dates.length - 1]
  listData()
}
// 考勤组下拉
const getGroupList = async () => {
  try {
    const res = await bcApi.groupList({
      pageNo: 1,
      pageSize: 100
    })
    groupList.value = res.list
  } finally {
    loading.value = false
  }
}
// 接收部门信息
const dynamicWidth = ref(240)
const inputHeight = ref(33.38)
const tagItemRef = ref()
const selected = (users) => {
  targetUser.value = []
  queryParams.departments = []
  queryParams.users = []
  users.forEach((item) => {
    targetUser.value.push({
      id: item.id,
      name: item.name,
      type: item.type
    })
    if (item.type == 'dept') {
      queryParams.departments.push({
        deptId: item.id,
        name: item.id
      })
    }
    if (item.type == 'user') {
      queryParams.users.push({
        userId: item.id,
        name: item.id
      })
    }
  })

  console.log(targetUser, 'targetUsertargetUser')

  nextTick(() => {
    console.log(tagItemRef.value.getBoundingClientRect().width)
    console.log(tagItemRef.value.getBoundingClientRect().height, 'dasda')
    if (tagItemRef.value.getBoundingClientRect().width > 230) {
      console.log(tagItemRef.value.getBoundingClientRect().height, 'dasda')
      dynamicWidth.value = tagItemRef.value.getBoundingClientRect().width + 20
    } else {
      dynamicWidth.value = 240
    }
    inputHeight.value = tagItemRef.value.getBoundingClientRect().height + 5
  })
  handleSearch()
}
/** 初始化 **/
onMounted(() => {
  // handleSearch()
  const dates = getRecent15Days()
  timeValue.value = [dates[0], dates[dates.length - 1]]
  queryParams.fromDate = dates[0]
  queryParams.toDate = dates[dates.length - 1]
  listData()
  // getbcList()
})
</script>
<style lang="scss" scoped>
.deptClass {
  position: relative;

  .el-input {
    pointer-events: none;
  }

  .tagItem {
    position: absolute;
    left: 10px;
    top: 0;
    // white-space: nowrap;

    .el-tag {
      margin-right: 5px;
    }
  }

  .isAddBox {
    position: relative;
    cursor: pointer;
  }
}
</style>
