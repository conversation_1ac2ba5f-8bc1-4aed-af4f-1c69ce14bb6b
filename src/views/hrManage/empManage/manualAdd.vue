<template>
  <!-- 确认到岗弹窗 -->
  <el-form ref="formRef" :model="formData" :rules="formRules" label-width="150px" v-loading="formLoading">
    <el-form-item label="姓名" prop="name">
      <el-input v-model="formData.name" placeholder="请输入员工姓名" />
    </el-form-item>
    <el-form-item label="手机号码" prop="mobile">
      <el-input v-model="formData.mobile" placeholder="请输入员工手机号码" />
    </el-form-item>
    <el-form-item label="预计入职日期" prop="preEntryTime">
      <el-date-picker v-model="formData.preEntryTime" type="date" placeholder="请选择日期" value-format="YYYY-MM-DD" />
    </el-form-item>
    <el-form-item label="部门" class="deptItem" prop="deptId">
      <div @click="handleDept" class="deptClass">
        <el-input readonly :placeholder="targetUser.length > 0 ? '' : '请选择部门'" v-model="formData.deptId" />
        <org-picker title="请选择" ref="orgPicker" type="dept" :selected="targetUser" @ok="selected" />
        <div v-if="targetUser.length > 0" class="tagItem">
          <el-tag type="info" v-for="(item, index) in targetUser" :key="index">{{ item.name }}</el-tag>
        </div>
      </div>
    </el-form-item>
    <el-form-item label="职位" prop="postIds">
      <el-select v-model="formData.postIds" placeholder="请选择" multiple>
        <el-option v-for="item in postList" :key="item.id" :label="item.name" :value="item.id" />
      </el-select>
    </el-form-item>
    <el-form-item label="">
      <div class="footerBox">
        <el-button @click="submitForm" type="primary" :disabled="formLoading">保存</el-button>
        <el-button @click="resetForm">重置</el-button>
      </div>
    </el-form-item>
  </el-form>
</template>
<script setup lang="ts">
import { EntryApi } from '@/api/hrManage/empManage'
import OrgPicker from '@/components/common/OrgPicker.vue'
import * as PostApi from '@/api/system/post'
import { any } from 'vue-types';
const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗是否展示
const formLoading = ref(false) // 加载中
const formData = ref<{
  name: any,
  mobile: any,
  preEntryTime: any,
  deptId: any,
  postIds: any,
}>({
  name: '',
  mobile: '',
  preEntryTime: '',
  deptId: [],
  postIds: [],
})
const formRules = reactive({
  name: [{ required: true, message: '请输入', trigger: 'blur' }],
  mobile: [{ required: true, message: '请输入', trigger: 'blur' }],
  preEntryTime: [{ required: true, message: '请选择', trigger: 'change' }],
  deptId: [{ required: true, message: '请选择', trigger: 'blur' }],
  postIds: [{ required: true, message: '请选择', trigger: 'change' }],
})
const formRef = ref() // 表单 Ref
const postList = ref([] as PostApi.PostVO[]) // 岗位列表
// 打开
const open = async (e) => {
  resetForm()
  // targetUser.value = []
  // formData.value.preEntryTime = getCurrentDate()
  postList.value = await PostApi.getSimplePostList()
}
const getCurrentDate = () => {
  const today = new Date()
  const year = today.getFullYear()
  const month = String(today.getMonth() + 1).padStart(2, '0')
  const day = String(today.getDate()).padStart(2, '0')
  return `${year}-${month}-${day}`;
}
// 点击选择部门
const orgPicker = ref()
const handleDept = (e) => {
  console.log(e.target.tagName.toLowerCase(), 'e.target.tagName.toLowerCase()')
  // 阻止穿透修改无效 用判断return掉
  if (e.target.tagName.toLowerCase() === 'input') {
    return
  }
  orgPicker.value.show()
}
// 接收部门信息
let targetUser = ref<any[]>([])
const myInput =ref()
const selected = (users: any) => {
  console.log(users,'users')
  targetUser.value = []
  users.forEach((item) => {
    targetUser.value.push({
      id: item.id,
      name: item.name,
      type:item.type
    })
    formData.value.deptId.push(item.id)
  })
  // myInput.value.validate()
}
// 提交
const submitForm = async () => {
  await formRef.value.validate()
  formLoading.value = true
  try {
    const data = formData.value
    let newDeptId = formData.value.deptId.map(Number)
    let newPostId = formData.value.postIds.map(Number)
    console.log(data)
    const obj = {
      name: formData.value.name,
      mobile: formData.value.mobile,
      preEntryTime: formData.value.preEntryTime,
      deptId: newDeptId.join(','),
      postIds: newPostId.join(','),
      entryTable: 0,
      opSourceType: 2,
      realAuthStatus: 1
    }
    // return
    await EntryApi.createEntry(obj)
    message.success('操作成功')
    dialogVisible.value = false
    emit('success', 'success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    name: '',
    mobile: '',
    preEntryTime: '',
    deptId: [],
    postIds: [],
  }
  formRef.value?.resetFields()
  targetUser.value = []
  formData.value.preEntryTime = getCurrentDate()
}
defineExpose({
  open
})
const emit = defineEmits(['success']) 
</script>
<style lang="less" scoped>
.el-input,
.el-select,
:deep(.el-date-editor) {
  width: 80%;
}

.el-form {
  margin-top: 20px;
}

.el-form-item {
  margin-bottom: 30px;
}

.deptClass {
  width: 80%;
  position: relative;

  .tagItem {
    position: absolute;
    left: 10px;
    top: 0;

    .el-tag {
      margin-right: 5px;
    }
  }

  .el-input {
    width: 100%;
    pointer-events: none;
  }
}

.deptItem :deep(.el-input__inner) {
  color: transparent;
}

.footerBox {
  width: 100%;

  // .el-button {
  //   width:180px
  // }
}
:deep(.el-select__selection.is-near){
  margin-left: 0;
}
</style>
