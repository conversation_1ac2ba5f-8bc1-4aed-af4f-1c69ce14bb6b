<template>
  <!-- 手机模型img里预览 动态表单 -->
  <div class="processBox">
    <div class="layer"></div>
    <div v-for="(item, index) in listData" :key="index" class="boxFor">
      <p class="titleP">{{ item.groupName }}</p>
      <div>
        <form-render mode="MOBILE" class="process-form" :ref="'formRef' + index" :forms="getForms(item.customFieldList)"
          v-model="item.formData" />
      </div>
    </div>
  </div>
</template>

<script>
import { UserRosterApi } from '@/api/hrManage/userRoster'
import FormRender from "@/views/wflow/common/form/FormRender.vue";
export default {
  components: {
    FormRender,
  },
  data() {
    return {
      listData: [],
    }
  },
  created() {
    this.getList()
  },
  methods: {
    getForms(e) {
      const newArr = [];
      console.log(e)
      e.forEach((item) => {
        newArr.push({
          id: item.id,
          name: item.fieldType,
          title: item.fieldName,
          props: {
            required: item.required == 0 ? true : false,
            placeholder: item.hint,
            options: this.getOptions(item.props),
          },
        });
      });
      return newArr
    },
    getOptions(e) {
      const newArr = [];
      if (e) {
        let newE = JSON.parse(e)
        newE.options.forEach((item) => {
          newArr.push(item.label)
        })
      }
      return newArr
    },
    getList() {
      UserRosterApi.getCustomForm({ tableName: '员工档案字段设置' }).then((res) => {
        console.log(res)
        this.listData = res.data.customGroupList.filter(item => {
          item.customFieldList = item.customFieldList.filter(child => child.visibleByEmp == 0)
          return item.customFieldList.length > 0
        }).map((item, index) => {
          return {
            ...item,
            formData: {
              // createTime: 0,
            }
          }
        })
      })
    },
  }
}
</script>

<style lang="less" scoped>
.processBox {
  position: relative;
  padding-top: 60px;
}

.layer {
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  top: 0;
  z-index: 12;
}

.titleP {
  margin: 0;
  padding: 0 0.625rem 0.625rem 1rem;
  color: #545456;
  font-size: 10px;
}
</style>