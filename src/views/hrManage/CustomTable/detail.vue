<template>
  <div>
    <el-card class="oaCard" v-loading="loading">
      <el-page-header :icon="ArrowLeft" @back="closeChild" class="custom-page-header" v-if="!isMenu">
        <template #content>
          <span class="text-large font-600 mr-3"> {{ tableName }} </span>
        </template>
      </el-page-header>
      <div class="addFormDiv">
        <el-button @click="openGroupForm('create', '')" v-hasPermi="['employee-editing:add']">新建分组</el-button>
      </div>
      <div v-for="(item, index) in tableData" :key="index" class="forBox">
        <div class="isTitle">
          <span class="beforeStyle">{{ item.groupName }}</span>
          <el-dropdown trigger="click" class="dropClass" @command="(command) => handleCommand(command, item)"
            v-hasPermi="['employee-editing:setting']">
            <span class="el-dropdown-link">
              设置<el-icon>
                <ArrowDown />
              </el-icon>
            </span>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item v-if="item.supportEditName == 0" command="edit">编辑分组</el-dropdown-item>
                <el-dropdown-item command="add">新增字段</el-dropdown-item>
                <el-dropdown-item command="delete">删除分组</el-dropdown-item>
                <!-- <el-dropdown-item>字段调整顺序</el-dropdown-item> -->
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
        <el-table :data="item.customFieldList">
          <el-table-column label="字段名称" prop="fieldName" min-width="130" />
          <el-table-column label="字段类型名称" prop="typeName" />
          <el-table-column label="是否必填">
            <template #default="scope">
              <el-icon v-if="scope.row.required == 0">
                <Check />
              </el-icon>
            </template>
          </el-table-column>
          <el-table-column label="在入职登记表中是否显示">
            <template #default="scope">
              <el-icon v-if="scope.row.visibleByEmp == 0">
                <Check />
              </el-icon>
            </template>
          </el-table-column>
          <el-table-column label="在个人档案中是否可见">
            <template #default="scope">
              <el-icon v-if="scope.row.hiddenFromEmployeeFlag == 0">
                <Check />
              </el-icon>
            </template>
          </el-table-column>
          <el-table-column label="在个人档案中是否可以修改">
            <template #default="scope">
              <el-icon v-if="scope.row.editFromEmployeeFlag == 0">
                <Check />
              </el-icon>
            </template>
          </el-table-column>
          <el-table-column label="创建时间" prop="createTime" :formatter="dateFormatter" width="180px" />
          <el-table-column label="操作">
            <template #default="scope">
              <el-button link type="primary" @click="openFieldForm('update', scope.row)"
                v-hasPermi="['employee-editing:editFiled']">
                编辑
              </el-button>
              <!-- <el-button link type="primary"  v-if="scope.row.stopFlag==0">
                停用
              </el-button>
              <el-button link type="danger" v-if="scope.row.delFlag==0">
                删除
              </el-button> -->
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-card>
    <!-- 表单弹窗：添加/修改 -->
    <addGroupForm ref="groupRed" @success="getList" />
    <addFieldForm ref="fieldRef" @success="getList" />
  </div>
</template>
<script setup type="ts">
import { dateFormatter } from '@/utils/formatTime'
import { CustomTableApi } from '@/api/hrManage/customTable'
import { Check, ArrowDown } from '@element-plus/icons-vue'
import addGroupForm from './dialog/addGroup.vue'
import addFieldForm from './dialog/addField.vue'

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗
const loading = ref(false)
const tableData = ref([])//列表数据
const tableName = ref('')//表单名称
const tableId = ref()//表单id
const isMenu = ref(true)//区分来源
// 获取列表 
const open = (id, name) => {
  loading.value = true
  tableData.value = []
  tableName.value = name ? name : '员工档案字段设置'
  if (name) {
    isMenu.value = false
  } else {
    isMenu.value = true
  }
  // tableId.value = id
  getList()
}
const getList = async () => {
  try {
    const res = await CustomTableApi.getDetail(tableName.value)
    tableData.value = res.customGroupList
    tableId.value = res.id
  } finally {
    loading.value = false
  }
}
// 关闭
const closeChild = () => {
  emit('close')
}
// 添加/修改 分组
const groupRed = ref()
const openGroupForm = (type, id) => {
  groupRed.value.open(type, id, tableId.value)
}
// 添加/修改操作 字段
const fieldRef = ref()
const openFieldForm = (type, row) => {
  fieldRef.value.open(type, row, tableId.value)
}
//更多-操作按钮
const handleCommand = (command, row) => {
  switch (command) {
    case 'edit':
      openGroupForm('update', row.id)
      break
    case 'add':
      openFieldForm('create', row)
      break
    case 'delete':
      handleDeleteGroup(row.id)
      break
    default:
      break
  }
}
// 删除分组
const handleDeleteGroup = async (id) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await CustomTableApi.deleteCustomGroup(id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    getList()
  } catch { }
}

const emit = defineEmits(["close"])
defineExpose({
  open
})
</script>
<style lang="less" scoped>
.forBox {
  margin-bottom: 36px;
}

.isTitle {
  position: relative;
}

.dropClass {
  cursor: pointer;
  position: absolute;
  right: 0;
  top: 0px;
  font-size: 16px;
  color: #3370FF;

  .el-icon {
    font-size: 14px;
    margin-left: 2px;
  }
}

.addFormDiv {
  text-align: right;
  margin-bottom: 15px;
}

.el-table {
  margin-top: 2px;
}
</style>