<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible">
    <el-form ref="formRef" :model="formData" :rules="formRules" label-width="130px" v-loading="formLoading">
      <el-form-item label="分组名称" prop="groupName">
        <el-input v-model="formData.groupName" placeholder="请输入分组名称" />
      </el-form-item>
      <el-form-item label="排序" prop="sort">
        <el-input v-model="formData.sort" placeholder="请输入排序" />
      </el-form-item>
      <el-form-item label="是否支持编辑名称" prop="supportEditName">
        <!-- <el-input v-model="formData.supportEditName" placeholder="请选择是否支持编辑名称" /> -->
        <el-select v-model="formData.supportEditName" placeholder="请选择是否支持编辑名称" >
          <el-option label="是" :value="0" />
          <el-option label="否" :value="1" />
        </el-select>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
import { CustomTableApi } from '@/api/hrManage/customTable'

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const formData = ref({
  id: undefined,
  tableId: undefined,
  groupName: undefined,
  sort: undefined,
  supportEditName: 0,
})
const formRules = reactive({
  groupName: [{ required: true, message: '请输入分组名称', trigger: 'blur' }],
  tableComment: [{ required: true, message: '表描述不能为空', trigger: 'blur' }],
})
const formRef = ref() // 表单 Ref

/** 打开弹窗 */
const open = async (type: string, id: any, tableId: any) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()
  formData.value.tableId = tableId
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      formData.value = await CustomTableApi.getCustomGroup(id)
    } finally {
      formLoading.value = false
    }
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value
    if (formType.value === 'create') {
      await CustomTableApi.createCustomGroup(data)
      message.success(t('common.createSuccess'))
    } else {
      await CustomTableApi.updateCustomGroup(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    tableId: undefined,
    groupName: undefined,
    sort: undefined,
    supportEditName: 0,
  }
  formRef.value?.resetFields()
}
</script>