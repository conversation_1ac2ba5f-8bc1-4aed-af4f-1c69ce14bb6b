<template>
  <el-drawer :title="dialogTitle" v-model="dialogVisible" class="custom-detail-header" size="34%">
    <el-form ref="formRef" :model="formData" :rules="formRules" label-width="150px" v-loading="formLoading">
      <el-form-item label="字段编码" prop="fieldCode">
        <el-input v-model="formData.fieldCode" placeholder="请输入字段编码" :disabled="formType == 'update'" />
      </el-form-item>
      <el-form-item label="字段名称" prop="fieldName">
        <el-input v-model="formData.fieldName" placeholder="请输入字段名称" />
      </el-form-item>
      <el-form-item label="字段类型" prop="typeName">
        <el-select v-model="formData.typeName" placeholder="请选择字段类型" @change="typeNameChange">
          <el-option :label="item.label" :value="item.label" v-for="(item, index) in typeList" :key="index" />
        </el-select>
      </el-form-item>
      <el-form-item label="选项内容" class="optionItem" v-if="formData.typeName == '选项'">
        <el-button icon="el-icon-plus" link size="small" @click="options.push('')" class="addButton">新增选项</el-button>
        <div class="optionDiv">
          <div v-for="(item, index) in options" :key="index" class="addOptions">
            <el-input v-model="options[index]" placeholder="请设置选项值"> </el-input>
            <el-button icon="el-icon-delete" type="danger" @click="options.splice(index, 1)"></el-button>
          </div>
        </div>
      </el-form-item>
      <el-form-item label="选项内容" class="optionItem" v-if="formData.typeName == '职位'">
        <div class="optionDiv">
          <div v-for="(item, index) in postList" :key="index" class="addOptions">
            <el-input v-model="item.name" disabled> </el-input>
          </div>
        </div>
      </el-form-item>
      <!-- <el-form-item label="字段类型名称" prop="fieldType">
        <el-input v-model="formData.fieldType" placeholder="请输入字段类型名称" />
      </el-form-item> -->
      <el-form-item label="提示文字" prop="hint">
        <el-input v-model="formData.hint" placeholder="请输入字段备注，以便员工理解如何录入" />
      </el-form-item>
      <el-form-item label="是否必填" prop="required">
        <!-- <el-input v-model="formData.required" placeholder="请输入是否必填" /> -->
        <el-radio-group v-model="formData.required">
          <el-radio :label="0">必填</el-radio>
          <el-radio :label="1">选填</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="在入职登记表中" prop="visibleByEmp">
        <!-- <el-input v-model="formData.visibleByEmp" placeholder="请输入在入职登记表中是否显示" /> -->
        <el-radio-group v-model="formData.visibleByEmp" :disabled="mobileDisabled">
          <el-radio :label="0">显示</el-radio>
          <el-radio :label="1">隐藏</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="在个人档案中" prop="hiddenFromEmployeeFlag" class="optionItem">
        <!-- <el-input v-model="formData.hiddenFromEmployeeFlag" placeholder="请输入在个人档案中是否可见" /> -->
        <el-radio-group v-model="formData.hiddenFromEmployeeFlag">
          <el-radio :label="0">对员工可见</el-radio>
          <el-radio :label="1">对员工不可见</el-radio>
        </el-radio-group>
        <!-- <el-input v-model="formData.editFromEmployeeFlag" placeholder="请输入在个人档案中是否可以修改" /> -->
        <el-radio-group v-model="formData.editFromEmployeeFlag">
          <el-radio :label="0">员工可修改</el-radio>
          <el-radio :label="1">员工不可修改</el-radio>
        </el-radio-group>
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="footerBox">
        <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
        <el-button @click="dialogVisible = false">取 消</el-button>
      </div>
    </template>
  </el-drawer>
</template>
<script setup lang="ts">
import { CustomTableApi } from '@/api/hrManage/customTable'
import { any } from 'vue-types';
import * as PostApi from '@/api/system/post'
const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const formData = ref<{
  id: any,
  tableId: any,
  groupId: any,
  fieldCode: any,
  fieldName: any,
  typeName: any,
  fieldType: any,
  hint: any,
  required: any,
  visibleByEmp: any,
  hiddenFromEmployeeFlag: any,
  editFromEmployeeFlag: any,
  props: any
}>({
  id: undefined,
  tableId: undefined,
  groupId: undefined,
  fieldCode: '',
  fieldName: '',
  typeName: '',
  fieldType: '',
  hint: '请输入',
  required: 1,
  visibleByEmp: 1,
  hiddenFromEmployeeFlag: 0,
  editFromEmployeeFlag: 0,
  props: ''
})
// 字段类型下拉
const typeList = reactive([
  { label: "文本", value: "TextInput" },
  { label: "选项", value: "SelectInput" },
  { label: "时间", value: "Date" },
  { label: "附件", value: "FileUpload" },
  { label: "图片", value: "ImageUpload" },
  { label: "部门", value: "DeptPicker" },
  { label: "职位", value: "jobMultiple" },
])
const options = ref<any[]>([])//选项的下拉框数组
const postList = ref([] as PostApi.PostVO[]) // 岗位列表
const mobileDisabled = ref(false)//手机端禁用展示该字段
const formRules = reactive({
  fieldCode: [{ required: true, message: '请输入字段编码', trigger: 'blur' }],
  fieldName: [{ required: true, message: '请输入字段名称', trigger: 'blur' }],
  typeName: [{ required: true, message: '请选择字段类型', trigger: 'change' }],
})
const formRef = ref() // 表单 Ref

/** 打开弹窗 */
const open = async (type: string, row) => {
  dialogVisible.value = true
  dialogTitle.value = type == 'create' ? '新增字段' : '编辑字段'
  formType.value = type
  resetForm()
  options.value = []
  formData.value.tableId = row.tableId
  formData.value.groupId = row.id
  formData.value.typeName = typeList[0].label
  formData.value.fieldType = typeList[0].value
  mobileDisabled.value = false
  postList.value = await PostApi.getSimplePostList()
  // 修改时，设置数据
  if (type == 'update') {
    formLoading.value = true
    try {
      formData.value = await CustomTableApi.getCustomField(row.id)
      if (formData.value.props) {
        let apiProps = JSON.parse(formData.value.props)
        apiProps.options.forEach((item) => {
          options.value.push(item.label)//选项的下拉框
        })
      }
      if (formData.value.typeName == '部门' || formData.value.typeName == '职位') {
        mobileDisabled.value = true
      }
    } finally {
      formLoading.value = false
    }
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  if (formData.value.typeName == '选项') {
    if (options.value.length == 0) {
      return message.warning('请添加选项内容')
    }
    const myArr: any[] = []
    const mySet = new Set(options.value)
    for (const item of mySet) {
      myArr.push(item)
    }
    const hasEmptyValue = options.value.some(value => value == null || value == undefined || value == '')
    if (hasEmptyValue) {
      return message.warning('请添加选项')
    }
    if (options.value.length != myArr.length) {
      return message.warning('请添加不同的选项')
    }
    const subArr: any[] = []
    options.value.forEach((item) => {
      subArr.push({
        label: item,
        value: item
      })
    })
    const subJson = {
      options: subArr
    }
    formData.value.props = JSON.stringify(subJson)
  }
  // 职位下拉接口获取 该处formData.value.props不传值
  console.log(formData.value, 'formData.value')
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value
    if (formType.value === 'create') {
      await CustomTableApi.createCustomField(data)
      message.success(t('common.createSuccess'))
    } else {
      await CustomTableApi.updateCustomField(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    tableId: undefined,
    groupId: undefined,
    fieldCode: '',
    fieldName: '',
    typeName: '',
    fieldType: '',
    hint: '请输入',
    required: 1,
    visibleByEmp: 1,
    hiddenFromEmployeeFlag: 0,
    editFromEmployeeFlag: 0,
    props: ''
  }
  formRef.value?.resetFields()
}
// 字段类型下拉
const typeNameChange = (e) => {
  console.log(e)
  if (e == '文本') {
    formData.value.hint = '请输入'
  }
  if (e != '文本') {
    formData.value.hint = '请选择'
  }
  if (e == '部门' || e == '职位') {//仅做pc数据解析
    mobileDisabled.value = true
    formData.value.visibleByEmp = 1
  } else {
    mobileDisabled.value = false
  }
  typeList.forEach((item) => {
    if (item.label == e) {
      formData.value.fieldType = item.value
    }
  })
}
</script>
<style lang="less" scoped>
.el-input,
.el-select {
  width: 80%;
}

.optionDiv {
  margin-top: 6px;
  width: 80%;
}

.optionItem {
  :deep(.el-form-item__content) {
    display: inline;
  }
}

.addButton {
  font-size: 14px;
}

.addOptions {
  margin-bottom: 10px;
  display: flex;

  .el-input {
    flex: 1;
  }

  :deep(.el-button--danger) {
    border-left: 0;
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
    box-shadow: 0 1px 0 0 #dcdfe6 inset, 0 -1px 0 0 #dcdfe6 inset, -1px 0 0 0 #dcdfe6 inset;
    background: #f5f7fa;
    border: #f5f7fa;
    color: #909399;

  }

  :deep(.el-input__wrapper) {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
  }
}

.el-radio-group {
  display: block;

  :deep(.el-radio__input.is-checked+.el-radio__label) {
    color: #606266;
  }
}

.el-form {
  margin-top: 20px;
}

.el-form-item {
  margin-bottom: 30px;
}

.footerBox {
  text-align: center !important;
  padding: 20px 20px 0;
  border-top: 1px solid #eee !important;
}
</style>
