<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="150px"
      v-loading="formLoading"
    >
      <el-form-item label="套餐名" prop="name">
        <el-input v-model="formData.name" placeholder="请输入套餐名" />
      </el-form-item>
      <el-form-item label="支付金额(元)" prop="payPrice">
        <el-input-number v-model="formData.payPrice" :min="0" :precision="2" :step="0.01" />
      </el-form-item>
      <el-form-item label="赠送金额(元)" prop="bonusPrice">
        <el-input-number v-model="formData.bonusPrice" :min="0" :precision="2" :step="0.01" />
      </el-form-item>
      <el-form-item label="开启状态" prop="status">
        <el-radio-group v-model="formData.status">
          <el-radio
            v-for="dict in getIntDictOptions(DICT_TYPE.COMMON_STATUS)"
            :key="dict.value"
            :label="dict.value"
          >
            {{ dict.label }}
          </el-radio>
        </el-radio-group>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
import * as WalletRechargePackageApi from '@/api/pay/wallet/rechargePackage'
import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'
import { fenToYuan, yuanToFen } from '@/utils'
const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const formData = ref({
  id: undefined,
  name: undefined,
  payPrice: undefined,
  bonusPrice: undefined,
  status: undefined
})
const formRules = reactive({
  name: [{ required: true, message: '套餐名不能为空', trigger: 'blur' }],
  payPrice: [{ required: true, message: '支付金额不能为空', trigger: 'blur' }],
  bonusPrice: [{ required: true, message: '赠送金额不能为空', trigger: 'blur' }],
  status: [{ required: true, message: '状态不能为空', trigger: 'blur' }]
})
const formRef = ref() // 表单 Ref

/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      formData.value = await WalletRechargePackageApi.getWalletRechargePackage(id)
      formData.value.payPrice = fenToYuan(formData.value.payPrice)
      formData.value.bonusPrice = fenToYuan(formData.value.bonusPrice)
    } finally {
      formLoading.value = false
    }
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  if (!formRef) return
  const valid = await formRef.value.validate()
  if (!valid) return
  // 提交请求
  formLoading.value = true
  try {
    const data = { ...formData.value }
    data.payPrice = yuanToFen(data.payPrice)
    data.bonusPrice = yuanToFen(data.bonusPrice)
    if (formType.value === 'create') {
      await WalletRechargePackageApi.createWalletRechargePackage(data)
      message.success(t('common.createSuccess'))
    } else {
      await WalletRechargePackageApi.updateWalletRechargePackage(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    name: undefined,
    payPrice: undefined,
    bonusPrice: undefined,
    status: undefined
  }
  formRef.value?.resetFields()
}
</script>
