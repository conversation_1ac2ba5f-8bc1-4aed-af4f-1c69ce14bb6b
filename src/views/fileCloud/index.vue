<template>
  <div>
    <el-row class="rowBox relative" :class="{ isCol: isCollapse, 'isCBox-else': windowWidth < 768 }"
      v-loading="loading">
      <el-col :span="4" :xl="4" :lg="6" :md="6" :sm="6" :xs="8" class="left-col">
        <p class="title">云盘</p>
        <tenantName @success="resetPage"></tenantName>
        <!-- <el-input placeholder="搜索文件" clearable :prefix-icon="Search"></el-input> -->
        <ul class="leftUl">
          <li v-for="(item, index) in leftList" :key="index" :class="{ 'activeLi': activeLi == index }"
            @click="changeLi(item, index)">
            <img :src="item.url" />{{ item.name }}
          </li>
        </ul>
      </el-col>
      <el-col :span="20" :xl="20" :lg="18" :md="18" :sm="18" :xs="16" class="rightCol">
        <!-- top1 -->
        <div class="rightTop">
          <p>{{ activeName }}</p>
          <div>
            <el-button :icon="Plus" @click="handleAdd('create')" v-if="queryParams.id == 0">新建文件夹</el-button>
            <el-button type="primary" :icon="Upload" @click="handleUpload"
              v-if="queryParams.id != 0 && (permissionNum == 1 || permissionNum == 2)">上传</el-button>
          </div>
        </div>
        <!-- top2 -->
        <div class="crumbDiv">
          <el-breadcrumb :separator-icon="ArrowRight" class="crumb-left" :class="{ 'onlyOne': crumbsList.length == 1 }">
            <el-breadcrumb-item v-for="(item, index) in crumbsList" :key="index">
              <img src="@/assets/image/fileCloud6-1.svg" v-if="index != 0 && item.parentId == 0" />
              <img src="@/assets/image/fileCloud7-1.svg" v-if="index != 0 && item.parentId != 0" />
              <span :title="item.name" @click="breadcrumbClick(item, index)">
                {{ item.name }}
              </span>
            </el-breadcrumb-item>
          </el-breadcrumb>
          <div class="flex">
            <div class="crumb-right" v-if="queryParams.id != 0">
              <div v-for="(item, index) in permissionUser" :key="index" class="user" v-show="index < 2"
                :class="{ 'secondPosition': index == '1' }" @click="handlePermission()">
                <p v-if="permissionUser.length > 2 && index == '1'" class="absoluteP">{{ permissionUser.length }}</p>
                <avatar :name="item.userName" :src="''" :size="24" :shape="circle" :title="item.userName"></avatar>
              </div>
              <div class="add" @click="handlePermission()" v-if="permissionNum == 1">+</div>
            </div>
            <!-- <div class="layout">
              <img v-for="(item, index) in layoutList" :key="index" :src="item.url"
                :class="{ 'activeLayout': index == layoutActive }" @click.stop="handleLayout(index)" />
            </div> -->
          </div>
        </div>
        <!-- content -->
        <div>
          <div>
            <el-row class="tableTop tableFlex" type="flex">
              <p>名称</p>
              <p>创建者</p>
              <p>更新时间</p>
              <p>大小</p>
              <p></p>
            </el-row>
            <div class="tableBody">
              <p v-if="tableData.length == 0" class="alignP">暂无数据</p>
              <div v-for="(item, index) in tableData" :key="index" class="tableFlex tableBottom"
                @click="handleDetail(item)">
                <p>
                  <img src="@/assets/image/fileCloud6-1.svg" v-if="queryParams.id == 0" />
                  <img src="@/assets/image/fileCloud7-1.svg" v-if="queryParams.id != 0 && item.type == 1" />
                  {{ item.name }}
                </p>
                <p>{{ item.creator }}</p>
                <p>{{ formatDate(item.updateTime) }}</p>
                <p>{{ item.size ? item.size : '--' }}</p>
                <p class="configP" @click.stop>
                  <el-dropdown @command="(command) => handleCommand(command, item)" trigger="contextmenu"
                    :ref="setItemRef(index)">
                    <img :src="item.showSecondImage ? configImg1 : configImg2" @mouseover="item.showSecondImage = true"
                      @mouseleave="item.showSecondImage = false" @click.stop="handleClick(item, index)" />
                    <template #dropdown>
                      <el-dropdown-menu>
                        <el-dropdown-item command="permission" :icon="Lock"
                          v-if="item.permission == 1">权限管理</el-dropdown-item>
                        <el-dropdown-item command="rename" :icon="Edit"
                          v-if="item.permission == 1 || item.permission == 2">重命名</el-dropdown-item>
                        <el-dropdown-item command="delete" :icon="Delete"
                          v-if="item.permission == 1">删除</el-dropdown-item>
                      </el-dropdown-menu>
                    </template>
                  </el-dropdown>
                </p>
              </div>
            </div>
          </div>
        </div>
      </el-col>
    </el-row>
  </div>
  <!-- 新建/修改文件夹名称 -->
  <addFolder ref="addFolderRef" @success="folderSuccess"></addFolder>
  <!-- 权限配置 -->
  <permissionConfig ref="permissionRef"></permissionConfig>
  <upload ref="uploadRef" @success="uploadSuccess"></upload>
  <!-- 预览附件 -->
  <readFile ref="readFileRef" />
</template>
<script type="ts" setup>
defineOptions({ name: 'fileCloud' })
import { ElMessage } from 'element-plus'
import { Search, Plus, Upload, ArrowRight, Lock, Edit, Delete } from '@element-plus/icons-vue'
import { fileCloudApi } from '@/api/fileCloud/list'
import { useAppStore } from '@/store/modules/app'
import fileCloud1 from '@/assets/image/fileCloud1.png'
import fileCloud2 from '@/assets/image/fileCloud2.png'
import fileCloud3 from '@/assets/image/fileCloud3.png'
import fileCloud4 from '@/assets/image/fileCloud4.png'
import fileCloud5 from '@/assets/image/fileCloud5.png'
import layout1 from '@/assets/image/fileCloudLayout1.png'
import layout2 from '@/assets/image/fileCloudLayout2.png'
import configImg1 from '@/assets/image/fileCloudConfig1.png'
import configImg2 from '@/assets/image/fileCloudConfig2.png'
import addFolder from '@/components/fileCloud/addFolder.vue'
import permissionConfig from '@/components/fileCloud/permission.vue'
import tenantName from '@/components/fileCloud/tenantName.vue'
import upload from '@/components/fileCloud/upload.vue'
import readFile from '@/views/system/notify/list/readFile.vue'
import { getTenantId } from '@/utils/auth'
const message = useMessage()
const { t } = useI18n()

const appStore = useAppStore()

const collapse = computed(() => appStore.getCollapse)
const isCollapse = computed(() => appStore.getCollapse)
watch(
  () => collapse.value,
  (newPath, oldPath) => {
    isCollapse.value = newPath
  }
)
// 监听浏览器宽度
const windowWidth = ref(window.innerWidth)
const handleResize = () => {
  windowWidth.value = window.innerWidth
}

const leftList = ref([
  // { name: '最近', url: fileCloud1 },
  // { name: '我的文件', url: fileCloud2 },
  { name: '团队文件', url: fileCloud3 },
  // { name: '聊天文件', url: fileCloud4 },
  // { name: '分享', url: fileCloud5 },
])
const activeLi = ref(0)
const activeName = ref('团队文件')
const changeLi = (item, index) => {
  activeName.value = item.name
  activeLi.value = index
}
// 面包屑列表
const crumbsList = ref([
  { name: '全部', parentId: 0, id: 0 }
])
// 点击面包屑导航
const breadcrumbClick = (item, index) => {
  if (queryParams.id == item.id) return
  queryParams.id = item.id
  crumbsList.value.splice(index + 1)
  getList()
}
// 权限列表人员
const permissionUser = ref([])
// 权限修改
const handlePermission = async (item) => {
  try {
    if (!item) {
      const res = await fetchPermissions()
      if (!res || (res && res.permission != 1)) return message.warning('暂无权限')
    }
    const params = item ? { ...item, tenantId: queryParams.tenantId } : { id: queryParams.id, tenantId: queryParams.tenantId }
    permissionRef.value.open(params)
  } finally {

  }
}
// 布局
const layoutActive = ref(1)
const layoutList = ref([
  { url: layout1 },
  { url: layout2 }
])
// 布局change
const handleLayout = (index) => {
  layoutActive.value = index
}
const queryParams = reactive({
  pageNo: 1,
  pageSize: 100,
  id: 0,
  tenantId: getTenantId()
})
const tableData = ref([])
const loading = ref(false)
const getList = async () => {
  loading.value = true
  try {
    const apiCall = queryParams.id != 0 ? fileCloudApi.cloudList : fileCloudApi.rootPage;
    const res = await apiCall(queryParams)
    tableData.value = queryParams.id != 0 ? res : res.list

  } finally {
    loading.value = false
  }
}
const handleSelectionChange = (row) => {

}
// 时间转换
const formatDate = (timestamp) => {
  // const isoTimestamp = timestamp.replace(' ', 'T')
  const date = new Date(timestamp)
  const today = new Date()

  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')

  const hours = String(date.getHours()).padStart(2, '0')
  const minutes = String(date.getMinutes()).padStart(2, '0')

  if (date.getFullYear() === today.getFullYear() && date.getMonth() === today.getMonth() && date.getDate() === today.getDate()) {
    return `${hours}:${minutes}`
  } else {
    return `${year}-${month}-${day}`
  }
}

const addFolderRef = ref()
const handleAdd = (type) => {
  addFolderRef.value.open(type, { parentId: queryParams.id }, queryParams.tenantId)
}

const permissionRef = ref()
const folderSuccess = (type) => {
  if (type == 'create' && queryParams.id != 0) {
    let obj = {
      id: queryParams.id,
      tenantId: queryParams.tenantId
    }
    // permissionRef.value.open(obj)
  }
  getList()
}
const uploadRef = ref()
const handleUpload = () => {
  uploadRef.value.open(queryParams.id, queryParams.tenantId)
}
const uploadSuccess = () => {
  getList()
}
// 点详情
const readFileRef = ref()
const handleDetail = async (item) => {
  ElMessage.closeAll()
  // loading.value = true
  try {
    if (item.type == 2) {
      const res = await fetchPermissions(item)
      fileOpen(res, item)
    } else {
      nextCatalog(item)
    }
  } finally {
    loading.value = false
  }
}
const fileOpen = (res, item) => {
  let isDown = res && res.permission ? true : false
  if (isDown) {
    let obj = {
      name: item.name,
      url: item.perviewPath,
      downloadUrl: item.path
    }
    readFileRef.value.open(obj, isDown, '1')
  } else {
    message.warning('暂无权限')
  }
}
const nextCatalog = (item) => {
  queryParams.id = item.id
  tableData.value = []
  getList()
  myPermission()
  crumbsList.value.push({
    name: item.name,
    parentId: item.parentId,
    id: item.id
  })

}



// 更多下拉
const handleCommand = (i, item) => {
  switch (i) {
    case 'permission':
      handlePermission(item)
      break
    case 'rename':
      handleRename(item)
      break
    case 'delete':
      handleDelete(item)
      break
    default:
      break
  }
}
// 重命名
const handleRename = async (item) => {
  addFolderRef.value.open('update', item, queryParams.tenantId)
}
// 删除
const handleDelete = async (item) => {
  try {
    await message.delConfirm()
    if (item.type == 2) {
      await fileCloudApi.fileDelete(item.id)
    } else {
      await fileCloudApi.delete(item.id)
    }
    message.success(t('common.delSuccess'))
    await getList()
  } catch { }
}

// 切换租户
const resetPage = (id) => {
  queryParams.tenantId = id
  crumbsList.value = [{ name: '全部', parentId: 0, id: 0 }]
  queryParams.id = 0
  getList()
}

const permissionNum = ref(-1)//是否为最高权限：1管理
const myPermission = async () => {
  permissionUser.value = []
  if (queryParams.id == 0) return
  try {
    const res = await fetchPermissions()
    permissionNum.value = res.permission
    getPermissionList()
  } finally {

  }
}
const fetchPermissions = async (item) => {
  let obj = {
    cloudId: item ? item.id : queryParams.id,
    type: item && item.type ? item.type : 1,
    tenantId: queryParams.tenantId
  }
  const res = await fileCloudApi.getMyPermission(obj)
  return res
}

const getPermissionList = async () => {
  try {
    const res = await fileCloudApi.permissionList({
      cloudId: queryParams.id,
      type: 1,
      tenantId: queryParams.tenantId
    })
    permissionUser.value = res
  } finally {

  }
}

const itemRefs = ref([])
const setItemRef = (index) => {
  return (el) => {
    itemRefs.value[index] = el
  }
}
const handleClick = async (item, index) => {
  ElMessage.closeAll()
  // loading.value = true
  try {
    const res = await fetchPermissions(item)
    console.log(res)
    if (!res) {
      console.log('?')
      return message.warning('暂无权限')
    } else if (res.permission == 3) {
      console.log('??')
      return message.warning('暂无权限')
    } else {
      console.log('???')
      item.permission = res.permission
      if (itemRefs.value[index]) {
        itemRefs.value[index].handleOpen()
      }
    }

  } finally {
    loading.value = false
  }

}





onMounted(() => {
  window.addEventListener('resize', handleResize)
  getList()
  myPermission()

})
onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
})
</script>
<style lang="less" scoped>
p {
  padding: 0;
  margin: 0;
}

.rowBox {
  margin: 0 0 0 20px;
  position: fixed;
  left: 180px;
  right: 0;
  top: 81px;
  bottom: 0;
  font-size: 14px;
  background: #fff;
  color: #303133;
}

.isCol {
  left: 64px;
  margin-left: 0 !important;
}

.isCBox-else {
  left: 0;
}

.el-col {
  height: 100%;
}

.left-col {
  padding: 12px !important;
  border-right: 1px solid #eceded;
  box-sizing: border-box;

}

.rightCol {
  padding: 18px 32px;
}

.left-col .title {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 10px;
}

.leftUl {
  margin-top: 16px;
}

.leftUl li {
  padding: 12px;
  display: flex;
  align-items: center;
  margin-bottom: 6px;
  border-radius: 4px;
  cursor: pointer;

  img {
    margin-right: 4px;
  }
}

.activeLi {
  background: #F3F4F7;
}

.rightTop {
  display: flex;
  justify-content: space-between;
  align-items: center;

  p {
    font-weight: bold;
    font-size: 16px;
  }

  .el-button--primary {
    background: #3370FF;
  }
}

.crumbDiv {
  margin: 24px 0;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.crumb-left {
  display: flex;
  align-items: center;

  :deep(.el-breadcrumb__inner) {
    display: flex;
    align-items: center;
    cursor: pointer;
    color: #A2A3A5;

    img {
      margin-right: 8px;
      width: 28px;
    }
  }

  :deep(.el-breadcrumb__inner:hover) {
    color: #54a7ee;
  }

  :deep(.el-breadcrumb__item:nth-last-child(1) .el-breadcrumb__inner) {
    color: #303133;
    cursor: inherit;
  }
}

.onlyOne {
  font-size: 16px;

  :deep(.el-breadcrumb__inner) {
    font-weight: bold;
  }
}

.flex {
  align-items: center;
}

.crumb-right {
  display: flex;
  cursor: pointer;

  .user {
    :deep(.avatar .a-img > div) {
      border-radius: 50%;
      font-size: 9px;
      background: #3370FF;
    }

    :deep(.avatar .name) {
      display: none;
    }
  }

  .secondPosition {
    position: relative;
  }

  .absoluteP {
    position: absolute;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    z-index: 1;
    background: rgba(0, 0, 0, 0.4);
    border-radius: 50%;
    color: #fff;
    display: flex;
    justify-content: center;
    align-items: center;
    pointer-events: none;
  }

  .add {
    margin-left: 6px;
    color: #fff;
    width: 24px;
    height: 24px;
    background: #3370FF;
    border-radius: 50%;
    font-size: 22px;
    display: flex;
    justify-content: center;
    align-items: center;
    box-sizing: border-box;
  }
}

.layout {
  margin-right: 4px;
  margin-left: 12px;
  position: relative;
  padding-left: 12px;

  img {
    padding: 4px;
    cursor: pointer;
    margin-left: 4px;
  }

  .activeLayout {
    background: #F3F4F7;
    border-radius: 4px;
  }
}

.layout::before {
  content: ' ';
  position: absolute;
  left: 0;
  top: 6px;
  background: #c1c1c1;
  width: 1px;
  height: 12px;
}

.tableFlex {
  display: flex;
  align-items: center;

  p {
    line-height: 20px;
  }

  p:nth-child(1) {
    width: 40%;
    display: flex;
    align-items: center;

    img {
      margin-right: 16px;
      width: 32px;
    }
  }

  p:nth-child(2) {
    width: 20%;
  }

  p:nth-child(3) {
    width: 20%;
  }

  p:nth-child(4) {
    width: 12%;
  }

  p:nth-child(5) {
    width: 8%;
  }
}

.tableTop {
  padding-bottom: 12px;
  margin-bottom: 12px;
  border-bottom: 1px solid #ECEDED;
  font-size: 12px;
  color: #606266;
  font-weight: bold;
}

.tableBottom {
  padding: 16px 0;
  font-size: 14px;
  color: #606266;
  position: relative;
  cursor: pointer;
  box-sizing: border-box;

  p:nth-child(1) {
    color: #303133;
  }
}


.tableBottom:hover::before {
  cursor: pointer;
  background: #F3F4F7;
  border-radius: 4px;
  content: ' ';
  position: absolute;
  left: -10px;
  right: 0;
  top: 0;
  height: 100%;
  z-index: -1;
}

.configP {
  opacity: 0;
  position: relative;

  .imgTip {
    // position: absolute;
  }
}

.tableBottom:hover .configP {
  opacity: 1;
}

:deep(.el-tooltip__trigger:focus-visible) {
  outline: unset;
}

.alignP {
  text-align: center;
  margin-top: 50px;
  color: #909399;
}

:deep(.el-dropdown-menu__item:not(.is-disabled):focus) {
  background: #F3F4F7;
  color: #303133;
}
</style>
