<template>
  <div>
    <el-card class="oaCard">
      <h4 class="title">生活应用</h4>
      <el-row>
        <el-col :xs="24" :sm="20" :md="20" :lg="20" :xl="20">
          <el-form class="mb-2px" :model="queryParams" ref="queryFormRef" :inline="true">
            <el-form-item>
              <el-input v-model="searchParams.name" placeholder="请输入生活应用名称" clearable class="!w-200px"
                @change="getList" />
            </el-form-item>
            <el-form-item>
              <el-select v-model="searchParams.status" placeholder="请选择状态" clearable class="!w-200px" @change="getList">
                <el-option label="启用" value="0" />
                <el-option label="禁用" value="1" />
              </el-select>
              <el-form-item>
                <el-select v-model="searchParams.lifeCenterTypeId" placeholder="请选择分类" clearable class="!w-200px"
                  style="margin-left:20px" @change="getList">
                  <el-option v-for="item in typeListAll" :key="item.id" :label="item.name" :value="item.id" />
                </el-select>
              </el-form-item>
            </el-form-item>
          </el-form>
        </el-col>
        <el-col :xs="24" :sm="4" :md="4" :lg="4" :xl="4" class="rightBtn">

          <div class="btn" style="margin-left:20px" @click="openForm('create')">
            <img src="@/assets/imgs/addApp.png" />
            创建应用
          </div>
          <div class="btn" @click="openTypeDialog()">
            <img src="@/assets/image/fileCloudLayout1.png">
            分类管理
          </div>

        </el-col>
      </el-row>

      <el-table :data="tableData" v-loading="loading">
        <el-table-column label="生活应用名称" prop="name">
          <template #default="scope">
            <div class="nameClass">
              <!-- <icon name="el-icon-coordinate w-h-center" /> -->
              <img :src="scope.row.appIcon ? scope.row.appIcon : logoBlue" class="logo-left" />
              <div>
                <p>{{ scope.row.name }}</p>
                <p class="p2">{{ scope.row.appType }}</p>
              </div>
            </div>
          </template>
        </el-table-column>
        <!-- <el-table-column label="开发者" prop="from" /> -->

        <el-table-column label="分类名称" prop="range">
          <template #default="scope">

            <span v-for="(item, i) in typeListAll" :key="i">
              <template v-if="item.id == scope.row.lifeCenterTypeId">
                {{ item.name }}
              </template>
            </span>

          </template>
        </el-table-column>

        <!-- <el-table-column label="可见范围" prop="range">
        <template #default="scope">
          <el-tag :type="scope.row.scopeType == 'all' ? '' : scope.row.scopeType == 'part' ? 'success' :
            scope.row.scopeType == 'admin' ? 'info' : 'warning'">
            {{ scope.row.scopeType == 'all' ? '全部成员' : scope.row.scopeType == 'part' ? '部分成员' : scope.row.scopeType ==
              'admin' ? '企业管理员' : scope.row.scopeType }}
          </el-tag>
        </template>
      </el-table-column> -->

        <el-table-column label="操作">
          <template #default="scope">
            <!-- <el-button link type="primary" @click="handleDetail(scope.row)">详情</el-button> -->
            <el-button link type="primary" @click="openForm('update', scope.row)">编辑</el-button>
            <!-- <el-button link type="danger" v-if="scope.row.appStatus != 0">停用</el-button>
          <el-button link type="primary" v-if="scope.row.appStatus == 0">启用</el-button> -->
            <el-button link type="danger" v-if="!scope.row.canDelete" @click="handleDelete(scope.row.id)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <Pagination :total="total" v-model:page="queryParams.pageNo" v-model:limit="queryParams.pageSize"
        @pagination="getList" />
    </el-card>
    <!-- 新增应用 drawer -->
    <addForm ref="formRef" @success="getList" />
    <!-- 分类管理 dialog -->
    <el-dialog title="分类管理" class="addtype_dialog" v-model="typeListVisible" width="900px" append-to-body>
      <div class="record_con">
        <div style="padding: 20px 0">
          <el-button type="primary" size="small" @click="openAddDialog('add')">新增</el-button>
        </div>
        <el-table v-loading="recordLoading" :data="typeList" border>
          <el-table-column label="序号" type="index" align="center" width="120"></el-table-column>
          <el-table-column label="分类名称" align="center" prop="name" />
          <el-table-column label="图标" align="center" prop="icon">
            <template #default="{ row }">
              <el-image v-show="row.icon" :src="row.icon" fit="cover" style="width: 50px; height: 50px;"></el-image> 
            </template>
          </el-table-column>
          <el-table-column label="排序" align="center" prop="rank" />
          <el-table-column label="操作" align="center" fixed="right" width="120">
            <template #default="scope">
              <el-button link type="primary" @click="openAddDialog('update',scope.row)">编辑</el-button>
              <el-button link type="danger" v-if="!scope.row.canDelete"
                @click="handleDeleteType(scope.row.id)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
        <Pagination :total="totalRecord" v-model:page="recordParams.pageNo" v-model:limit="recordParams.pageSize"
          @pagination="getTypeList" />
      </div>
    </el-dialog>
    <!-- 新增分类 dialog -->
    <el-dialog :title="`${typeId ? '编辑' : '新增'}分类`" class="addtype_dialog" v-model="typeAddVisible" width="500px" append-to-body
      @closed="resetAddTypeDialog">
      <div class="addtype_dia_con" style="padding-top: 30px">
        <el-form label-width="100px">
          <el-form-item label="分类名称：" prop="typeName">
            <el-input v-model="typeName" placeholder="请输入分类名称" clearable style="width: 100%" />
          </el-form-item>
 <el-form-item label="排序：" prop="typeRank">
<el-input-number v-model="typeRank"  :min="0"  label="描述文字"></el-input-number>
</el-form-item>
          <el-form-item label="图标：" prop="icon" class="iconItem">
            <el-upload 
              action="#" 
              :auto-upload="false" 
              :file-list="typeImgList" 
              :limit="1"
              list-type="picture-card"
              :on-change="onChange" 
              :on-remove="onRemove" 
              :on-exceed="onExceed" 
              :on-preview="handlePictureCardPreview" 
              :class="{ 'isImgBox': typeImgList.length > 0 }"
            >
              <el-icon class="avatar-uploader-icon" v-if="typeImgList.length == 0"><Plus /></el-icon>
            </el-upload>
            <el-image-viewer v-if="previewVisible" :url-list="[previewImgUrl]" @close="previewVisible = false" />
          </el-form-item>
        </el-form>
      </div>
      <div class="dialog-footer" slot="footer">
        <el-button @click="typeAddVisible = false">取 消</el-button>
        <el-button type="primary" @click="typeDialogConfirm" :loading="typeAddLoading">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script lang="ts" setup>

import {ref, onMounted, reactive} from 'vue';
import logoBlue from '@/assets/imgs/logoBlue.png';
import { Plus } from '@element-plus/icons-vue';
import { applicationApi } from '@/api/application/list'
import addForm from './drawer/form.vue'
import * as FileApi from '@/api/infra/file'
const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化
defineOptions({ name: 'LifeCenterApp' })
//分类管理数据
const typeListVisible=ref(false)
const typeList=ref([]) //分类管理列表data
const totalRecord=ref(0) //分类列表的总页数
const recordParams = reactive<{
  type: number,
  pageNo: any,
  pageSize: any
}>({
  type: 2, // 1应用中心 2生活中心
  pageNo: 1,
  pageSize: 10,
})
//分类新增/编辑数据
const typeAddVisible=ref(false)
const recordLoading=ref(false)
const typeName=ref("")
const typeId=ref("")
let typeRank=ref(1)
const typeImgList = ref<any>([]) //el-upload绑定的fileList
const typeUploadedImgList = ref<string[]>([])
const haveNetworkImgUrl = ref(false)
const previewImgUrl = ref('')
const previewVisible = ref(false)
const typeAddLoading = ref(false)

const tableData = ref([])//列表data
const total = ref(0) // 列表的总页数
const queryParams = reactive<{
  type:number,
  pageNo: any,
  pageSize: any,
}>({
  // 1应用中心 2生活中心
  type:2,
  pageNo: 1,
  pageSize: 10,
})
const searchParams = reactive<{
  name: any,
  status: any,
  lifeCenterTypeId: number | undefined
}>({
  name: '',
  status: '',
  lifeCenterTypeId: undefined
})
const queryFormRef = ref() // 搜索条件
const loading = ref(false)//表格加载中
// 列表
const getList = async () => {
  try {
    loading.value = true
    const res = await applicationApi.scopeList({ ...queryParams, ...searchParams })
    tableData.value = res.list
    total.value = res.total
  } finally {
    loading.value = false
  }
}

const typeListAll = ref<any[]>([])
// 分类下拉列表
const getTypeListAll = async () => {
  try {
    const res = await applicationApi.lifeTypeList({ type: 2 })
    typeListAll.value = res
  } finally {
  }
}

const visibleList = ref<any[]>([])
// 可见范围下拉列表
const getVisible = async () => {
  try {
    const res = await applicationApi.applicationScope()
    visibleList.value = res
  } finally {
  }
}
// 添加/修改操作 字段
const formRef = ref()
const openForm = (type: string, row?: any) => {
  formRef.value.open(type, row)
}
// 删除
const handleDelete = async (id: number) => {
  try {
    await message.delConfirm()
    await applicationApi.deleteApplication(id)
    message.success(t('common.delSuccess'))
    await getList()
  } catch { }
}

// 分类管理弹框
const openTypeDialog = () => {
      recordParams.pageNo = 1
      typeListVisible.value = true
      getTypeList()
}
// 分类列表
const getTypeList = async () => {
  try {
    recordLoading.value = true
    const res = await applicationApi.lifeTypeListPage({ ...recordParams })
    typeList.value = res.list
    totalRecord.value = res.total
  } finally {
    recordLoading.value = false
  }
}
// 删除分类
const handleDeleteType = async (id: number) => {
  try {
    await message.delConfirm()
    await applicationApi.deleteType(id)
    message.success(t('common.delSuccess'))
    await getTypeList()
  } catch { }
}

// 打开 分类新增/编辑 dialog
const openAddDialog = (type: string, row?: any) => {
  if (type=='update') {
    typeId.value = row.id
    typeName.value = row.name
    typeRank.value=row.rank
    if (row.icon) {
      typeImgList.value = [{ url: row.icon }]
      haveNetworkImgUrl.value = true
    }    
  }else{
    typeId.value = ""
  typeName.value = ""
  typeRank.value=1
  typeImgList.value = []
  haveNetworkImgUrl.value = false
  typeUploadedImgList.value = []
  }
  typeAddVisible.value = true
}

// 重置
const resetAddTypeDialog = () => {
  typeId.value = ""
  typeName.value = ""
  typeRank.value=1
  typeImgList.value = []
  haveNetworkImgUrl.value = false
  typeUploadedImgList.value = []
}

// 分类图片组件 - 移除
const onRemove = (_: any, list: any) => {
  haveNetworkImgUrl.value = false
  typeImgList.value = list
}

// 分类图片组件 - 变动
const onChange = (_: any, list: any) => {
  haveNetworkImgUrl.value = false
  typeImgList.value = list
}

// 分类图片组件 - 超出限制
const onExceed = () => {
  ElMessage.warning("当前最多只能上传1张图片，请移除后上传")
}

// 分类图片组件 - 预览
const handlePictureCardPreview = (file: any) => {
  previewImgUrl.value = file.url
  previewVisible.value = true
}

// 分类新增/编辑 确定
const typeDialogConfirm = () => {
  if (!typeName.value) {
    return message.warning('请输入分类名称')
  }
  typeAddLoading.value = true
  handleImageUpload()
}

// 处理图片上传
const handleImageUpload = () => {
  if (typeImgList.value.length > 0 && !haveNetworkImgUrl.value) {
    const uploadQueue = <any>[]
    typeImgList.value.forEach((e: any) => {
      uploadQueue.push(        
        FileApi.updateFile(<any>{ file: e.raw })
      )
    })
    Promise.all(uploadQueue).then(async (res: any) => {
      typeUploadedImgList.value = []
      let isFault = false;
      res.forEach((item: any) => {
        if (item.code === 0) {
          typeUploadedImgList.value.push(item.data)
        } else {
          isFault = true
        }
      })
      if (isFault) {
        ElMessage.error("图片上传失败，请重新上传")
        typeAddLoading.value = false
        return
      }
      handleCommit()
    }).catch(() => {
      ElMessage.error("图片上传失败，请重新上传")
      typeAddLoading.value = false
    })
  } else {
    handleCommit()
  }
}

const handleCommit = async () => {
  if (typeId.value) {
    await applicationApi.updateType({
      type: 2, // 1应用中心 2生活中心
      id: typeId.value,
      name: typeName.value,
      rank:typeRank.value,
      icon: haveNetworkImgUrl.value ? typeImgList.value[0].url : (typeUploadedImgList.value.length ? typeUploadedImgList.value[0] : '')
    })
    message.success('操作成功')
    typeAddVisible.value = false
    getTypeList()
  } else {
    await applicationApi.createType({
      type: 2, // 1应用中心 2生活中心
      name: typeName.value,
      rank:typeRank.value,
      icon: typeUploadedImgList.value.length ? typeUploadedImgList.value[0] : ''
    })
    message.success('操作成功')
    getTypeList()
    typeAddVisible.value = false
  }
  typeAddLoading.value = false
}

onMounted(() => {
  getTypeListAll()
  getVisible()
  getList()
})
</script>

<style lang="less" scoped>
.title {
  margin: 0 0 20px;
}

:deep(.el-form--inline .el-form-item) {
  margin-right: 10px;
}

.nameClass {
  display: flex;
  align-items: center;

  :deep(.el-icon-coordinate) {
    width: 40px;
    height: 40px;
    background: #FFE8B8;
    border-radius: 8px;
    font-size: 24px;
    color: #FF9200;
    margin-right: 8px;
  }

  .logo-left {
    width: 40px;
    height: 40px;
    border-radius: 8px;
    border: 1px solid #fafafa;
    box-sizing: border-box;
    margin-right: 8px;
  }

  p {
    margin: 0;
    line-height: 140%;
    font-size: 14px;
  }

  .p2 {
    color: #909399;
    font-size: 12px;
    padding: 0;
  }
}

.el-table {
  margin-top: 0;
}

.rightBtn {
  .btn {
    float: right;
    border-radius: 4px;
    border: 1px solid #dcdfe6;
    font-size: 14px;
    padding: 5px 12px;
    width: fit-content;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;

    img {
      width: 15px;
      margin-right: 5px;
    }
  }
}

.addtype_dialog {

  .record_con {
    padding: 0px 20px 60px 20px;
  }

  .addtype_dia_con {
    max-height: calc(70vh);
    overflow-y: auto;
    padding: 0 40px;
  }
}

.dialog-footer {
  margin: 20px 0 10px 0;
  text-align: center;
}

.iconItem {
  :deep(.el-form-item__content) {
    display: inline;
  }

  :deep(.el-upload--picture-card) {
    width: 100px;
    height: 100px;
    background: #ECEDED;
    border: none;
    margin-top: 4px;
    border-radius: 4px;
  }

  :deep(.el-upload--picture-card:hover) {
    border: 1px dashed #ccc;
  }

  .isImgBox {
    margin-top: 4px;
    height: 100px;
  }

  :deep(.isImgBox .el-upload--picture-card) {
    display: none;
  }

  :deep(.el-upload-list--picture-card .el-upload-list__item) {
    height: 100px;
    width: 100px;
  }

  .tipP {
    margin: 0;
    line-height: normal;
    margin-top: 4px;
  }
}
</style>
