<template>
  <el-drawer v-model="isVisible" size="560px" direction="rtl" title="新成员申请" class="custom-detail-header">
    <div class="main-view" ref="listRef" v-infinite-scroll="load" :infinite-scroll-disabled="loadDisabled"
      :infinite-scroll-immediate="false">
      <div class="item-view" v-for="item in list" :key="item.id">
        <div class="left-view">
          <el-image :src="defaultIcon" />
        </div>
        <div class="right-view">
          <div class="top-content">
            <div class="personal-info">
              <span class="name">{{ item.username }}</span>
              <span class="mobile">{{ item.mobile }}</span>
            </div>
            <span class="status"
              :class="{ 'status-agree': item.approveStatus == 0, 'status-refuse': item.approveStatus == 1, 'status-unreview': item.approveStatus != 0 && item.approveStatus != 1 }">{{
                item.approveStatus == 0 ? '已同意' : (item.approveStatus == 1 ? '已拒绝' : '未审核') }}</span>
          </div>
          <h6 class="title">申请加入</h6>
          <span class="info">{{ item.tenantName ? item.tenantName : '--' }}</span>
          <h6 class="title">邀请人姓名</h6>
          <span class="info">{{ item.inviterName ? item.inviterName : '--' }}</span>
          <h6 class="title">加入部门</h6>
          <span class="info">{{ item.deptName ? item.deptName : '--' }}</span>
          <div class="bottom-content">
            <div class="reason-info">
              <h6 class="title">加入原因</h6>
              <span class="info">{{ item.applyReason ? item.applyReason : '--' }}</span>
            </div>
            <div v-if="item.approveStatus != 0 && item.approveStatus != 1" class="option-btns">
              <el-button :loading="currentApproveId === item.id && approveLoading" @click="onApprove(item, 1)">拒绝</el-button>
              <el-button type="primary" color="#3370FF" :loading="currentApproveId === item.id && approveLoading"
                @click="onApprove(item, 0)">通过</el-button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </el-drawer>

</template>

<script setup lang="ts">

import { StaffApplyApi, approve } from '@/api/hrManage/newStaff'
import defaultIcon from '@/assets/image/appLogo.png';

const message = useMessage()
const pageSize = 10

const isVisible = ref(false)
const queryparams = {
  pageNo: 1,
  pageSize: pageSize,
  approveStatus: -1 //-1_未审批；0_同意；1_拒绝
}
const listRef = ref<HTMLElement | null>()
const list = ref<any[]>([])
const loading = ref(false)
const noMore = ref(false)
const loadDisabled = computed(() => loading.value || noMore.value)

const reload = async (isStart: boolean) => {
  if (isStart) {
    queryparams.pageNo = 1
    queryparams.pageSize = pageSize
  } else {
    queryparams.pageSize = queryparams.pageNo * pageSize
    queryparams.pageNo = 1
  }

  noMore.value = false
  try {
    loading.value = true
    const data = await StaffApplyApi.getStaffApplyPage(queryparams)
    if (data.list.length > 0) {
      list.value = data.list
    }
    if (list.value.length == data.total) {
      noMore.value = true
    }
  } finally {
    loading.value = false
  }
}

const load = async () => {
  queryparams.pageNo++
  queryparams.pageSize = pageSize
  try {
    loading.value = true
    const data = await StaffApplyApi.getStaffApplyPage(queryparams)
    if (data.list.length > 0) {
      list.value.push(...data.list)
    }
    if (list.value.length == data.total) {
      noMore.value = true
    }
  } finally {
    loading.value = false
  }
}

const currentApproveId = ref<number | undefined>(undefined)
const approveLoading = ref(false)
const onApprove = async (item: any, status: number) => {
  await message.confirm(`确定要${status == 0 ? '通过' : '拒绝'}该申请吗？`)
  try {
    currentApproveId.value = item.id
    approveLoading.value = true
    const res = await approve({ id: item.id, approveStatus: status })
    approveLoading.value = false
    if (res.code == 0) {
      reload(false)
      message.success('操作成功')
    } else {
      message.error(res.msg ?? '操作失败')
    }
  } finally {
    currentApproveId.value = undefined
    approveLoading.value = false
  }
}

const openNewApply = () => {
  list.value = []
  if (listRef.value) {
    listRef.value.scrollTop = 0
  }
  isVisible.value = true
  reload(true)
}

defineExpose({ openNewApply })

</script>

<style lang="less" scoped>
.main-view {
  padding: 0 24px 24px 24px;
}

.item-view {
  padding: 32px 0;
  display: flex;
  justify-content: space-between;
  border-bottom: 1px solid #ECEDED;
}

.left-view {
  width: 48px;
  height: 48px;
  margin-right: 16px;
  border-radius: 8px;
}

.right-view {
  display: flex;
  flex-direction: column;
  flex: 1;

  .title {
    margin: 14px 0 4px 0;
    font-size: 12px;
    color: #aaa;
  }

  .info {
    font-size: 14px;
    color: #303133;
  }

  .top-content {
    display: flex;
    justify-content: space-between;

    .personal-info {
      display: flex;
      flex-direction: column;
      gap: 2px;
      flex: 1;

      .name {
        height: 25px;
        font-size: 18px;
        font-weight: 500;
        color: #303133;
      }

      .mobile {
        height: 18px;
        font-size: 14px;
        color: #303133;
      }
    }

    .status {
      font-size: 14px;
    }

    .status-agree {
      color: #22cc24
    }

    .status-refuse {
      color: #ff304a
    }

    .status-unreview {
      color: #E59119;
    }
  }

  .bottom-content {
    display: flex;
    justify-content: space-between;
    align-items: flex-end;

    .reason-info {
      flex: 1;
    }

    .option-btns {
      margin-left: 10px;

      .el-button {
        font-size: 14px;
      }
    }
  }
}
</style>