<template>
  <div>
    <div ref="targetElement">
      <!-- 检测页面是否可见 -->
    </div>
    <el-row
      class="rowBox relative"
      :class="{ isCol: isCollapse, 'isCBox-else': windowWidth < 768 }"
      v-loading="loading"
    >
      <el-col :span="4" :xl="4" :lg="6" :md="8" :sm="8" :xs="10" class="left-col">
        <div class="left-top center-align" style="justify-content: space-between">
          <div class="blue-button center-view"> 全部</div>
          <div @click="handleCreateGroup('createGroup')" class="blue-button center-view">
            <img
              src="@/assets/image/add.png"
              style="width: 10px; height: 10px; margin-right: 4px"
            />
            发起群聊
          </div>
          <!-- <el-radio-group v-model="radio">
            <el-radio label="0" border>全部</el-radio>
            <el-radio :label="item.value" v-for="(item, index) in radioList" :key="index" border>
              {{ item.label }}
            </el-radio>
          </el-radio-group> -->
        </div>
        <div class="forDiv" @contextmenu="showContextMenu">
          <div
            v-for="(item, index) in leftList"
            :key="index"
            class="contentBox"
            @click="activateTab(index, item)"
            :class="{ active: activeTab == index, topClass: item.top }"
            :data-obj="JSON.stringify(item)"
            :data-index="index"
            v-show="!item.hidden"
          >
            <img
              src="@/assets/image/closeNotify.png"
              v-if="item.mute"
              class="muteClass"
              :style="{ right: item.lastMessage.unreadMessageTotal > 0 ? '20px' : '6px' }"
            />
            <el-badge
              is-dot
              class="messageHornClass"
              v-if="item.mute && item.lastMessage.unreadMessageTotal > 0"
            ></el-badge>
            <icon
              name="el-icon-close"
              class="closeClass"
              @click.stop="getSetTop('移除会话', item, index)"
            />

            <div v-if="item.targetType == 2" class="group-container">
              <div
                v-for="(member, memberIndex) in getGroupMembers(item)"
                :key="memberIndex"
                :style="groupAvatarStyle(item.groupInfo.member_count, memberIndex)"
                class="group-avatar"
              >
                <div v-if="member.avatar" class="group-member-avatar">
                  <el-avatar
                    shape="square"
                    :size="getAvatarSize(item.groupInfo.member_count)"
                    :src="member.avatar"
                  />
                </div>
                <div v-else class="groupDiv1">
                  {{ member.alias.substr(-1) }}
                </div>
              </div>
            </div>

            <div class="div1-avatar" v-if="item.targetType == 1 && item.target.portrait">
              <el-avatar shape="square" :size="42" :src="item.target.portrait" />
            </div>
            <div class="div1" v-if="item.targetType == 1 && !item.target.portrait">
              {{
                item.targetType == 2
                  ? item.groupInfo.name && item.groupInfo.name.length > 2
                    ? item.groupInfo.name.substr(-2)
                    : item.groupInfo.name
                  : item.target.displayName && item.target.displayName.length > 2
                    ? item.target.displayName.substr(-2)
                    : item.target.displayName
              }}
            </div>

            <div class="div2">
              <p
                class="p2-1"
                :title="item.targetType == 2 ? item.groupInfo.name : item.target.displayName"
              >
                {{ item.targetType == 2 ? item.groupInfo.name : item.target.displayName }}
              </p>
              <!-- <p class="p2-2">{{ item.lastMessage.payload.searchableContent }}</p> -->
              <p class="p2-2">
                <span
                  :class="{
                    specialBlue: item.lastMessage.readStatus == 0,
                    specialGrey: item.lastMessage.readStatus == 1
                  }"
                >
                  {{
                    item.lastMessage.isSender == 1
                      ? ''
                      : item.lastMessage.readStatus == 0
                        ? '[未读]'
                        : item.lastMessage.readStatus == 1
                          ? '[已读]'
                          : ''
                  }}
                </span>
                <div v-html="item.lastMessage.payload.searchableContent"></div>
              </p>
            </div>
            <div class="div3">
              <p class="p3-1">{{ toTime(item.timestamp) }}</p>
              <p class="p4-1">
                <el-badge
                  class="custom-badge"
                  color="#f00"
                  :value="getUnreadCount(item)"
                ></el-badge>
              </p>
            </div>
          </div>
          <leftMenu ref="leftMenuRef" @setTop="getSetTop" />
        </div>
      </el-col>
      <el-col :span="20" :xl="20" :lg="18" :md="16" :sm="16" :xs="14" class="rightCol">
        <div v-if="Object.keys(rowInfo).length > 0" class="right-top">
          <div class="top-content" @click="handleTopAvatar(rowInfo)">
            <div v-if="rowInfo.targetType == 2" class="group-container">
              <div
                v-for="(member, memberIndex) in getGroupMembers(rowInfo)"
                :key="memberIndex"
                :style="groupAvatarStyle(rowInfo.groupInfo.member_count, memberIndex)"
                class="group-avatar"
              >
                <div v-if="member.avatar" class="group-member-avatar">
                  <el-avatar
                    shape="square"
                    :size="getAvatarSize(rowInfo.groupInfo.member_count)"
                    :src="member.avatar"
                  />
                </div>
                <div v-else class="groupDiv1">
                  {{ member.alias.substr(-1) }}
                </div>
              </div>
            </div>
            <div class="div1-avatar" v-if="rowInfo.targetType == 1 && rowInfo.target.portrait">
              <el-avatar shape="square" :size="42" :src="rowInfo.target.portrait" />
            </div>
            <div v-else class="div1" v-if="rowInfo.targetType == 1">
              {{
                rowInfo.targetType == 2
                  ? rowInfo.groupInfo.name && rowInfo.groupInfo.name.length > 2
                    ? rowInfo.groupInfo.name.substr(-2)
                    : rowInfo.groupInfo.name
                  : rowInfo.target.displayName && rowInfo.target.displayName.length > 2
                    ? rowInfo.target.displayName.substr(-2)
                    : rowInfo.target.displayName
              }}
            </div>

            <div class="div2 div2-2">
              <p class="p2-1-1">{{
                  rowInfo.targetType == 2 ? rowInfo.groupInfo.name : rowInfo.target.displayName
                }}</p>
              <p class="p2-2" v-if="Object.keys(userCard).length > 0">
                {{ userCard.nickname }}（
                {{ userCard.tenantName }}
                {{ userCard.deptName.length > 0 ? '-' + userCard.deptName.join('、') : '' }}
                {{ userCard.postName.length > 0 ? ' | ' + userCard.postName.join('、') : '' }}）
              </p>
            </div>
          </div>
          <div class="center-align" v-if="rowInfo.targetType == 2">
            <img
              v-if="rowInfo.targetType == 1"
              src="@/assets/image/groupAdd.png"
              @click="handleCreateGroup('createGroup')"
              style="width: 20px; height: 20px; margin-right: 32px; cursor: pointer"
            />
            <img
              v-if="rowInfo.targetType == 2"
              src="@/assets/image/groupAdd.png"
              @click="handleCreateGroup('add', rowInfo.groupInfo.target_id)"
              style="width: 20px; height: 20px; margin-right: 32px; cursor: pointer"
            />

            <img
              v-if="rowInfo.targetType == 2"
              src="@/assets/image/groupSetting.png"
              @click="showGroupInfo(rowInfo.groupInfo.target_id)"
              style="width: 20px; height: 20px; margin-right: 20px; cursor: pointer"
            />
          </div>
        </div>
        <DialogComponent
          ref="scrollTarget"
          :detailList="detailList"
          :rowInfo="rowInfo"
          :isMultiSelectMsg="isMultiSelectMsg"
          :selectedMsg="selectedMsg"
          @handleAvatar="handleAvatar"
          @showMsgMenu="showMsgMenu"
          @handleSelectMsg="handleSelectMsg"
          @textClick="textClick"
          @fileOpen="fileOpen"
          @handleDetail="handleDetail"
          @handleRuZhi="handleRuZhi"
          @handleSign="handleSign"
          @handleUnRead="handleUnRead"
        />

        <div class="footerDiv" v-if="Object.keys(rowInfo).length > 0 && !isRobot && !isMultiSelectMsg">

          <div class="eventClass" style="position: relative">
            <TEmoji
              v-if="emojiShow"
              @chooseEmoji="chooseEmoji"
              v-click-out-side="vClickOutside"
            ></TEmoji>
            <div title="选择表情" @click="changeEmoji()">
              <div class="up-div"><img src="@/assets/image/smile.svg" /></div>
            </div>
            <div title="选择图片">
              <el-upload
                class="upload-replace"
                action=""
                :auto-upload="false"
                :show-file-list="false"
                multiple
                :on-change="handleImg"
                accept="image/*"
                ref="uploadRef"
              >
                <div class="up-div"><img src="@/assets/image/picture.svg" /></div>
              </el-upload>
            </div>
            <div title="选择附件">
              <el-upload
                class="upload-replace"
                action=""
                :auto-upload="false"
                :show-file-list="false"
                multiple
                :on-change="handleFile"
                ref="uploadRef2"
              >
                <div class="up-div"><img src="@/assets/image/file.svg" /></div>
              </el-upload>
            </div>

          </div>
          <!-- 引用内容组件 -->
          <div v-if="Object.keys(quoteMsg).length > 0" class="quote-message">
            <div class="quote-content">
              <div class="quote-sender">{{ quoteMsg.n }}</div>
              <div class="quote-text">{{ quoteMsg.d }}</div>
            </div>
            <el-button class="quote-close" type="text" @click="resetQuoteMsg">
              <el-icon>
                <Close />
              </el-icon>
            </el-button>
          </div>
          <div
            class="message-area"
            contenteditable="true"
            @paste.prevent="handlePaste"
            @input="handleInput"
            ref="messageArea"
            @keydown="handleCtrlEnter"
            @drop.prevent="handleDrop"
            @dragover.prevent="handleDragOver"
            @dragenter.prevent="handleDragEnter"
            @dragleave.prevent="handleDragLeave"
            :class="{'drag-over': isDragOver}"
          ></div>
          <div class="enterDiv">
            <span class="spanTip">Enter发送，Ctrl+Enter换行</span>
            <el-button
              type="primary"
              :disabled="isDisabled || currentMessage.trim() == ''"
              @click="sendMsg"
              @keyup.enter="keyDown(e)"
            >发 送
            </el-button>
          </div>
        </div>
        <!--        转发消息选择框-->
        <div class="footerDiv" v-if="isMultiSelectMsg">
          <div class="multi-select-footer">
            <div class="selected-count">已选择 {{ selectedMsg.length }} 条消息</div>
            <div class="action-buttons">
              <div class="action-button forward" @click="forwardSelectedMessages('one')"
                   :class="{ disabled: selectedMsg.length === 0 }">
                <img src="@/assets/image/forward-msg-one.png" alt="逐条转发" />
                <span>逐条转发</span>
              </div>
              <div class="action-button forward" @click="forwardSelectedMessages('together')"
                   :class="{ disabled: selectedMsg.length === 0 }">
                <img src="@/assets/image/forward-msg-multi.png" alt="合并转发" />
                <span>合并转发</span>
              </div>
              <div class="action-button cancel" @click="isMultiSelectMsg = false; selectedMsg = []">
                <img src="@/assets/image/forward-msg-cancel.png" alt="取消" />
                <span>取消</span>
              </div>
            </div>
          </div>
        </div>
      </el-col>
    </el-row>
    <!-- 预览图片 -->
    <el-image-viewer v-if="imgVisible" :url-list="[previewImg]" @close="imgVisible = false" />
    <!-- 审批详情 -->
    <el-drawer
      size="560px"
      direction="rtl"
      title="审批详情"
      v-model="processVisible"
      class="custom-detail-header"
    >
      <instance-preview v-if="processVisible" :node-id="isNodeId" :instance-id="selectInstance" />
    </el-drawer>
    <!-- 新成员申请 -->
    <NewApplyDrawer ref="newApplyRef" />
    <readMember ref="readRef"></readMember>
    <msgMenu ref="msgMenuRef" @setMsg="handleMsg" />
    <!-- 用户名片弹窗 -->
    <UserDialog ref="userRef" @setMsg="userDialogSetMsg"></UserDialog>
    <!-- 预览附件 -->
    <readFile ref="readFileRef" />

    <!--    入职完善抽屉-->
    <el-drawer
      size="1440px"
      direction="rtl"
      title="入职完善"
      v-model="ruzhiDrawerVisible"
      class="custom-detail-header"
    >
      <Personal ref="detailRef" :isPush="true" @updateParent="ruzhiDrawerVisible = false" />
    </el-drawer>

    <group-picker-view
      :title="groupPickerTitle"
      ref="groupPicker"
      :pickType="groupPickerType"
      :multiple="true"
      @ok="selected"
    />
    <!-- 群详情弹窗 -->
    <el-drawer
      v-model="groupDrawer"
      :with-header="false"
      :close-on-click-modal="true"
      size="440px"
      :style="groupDrawerType == 1 ? 'background-color: #f3f4f7' : 'background-color: #ffffff'"
    >
      <!-- 默认群设置 -->
      <div v-if="groupDrawerType == 1" style="height: 100%">
        <div class="center-align" style="justify-content: space-between; height: 30px">
          <div style="font-weight: 500; font-size: 15px; color: #303133; line-height: 21px">
            群设置
          </div>

          <img
            src="@/assets/image/close.png"
            style="cursor: pointer; width: 23px; height: 22px"
            @click="groupDrawer = false"
          />
        </div>
        <div style="height: 15px"></div>

        <el-scrollbar style="height: calc(100% - 45px)">
          <div style="display: flex">
            <div class="group-container">
              <div
                v-for="(member, memberIndex) in currentGroupUser"
                :key="memberIndex"
                :style="groupAvatarStyle(currentGroupUser.length, memberIndex)"
                class="group-avatar"
              >
                <div v-if="member.avatar" class="group-member-avatar">
                  <el-avatar
                    shape="square"
                    :size="getAvatarSize(currentGroupUser.length)"
                    :src="member.avatar"
                  />
                </div>
                <div v-else class="groupDiv1">
                  {{ member.alias.substr(-1) }}
                </div>
              </div>
            </div>

            <div>
              <div style="font-weight: 500; font-size: 15px; color: #303133; line-height: 21px">
                {{ currentGroupInfo.name }}
              </div>

              <div style="font-size: 11px; color: #a2a3a5; line-height: 16px">
                归属于
                {{ currentGroupInfo.tenantName }}
              </div>
            </div>
          </div>

          <div style="margin-top: 15px; background: #ffffff; border-radius: 10px; padding: 0 14px">
            <div class="center-align" style="height: 53px; justify-content: space-between">
              <div> 群成员</div>

              <img
                v-if="!isGroupMemberSearch"
                @click="isGroupMemberSearch = true"
                src="@/assets/image/search.png"
                style="cursor: pointer; width: 20px; height: 20px"
              />

              <div v-if="isGroupMemberSearch" class="center-align">
                <el-input
                  v-model="groupMemberSearchValue"
                  size="small"
                  placeholder="请输入成员名称"
                >
                  <template #prefix>
                    <img src="@/assets/image/search.png" style="width: 20px; height: 20px" />
                  </template>
                </el-input>

                <el-button
                  style="margin-left: 11px; font-size: 10px"
                  type="text"
                  @click=";(isGroupMemberSearch = false) & (groupMemberSearchValue = '')"
                >取消
                </el-button>
              </div>
            </div>

            <div style="display: flex; flex-wrap: wrap; column-gap: 30px; row-gap: 18px">
              <template v-for="(item, index) in currentGroupUser.slice(0, 19)" :key="index">
                <div
                  v-if="item.alias?.includes(groupMemberSearchValue)"
                  @click="showUserInfo(item.member_id)"
                  style="width: 35px; cursor: pointer"
                >
                  <div style="width: 35px; height: 35px">
                    <img
                      v-if="item.avatar"
                      :src="item.avatar"
                      style="width: 35px; height: 35px; border-radius: 6px"
                    />

                    <div
                      v-else
                      class="center-view"
                      style="
                        width: 35px;
                        height: 35px;
                        background: #3370ff;
                        border-radius: 6px;
                        font-size: 13px;
                        color: #ffffff;
                        line-height: 18px;
                      "
                    >
                      {{ item.alias?.length > 2 ? item.alias.substr(-2) : item.alias }}
                    </div>
                  </div>
                  <div class="center-view" style="width: 35px; margin-top: 5px">
                    <div
                      class="textover"
                      style="max-width: 35px; font-size: 11px; color: #909399; line-height: 16px"
                    >
                      {{ item.alias }}
                    </div>
                  </div>
                </div>
              </template>

              <div
                style="width: 35px; cursor: pointer"
                @click="handleCreateGroup('add', currentGroupInfo.target_id)"
              >
                <div style="width: 35px; height: 35px">
                  <img
                    src="@/assets/image/addCard.png"
                    style="width: 35px; height: 35px; border-radius: 6px"
                  />
                </div>

                <div
                  class="center-view"
                  style="font-size: 11px; color: #909399; line-height: 16px; margin-top: 6px"
                >
                  添加
                </div>
              </div>

              <!-- 当前简单判断是不是群主，只有群主能删除 -->
              <div
                v-if="useUserStore().getUser.id == currentGroupInfo.owner"
                style="width: 35px; cursor: pointer"
                @click="handleCreateGroup('remove', currentGroupInfo.target_id)"
              >
                <div style="width: 35px; height: 35px">
                  <img
                    src="@/assets/image/reduceCard.png"
                    style="width: 35px; height: 35px; border-radius: 6px"
                  />
                </div>
                <div
                  class="center-view"
                  style="font-size: 11px; color: #909399; line-height: 16px; margin-top: 6px"
                >
                  移除
                </div>
              </div>
            </div>
            <div
              class="center-view"
              style="cursor: pointer; margin-top: 18px"
              @click="groupDrawerTypeChange(2)"
            >
              <div style="font-size: 13px; color: #606266; line-height: 18px"> 查看全部群成员</div>
              <img
                src="@/assets/image/arrowRightGray.png"
                style="width: 19px; height: 19px; margin-left: 4px"
              />
            </div>
            <div style="height: 18px"></div>
          </div>

          <div
            style="
              margin-top: 14px;
              padding-left: 14px;
              font-size: 11px;
              color: #909399;
              line-height: 16px;
            "
          >
            群聊消息
          </div>
          <div style="margin-top: 5px; background: #ffffff; border-radius: 10px; padding: 0 14px">
            <div class="center-align" style="justify-content: space-between; padding: 14px 0">
              <div style="font-size: 13px; color: #303133; line-height: 18px"> 群名称</div>

              <div class="center-align">
                <div
                  v-if="!isGroupNameEdit"
                  style="font-size: 13px; color: #909399; line-height: 18px; margin-right: 5px"
                >
                  {{ currentGroupInfo.name }}
                </div>

                <img
                  v-if="!isGroupNameEdit"
                  @click="editGroupName()"
                  src="@/assets/image/edit.png"
                  style="cursor: pointer; width: 15px; height: 15px"
                />
                <div v-if="isGroupNameEdit" class="center-align">
                  <el-input
                    ref="groupNameRef"
                    size="small"
                    v-model="groupNameValue"
                    @keydown.enter="groupNameChange()"
                    @blur="groupNameChange()"
                    placeholder="请输入群名称"
                  >
                  </el-input>
                </div>
              </div>
            </div>
          </div>

          <div
            style="
              margin-top: 15px;
              padding-left: 14px;
              font-size: 11px;
              color: #909399;
              line-height: 16px;
            "
          >
            个性化设置，仅对自己生效
          </div>
          <div style="margin-top: 5px; background: #ffffff; border-radius: 10px; padding: 0 14px">
            <!-- <div class="center-align" style="justify-content: space-between; padding: 14px 0">
              <div style="font-size: 18px; color: #303133; line-height: 25px">我在本群的昵称</div>

              <img
                src="@/assets/image/edit.png"
                style="cursor: pointer; width: 21px; height: 21px"
              />
            </div>
            <div style="height: 1px; background: #ebeef5"></div> -->
            <div class="center-align" style="justify-content: space-between; padding: 14px 0">
              <div style="font-size: 13px; color: #303133; line-height: 18px">置顶会话</div>

              <!-- <img
                src="@/assets/image/arrowRightGray.png"
                style="cursor: pointer; width: 26px; height: 26px"
              /> -->
              <div style="position: relative">
                <el-switch v-model="groupTop" />
                <div
                  @click="handleGroupTop"
                  style="
                    position: absolute;
                    top: 0;
                    right: 0;
                    width: 100%;
                    height: 100%;
                    z-index: 1000;
                    cursor: pointer;
                  "
                ></div>
              </div>
            </div>
            <div style="height: 1px; background: #ebeef5"></div>
            <div class="center-align" style="justify-content: space-between; padding: 14px 0">
              <div style="font-size: 13px; color: #303133; line-height: 18px">消息免打扰</div>

              <!-- <img
                src="@/assets/image/arrowRightGray.png"
                style="cursor: pointer; width: 26px; height: 26px"
              /> -->
              <div style="position: relative">
                <el-switch v-model="groupNoDisturb" />
                <div
                  @click="handleGroupNoDisturb"
                  style="
                    position: absolute;
                    top: 0;
                    right: 0;
                    width: 100%;
                    height: 100%;
                    z-index: 1000;
                    cursor: pointer;
                  "
                ></div>
              </div>
            </div>
            <div style="height: 1px; background: #ebeef5"></div>
            <!-- <div class="center-align" style="justify-content: space-between; padding: 20px 0">
              <div style="font-size: 18px; color: #303133; line-height: 25px">AI实时翻译</div>

              <img
                src="@/assets/image/arrowRightGray.png"
                style="cursor: pointer; width: 26px; height: 26px"
              />
            </div> -->
          </div>

          <div
            style="
              margin-top: 15px;
              padding-left: 14px;
              font-size: 11px;
              color: #909399;
              line-height: 18px;
            "
          >
            群管理
          </div>
          <div style="margin-top: 8px; background: #ffffff; border-radius: 10px; padding: 0 14px">
            <div class="center-align" style="justify-content: space-between; padding: 14px 0">
              <div style="font-size: 13px; color: #303133; line-height: 18px">群类型</div>

              <div style="font-size: 13px; color: #c0c4cc; line-height: 18px">内部群</div>
            </div>
          </div>

          <div style="margin-top: 8px; background: #ffffff; border-radius: 10px; padding: 0 14px">
            <!-- <div class="center-align" style="justify-content: space-between; padding: 14px 0">
              <div style="font-size: 18px; color: #ff5757; line-height: 25px; cursor: pointer"
                >清空聊天记录</div
              >
            </div>
            <div style="height: 1px; background: #ebeef5"></div> -->
            <div v-if="!isGroupOwner" class="center-align" style="justify-content: space-between; padding: 14px 0">
              <div
                @click="handleExitGroup()"
                style="font-size: 13px; color: #ff5757; line-height: 18px; cursor: pointer"
              >退出群聊
              </div>
            </div>
            <div style="height: 1px; background: #ebeef5"></div>
            <div v-if="isGroupOwner" class="center-align" style="justify-content: space-between; padding: 14px 0">
              <div
                @click="disbandedGroup()"
                style="font-size: 13px; color: #ff5757; line-height: 18px; cursor: pointer"
              >解散群组
              </div>
            </div>
          </div>

        </el-scrollbar>

      </div>
      <!-- 群成员列表 -->
      <div v-if="groupDrawerType == 2" style="height: 100%">
        <div class="center-align" style="justify-content: space-between; height: 30px">
          <div class="center-align" style="cursor: pointer" @click="groupDrawerTypeChange(1)">
            <img
              src="@/assets/image/arrowLeft.png"
              style="width: 15px; height: 15px; margin-right: 5px"
            />
            <div style="font-weight: 500; font-size: 15px; color: #303133; line-height: 21px">
              返回
            </div>
          </div>

          <img
            src="@/assets/image/close.png"
            style="cursor: pointer; width: 23px; height: 23px"
            @click="groupDrawer = false"
          />
        </div>
        <div style="height: 15px"></div>

        <el-scrollbar style="height: calc(100% - 45px)">
          <div style="padding: 0 4px">
            <div v-for="(item, index) in currentGroupUser" :key="index">
              <div class="center-align" style="padding: 13px 0; justify-content: space-between">
                <div
                  class="center-align"
                  @click="showUserInfo(item.member_id)"
                  style="cursor: pointer"
                >
                  <div style="width: 26px; height: 26px; padding-right: 9px">
                    <img
                      v-if="item.avatar"
                      :src="item.avatar"
                      style="width: 26px; height: 26px; border-radius: 4px"
                    />

                    <div
                      v-else
                      class="center-view"
                      style="
                        width: 26px;
                        height: 26px;
                        background: #3370ff;
                        border-radius: 4px;
                        font-size: 10px;
                        color: #ffffff;
                        line-height: 13px;
                      "
                    >
                      {{ item.alias?.length > 2 ? item.alias.substr(-2) : item.alias }}
                    </div>
                  </div>

                  <div
                    class="textover"
                    style="max-width: 300px; font-size: 13px; color: #303133; line-height: 18px"
                  >
                    {{ item.alias }}
                  </div>
                </div>

                <div
                  v-if="item.type == 2"
                  style="font-size: 13px; color: #3370ff; line-height: 18px"
                >
                  群主
                </div>
              </div>

              <div style="height: 1px; background: #ebeef5"></div>
            </div>
          </div>
        </el-scrollbar>
      </div>
    </el-drawer>

    <!-- 入职签约二维码 -->
    <el-dialog v-model="signQrCodeVisible" title="签约二维码" width="500">
      <div class="center-view">
        <img style="width: 300px; height: 300px" :src="signQrCodeUrl" />
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="signQrCodeVisible = false">关闭</el-button>
        </div>
      </template>
    </el-dialog>


    <!--    @群友的弹窗-->
    <el-dialog
      v-model="atDialogVisible"
      title=""
      width="200px"
      class="at-dialog"
      :style="atDialogStyle"
      :show-close="false"
    >
      <template #header>
        <!-- 这里以后可以做一个搜索框 -->
      </template>
      <div class="dialog-header">
        <h4>群成员</h4>
        <el-button type="primary" link @click="atToggleMode" v-if="!atIsMultipleMode">
          {{ '多选' }}
        </el-button>
        <el-button type="primary" link @click="atHandleMultipleSelectedMembers" v-else>
          {{ '完成' }}
        </el-button>
      </div>

      <div class="member-list">
        <div v-for="member in atGroupMembers" :key="member.value" class="member-item">
          <template v-if="atIsMultipleMode">
            <el-checkbox v-model="atSelectedMembers" :label="member.value" :key="member.value">
              <div class="member-content">
                <el-avatar
                  :size="32"
                  :src="member.avatar"
                  shape="square"
                  :style="{ backgroundColor: !member.avatar ? '#3470FF' : '' }"
                >
                  {{
                    member.value === 'all_members' ? '@' : (member.label?.length > 2 ? member.label.substr(-2) : member.label)
                  }}
                </el-avatar>
                <span class="member-name">{{ member.label }}</span>
              </div>
            </el-checkbox>
          </template>
          <template v-else>
            <div class="member-content" @click="atHandleConfirm(member)">
              <el-avatar
                :size="32"
                :src="member.avatar"
                shape="square"
                :style="{ backgroundColor: !member.avatar ? '#3470FF' : '' }"
              >
                {{
                  member.value === 'all_members' ? '@' : (member.label?.length > 2 ? member.label.substr(-2) : member.label)
                }}
              </el-avatar>
              <span class="member-name">{{ member.label }}</span>
            </div>
          </template>
        </div>
      </div>
    </el-dialog>
    <!-- @群友的弹窗end -->
    <!-- 引用展示图片预览 -->
    <el-image-viewer
      v-if="showImagePreview"
      :url-list="[imageUrl]"
      :initial-index="0"
      @close="closeViewer" />
    <!-- 引入 SelectUser 组件 -->
    <SelectUser ref="selectUserRef" @confirm="handleUserConfirm" />
  </div>
</template>
<script type="ts" setup>
import { Close } from '@element-plus/icons-vue'

defineOptions({ name: 'messageModules' })
import { useAppStore } from '@/store/modules/app'
import * as messageApi from '@/api/message/index'
import { useWFlowStore } from '@/store/modules/wflow'
import InstancePreview from '@/views/wflow/workspace/approval/ProcessInstancePreview.vue'
import NewApplyDrawer from './drawer/NewApplyDrawer.vue'
import { useRoute, useRouter } from 'vue-router'
import { useWebSocket } from '@vueuse/core'
import { useOriginUrl } from '@/utils/useOriginUrl'
import Personal from '@/views/hrManage/userRoster/personal.vue'
import { useMyStore } from '@/store/modules/jump'
import readMember from './readMember.vue'
import leftMenu from './ContextMenu.vue'
import msgMenu from './msgMenu.vue'
import UserDialog from '@/views/system/orgstructure/userDialog.vue'
import readFile from '@/views/system/notify/list/readFile.vue'
import * as LoginApi from '@/api/login'
import TEmoji from './emoji.vue'
import { useUserStore } from '@/store/modules/user'
import groupPickerView from '@/components/common/group/picker.vue'
import { ElImageViewer } from 'element-plus'
import SelectUser from '@/components/SelectUser/SelectUser.vue'
import DialogComponent from './dialogComponent.vue'
import { toTime } from '@/utils/date'

const { server } = useOriginUrl()

const router = useRouter()
const route = useRoute()

/** 发起 WebSocket 连接 */
const { status, data } = useWebSocket(server.value, {
  autoReconnect: true,
  // heartbeat: true,
  heartbeat: {
    message: 'ping', // 发送的心跳消息内容
    interval: 30000,  // 每 30 秒发送一次心跳消息
    pongTimeout: 5000 // 等待服务器响应的超时时间为 5 秒
  }
})

//事件
const appStore = useAppStore()

// console.log('连接', server.value, moment().format('YYYY-MM-DD HH:mm:ss'))

const targetElement = ref(null) // 获取目标元素的引用
const isVisible = ref(false) // 响应式变量，存储元素的可见状态

const selectUserRef = ref(null) // 初始为 null，不是组件
const selectedUsers = ref([]) // 用于存储选中的用户数据

// 创建 IntersectionObserver 实例
const observer = new IntersectionObserver((entries) => {
  entries.forEach((entry) => {
    isVisible.value = entry.isIntersecting // 更新元素的可见状态
  })
})

watchEffect(() => {
  // console.log('watchEffect', moment().format('YYYY-MM-DD HH:mm:ss'))
  // console.log("isVisible.value=",isVisible.value)
  // console.log('watchEffect data.value', data.value)
  if (!data.value) {
    return
  }
  try {
    if (data.value === 'pong') {
      return
    }
    // 2.1 解析 type 消息类型
    const jsonMessage = JSON.parse(data.value)
    const type = jsonMessage.type
    const content = JSON.parse(jsonMessage.content)
    // console.log(type)
    console.log('message-type:', type, 'content:', content)
    if (!type) {
      message.error('未知的消息类型：' + data.value)
      return
    }

    // 发送 接收 并且不为91正在输入中状态
    if ((type === 'im-push-send' || type === 'im-push-receive') && content.payload.type != 91) {
      let targetType
      let target
      let groupInfo
      let isPush = true
      let readStatus
      let matchItem
      let unreadMessageTotal
      if (type === 'im-push-send') {
        matchItem = leftList.value.filter(
          (item) =>
            (item.targetType == 1 &&
              content.targetType == 1 &&
              item.target.userId == content.conv.target) ||
            (item.targetType == 2 &&
              content.targetType == 2 &&
              item.groupInfo.target_id == content.conv.target)
        )
        // console.log(matchItem, 'matchItem')
        leftList.value = leftList.value.filter(
          (item) =>
            !(
              (item.targetType == 1 &&
                content.targetType == 1 &&
                item.target.userId == content.conv.target) ||
              (item.targetType == 2 &&
                content.targetType == 2 &&
                item.groupInfo.target_id == content.conv.target)
            )
        )
        if (Object.keys(rowInfo.value).length > 0) {
          const targetTypeMatches = rowInfo.value.targetType == content.targetType
          const userIdMatches =
            rowInfo.value.targetType == 1 && rowInfo.value.target.userId == content.conv.target
          // const groupIdMatches = rowInfo.value.targetType == 2 && rowInfo.value.groupInfo.target_id == content.conv.target
          // 发送群组后会受到send和receive 所以只取receive 避免重复push
          if (targetTypeMatches && userIdMatches) {
            // getSendRead(content.sender, content.conv.target, 0)
            pushDetailList(content, type)
            // activeTab.value = 0
            isPush = false
          }
        }
      }

      let isSamePerson = false
      if (type === 'im-push-receive') {
        console.log('收到消息是：', content)
        matchItem = leftList.value.filter(
          (item) =>
            (item.targetType == 1 &&
              content.targetType == 1 &&
              item.target.userId == content.sender) ||
            (item.targetType == 2 &&
              content.targetType == 2 &&
              item.groupInfo.target_id == content.conv.target)
        )
        // console.log(matchItem, 'matchItem')
        let cv = rowInfo.value

        leftList.value = leftList.value.filter(
          (item) =>
            !((item.targetType == 1 && content.targetType == 1 && item.target.userId == content.sender) ||
              (item.targetType == 2 && content.targetType == 2 && item.groupInfo.target_id == content.conv.target)
            )
        )
        if (Object.keys(rowInfo.value).length > 0) {
          const targetTypeMatches = rowInfo.value.targetType == content.targetType
          const userIdMatches =
            rowInfo.value.targetType == 1 &&
            rowInfo.value.target.userId == content.sender
          const groupIdMatches = rowInfo.value.targetType == 2 &&
            rowInfo.value.groupInfo.target_id == content.conv.target
          if (targetTypeMatches && (userIdMatches || groupIdMatches)) {
            console.log('通过接收参数，来调用getSendRead')
            getSendRead(content.sender, content.conv.target, 1)
            pushDetailList(content, type)
            // activeTab.value = 0
            isPush = false
            isSamePerson = true
          }
        }
        // 播放声音提示
        // playNotificationSound();
      }
      // console.log(leftList.value, 'unshift前的数组', 'unshift前的长度' + leftList.value.length)
      targetType = content.targetType
      target = content.targetType === 1 && content.targetInfo ? content.targetInfo : null
      groupInfo = content.targetType === 2 && content.targetInfo ? content.targetInfo : null
      readStatus = type == 'im-push-send' && content.targetType === 1 ? 0 : null

      if (rowInfo.value?.groupInfo?.extra && groupInfo) {
        groupInfo.extra = rowInfo.value.groupInfo?.extra
        groupInfo.member_count = rowInfo.value.groupInfo.member_count
      }
      const insetArr = {
        targetType,
        target,
        groupInfo,
        lastMessage: {
          payload: {
            searchableContent: content.payload.searchableContent
          },
          sender: content.sender,
          readStatus: readStatus,
          unreadMessageTotal: content.userUnreadCount
        },
        timestamp: new Date().getTime(),
        isPush: isPush,
        hidden: false
      }
      insertNode(matchItem, insetArr, type)
      if (type === 'im-push-receive') {
        pcRemind(matchItem, insetArr, isSamePerson)
        //新增消息总角标缓存数量
        const loginUser = localStorage.getItem('loginUser')
        console.info('loginUser==>', loginUser)
        if (loginUser) {
          try {
            const myId = parseInt(JSON.parse(loginUser).id)
            // console.log("增消息总角标缓存数量 incrementAllUnReadCount")
            // console.log("myId==>", myId)
            // console.log("content.sender==>", content.sender)
            if (Number(content.sender) != myId) {
              appStore.incrementAllUnReadCount(1)
            } else {
              // appStore.incrementAllUnReadCount(1)
            }
          } catch (error) {
            console.error('解析 loginUser 失败:', error)
            message.error('解析 loginUser 失败')
          }
        }
      }
      // console.log(leftList.value, 'unshift结束的数组', 'unshift结束的长度' + leftList.value.length)
    }
    // 接收 已读人员 ws推送
    if (type === 'im-push-read') {
      if (Object.keys(rowInfo.value).length > 0) {
        const myId = useWFlowStore().loginUser.id
        // 单聊
        if (!content.isGroup) {
          console.log('收到已读消息变更的推送  单聊模式')
          getSendRead(content.sender, content.target, 0)
          // 若消息是本人发送的
          if (content.target == myId) {
            // 列表塞已读状态
            const obj = leftList.value.find((item) => item.lastMessage.sender == myId)
            if (obj && obj.lastMessage.readStatus == 0) {
              obj.lastMessage.readStatus = 1
            }
          }
          // 若接收消息的已读人员在当前右侧聊天框中
          if (rowInfo.value.targetType == 1 && content.sender == rowInfo.value.target.userId) {
            // 详情消息列表全部遍历改为已读状态
            detailList.value.forEach((item) => {
              item.messageData.readStatus = 1
            })
          }
        }
        // 群聊
        if (content.isGroup) {
          if (
            rowInfo.value.targetType == 2 &&
            content.target == rowInfo.value.groupInfo.target_id
          ) {
            console.log('收到已读消息变更的推送  群聊模式')
            getSendRead(content.sender, content.target, 1)
            const valuesToRemove = new Set(content.sender.split(','))
            detailList.value.forEach((obj) => {
              if (obj.messageData.unreadUser) {
                let cArray = obj.messageData.unreadUser.split(',')
                cArray = cArray.filter((item) => !valuesToRemove.has(item))
                obj.messageData.unreadUser = cArray.join(',')
              }
              if (!obj.messageData.readUser) {
                obj.messageData.readUser += content.sender
              } else {
                if (!obj.messageData.readUser.includes(content.sender)) {
                  obj.messageData.readUser += ',' + content.sender
                }
              }
            })
          }
        }
      }
    }
  } catch (error) {
    message.error('处理消息发生异常：' + data.value)
    console.error(error)
  }
})

// 使用计算属性来获取每个item的userUnreadCount未读数量
const getUnreadCount = (item) => {
  //console.info('未读数量=>', item.lastMessage.unreadMessageTotal)
  if (item.lastMessage.unreadMessageTotal == 0 || item.mute) {
    return ''
  }
  return item.lastMessage.unreadMessageTotal
}

//群头像九宫格拼接
const getGroupMembers = (item) => {
  if (!item.groupInfo.extra) return []
  // 假设每个群组有一个成员列表
  const members = JSON.parse(item.groupInfo.extra)?.groupMemberList || []
  return members
}, getAvatarSize = (memberCount) => {
  // 根据成员数量计算小头像的大小
  if (memberCount < 1) return 42
  if (memberCount <= 4) return 20
  return 14 // 多于5人/1人 的情况下再小一些
}
//头像动态内联样式
const groupAvatarStyle = (memberCount, memberIndex) => {
  const isGroupOwner = memberCount % 2 !== 0 && memberIndex === 0
  let avatarSize = 14
  let gap = 1
  let rowNum = 3
  let leftNum = 3
  let fontSize = 10
  let lineHeight = 1
  let top, left

  // 根据群员数量处理不同的拼接样式
  switch (memberCount) {
    case 1:
      avatarSize = 21
      lineHeight = 2;
      [top, left] = getPositionForOne()
      break
    case 2:
      avatarSize = 21
      lineHeight = 2;
      [top, left] = getPositionForTwo(memberIndex)
      break
    case 3:
      avatarSize = 21
      lineHeight = 2;
      [top, left] = getPositionForThree(memberIndex, isGroupOwner)
      break
    case 4:
      avatarSize = 21
      rowNum = 2
      leftNum = 2
      lineHeight = 2;
      [top, left] = getPositionForGrid(memberIndex, rowNum, leftNum, false)
      break
    case 5:
      fontSize = 11
      lineHeight = 1.2
      leftNum = 2;
      [top, left] = isGroupOwner ?
        ['0px', `${(42 - avatarSize) / 2}px`] :
        getPositionForFive(memberIndex, rowNum, leftNum)
      break
    case 6:
      fontSize = 11
      lineHeight = 1.2;
      [top, left] = getPositionForGrid(memberIndex, rowNum, leftNum, true)
      break
    case 7:
      fontSize = 11
      lineHeight = 1.2
      gap = 0;
      [top, left] = isGroupOwner ?
        ['0px', `${(42 - avatarSize) / 2}px`] :
        getPositionForSeven(memberIndex - 1, rowNum, leftNum)
      break
    default: // 超过8个人数
      fontSize = 11
      lineHeight = 1.2;
      [top, left] = getPositionForGrid(memberIndex, rowNum, leftNum, false)
  }
  return {
    top,
    left,
    position: 'absolute',
    width: `${avatarSize}px`,
    height: `${avatarSize}px`,
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    fontSize: `${fontSize}px`,
    lineHeight: `${lineHeight}`
  }

  function getPositionForOne() {
    return [`${(42 - avatarSize) / 2}px`, `${(42 - avatarSize) / 2}px`]
  }

  function getPositionForTwo(index) {
    return [`${(42 - avatarSize) / 2}px`, `${index * (avatarSize + gap)}px`]
  }

  function getPositionForThree(index, isOwner) {
    if (isOwner) return ['0px', `${(42 - avatarSize) / 2}px`]
    return [`${avatarSize}px`, `${(index - 1) * (avatarSize + gap)}px`]
  }

  function getPositionForFive(index, rows, cols) {
    let row = Math.floor(index / rows) + 1
    let col = index % cols
    return [`${row * (avatarSize + gap)}px`, `${42 / 2 - col * (avatarSize + gap)}px`]
  }

  function getPositionForSeven(index, rows, cols) {
    let row = Math.floor(index / rows) + 1
    let col = index % cols
    return [`${row * (avatarSize + gap)}px`, `${col * (avatarSize + gap)}px`]
  }

  //reverseY 是否上下居中
  function getPositionForGrid(index, rows, cols, reverseY) {
    let row = Math.floor(index / rows)
    let col = index % cols
    let y = reverseY ? `${42 / 2 - row * (avatarSize + gap)}px` : `${row * (avatarSize + gap)}px`
    return [y, `${col * (avatarSize + gap)}px`]
  }
}

// 左侧列表右击菜单事件
const leftMenuRef = ref(null)
const showContextMenu = (event) => {
  if (leftMenuRef.value) {
    leftMenuRef.value.showMenu(event)
  }
}
const getSetTop = async (type, row, index) => {
  // console.log(row, type, index, 'getSetTop')
  if (type == '置顶会话') {
    const res = await messageApi.listTopSet({
      userId: row.targetType == 1 ? row.target.userId : row.groupInfo.target_id,
      convType: row.targetType == 1 ? 0 : 1
    })
    if (res.code == 0) {
      row.top = true
      const filteredArray = leftList.value.filter((_, i) => i != index)
      leftList.value = [row, ...filteredArray]
      activeTab.value = 0

      if (rowEqualityJudgment(row, rowInfo.value)) {
        rowInfo.value = row
      }
    }
  }
  if (type == '取消置顶') {
    const res = await messageApi.listTopSet({
      userId: row.targetType == 1 ? row.target.userId : row.groupInfo.target_id,
      convType: row.targetType == 1 ? 0 : 1
    })
    if (res.code == 0) {
      row.top = false
      leftList.value.splice(index, 1)
      elseInsert(row, '取消置顶')
    }
  }
  if (type == '消息免打扰' || type == '取消免打扰') {
    const res = await messageApi.listMuteSet({
      userId: row.targetType == 1 ? row.target.userId : row.groupInfo.target_id,
      convType: row.targetType == 1 ? 0 : 1
    })
    if (res.code == 0) {
      leftList.value[index].mute = type == '消息免打扰' ? true : false
    }
  }
  if (type == '移除会话') {
    leftList.value[index].hidden = true
    const arr = leftList.value.filter((item) => !item.hidden)
    const isValidRowInfo = Object.keys(rowInfo.value).length > 0
    const isTargetTypeMatch =
      (row.targetType == 1 &&
        rowInfo.value.targetType == 1 &&
        row.target.userId == rowInfo.value.target.userId) ||
      (row.targetType == 2 &&
        rowInfo.value.targetType == 2 &&
        row.groupInfo.target_id == rowInfo.value.groupInfo.target_id)
    if (isValidRowInfo && isTargetTypeMatch) {
      const hasNext = index < arr.length
      if (arr.length < 1) {
        detailList.value = []
        rowInfo.value = {}
      } else if (hasNext) {
        const nextElement = findNextValidElement(index + 1, 'next')
        activateTab(nextElement.index, nextElement.item)
      } else {
        const nextElement = findNextValidElement(index - 1, 'back')
        activateTab(nextElement.index, nextElement.item)
      }
    }
  }
}
const findNextValidElement = (startIndex, direction) => {
  let i = startIndex
  if (direction === 'next') {
    while (i < leftList.value.length) {
      if (!leftList.value[i].hidden) {
        return { item: leftList.value[i], index: i }
      }
      i++
    }
  } else if (direction === 'back') {
    while (i >= 0) {
      if (!leftList.value[i].hidden) {
        return { item: leftList.value[i], index: i }
      }
      i--
    }
  }
  return { item: null, index: -1 }
}

// 右侧消息右击菜单事件
const msgMenuRef = ref(null)
const showMsgMenu = (event) => {
  if (msgMenuRef.value) {
    msgMenuRef.value.showMenu(event)
  }
}
const handleMsg = async (type, row, index) => {
  console.log(row, type, index, 'handleMsg')
  if (type == '撤回') {
    const res = await messageApi.detailMsgRecall({
      userId: row.senderInfo.userId,
      msgId: row.messageData.messageId
    })
    if (res.code == 0) {
      detailList.value.splice(index, 1)
      row.messageData.payload.type = 80
      detailList.value.splice(index, 0, row)
    }
  }
  if (type == '引用') {
    //判断消息本身是不是引用消息
    const searchableContent = row.messageData.payload.searchableContent
    // 创建一个临时 DOM 元素来解析 HTML 字符串
    const tempDiv = document.createElement('div')
    tempDiv.innerHTML = searchableContent

    // 检查是否存在 data-msgType="quote"
    const quoteDiv = tempDiv.querySelector('[data-msgType="quote"]')

    if (quoteDiv) {
      // 查找 <span> 标签
      const spanElement = quoteDiv.querySelector('span')

      // 提取 <span> 标签的文本内容
      const spanText = spanElement ? spanElement.textContent : null

      if (spanText) {
        // 将提取的文本内容赋值给 searchableContent
        row.messageData.payload.searchableContent = spanText
      }
    }
    const u = row.messageData.messageId
    const i = row.senderInfo.userId
    const n = row.senderInfo.displayName
    const d = row.messageData.payload.searchableContent
    await createQuoteMsg(u, i, n, d)
  }

  if (type == '转发') {
    selectedMsg.value = []
    selectedMsg.value.push(row)
    forwardModel.value = 'one'
    selectUserRef.value.open()
  }

  if (type == '多选') {
    isMultiSelectMsg.value = true
    selectedMsg.value = []
  }
}


const quoteMsg = ref({})
const htmlQuoteMsg = ref('')
const htmlQuoteMsgSendValue = ref('')
const htmlQuoteMsgQuoteValue = ref('')
const createQuoteMsg = async (u, i, n, d) => {
  quoteMsg.value = {
    u, i, n, d
  }

  htmlQuoteMsgQuoteValue.value = `${n}：${d}`
  await focusMessageArea()
}

//构建聊天记录框中需要的引用消息样式
const createQuoteHtmlMsg = () => {
  htmlQuoteMsg.value = `<div style="display: flex; flex-direction: column; align-items: flex-start; padding: 2px 4px; border-radius: 4px; max-width: 100%; line-height: 1; gap: 4px; margin: 4px 0;" data-msgType="quote" data-originMsgValue="${htmlQuoteMsgSendValue.value}">
  <span>${htmlQuoteMsgSendValue.value}</span>
  <input
    type="text"
    readonly
    style="border: none; background: transparent; border-left: 2px solid #4CAF50; padding-left: 6px; white-space: pre-wrap; overflow: hidden; padding-top: 1px; padding-bottom: 1px; outline: none; width: 100%;"
    value="${htmlQuoteMsgQuoteValue.value}"
  >
</div>`
}
//清空引用消息
const resetQuoteMsg = () => {
  quoteMsg.value = {}
  //光标定位到消息输入框
  focusMessageArea()
}
// 监听折叠面板
const message = useMessage()
const collapse = computed(() => appStore.getCollapse)
const isCollapse = computed(() => appStore.getCollapse)
watch(
  () => collapse.value,
  (newPath, oldPath) => {
    isCollapse.value = newPath
  }
)
// 监听浏览器宽度
const windowWidth = ref(window.innerWidth)
const handleResize = () => {
  windowWidth.value = window.innerWidth
}

const activeTab = ref(-1) //消息列表默认选中值
const detailList = ref([]) //消息列表默认选中值的详情数据
const rowInfo = ref({})
const scrollTarget = ref()
const isRobot = ref(false) //true 不是机器人
const readList = ref([]) //已读人员列表
const userCard = ref({}) //详情对方名片数据

//消息多选模式
const isMultiSelectMsg = ref(false)
//被选中的消息
const selectedMsg = ref([])

const forwardModel = ref('one') //转发弹窗
// 处理消息选择
const handleSelectMsg = (checked, item) => {
  if (checked) {
    if (!selectedMsg.value.includes(item)) {
      selectedMsg.value.push(item)
    }
  } else {
    const index = selectedMsg.value.findIndex(msg => msg.messageData.messageId === item.messageData.messageId)
    if (index !== -1) {
      selectedMsg.value.splice(index, 1)
    }
  }
}

// 转发选中的消息
const forwardSelectedMessages = (model) => {
  //转发消息的模式：逐条还是合并
  forwardModel.value = model
  // 打开联系人弹窗，然后将selectedMsg中的消息转发给选中的联系人
  selectUserRef.value.open()
}

// 执行消息转发
const handleUserConfirm = (users) => {
  try {
    //选择要转发的用户
    selectedUsers.value = users
    const targetId = selectedUsers.value[0].id
    if (forwardModel.value === 'one') {
      // 逐条转发
      for (const item of selectedMsg.value) {
        const message = {
          convType: 0,
          payloadType: 1,
          searchableContent: item.messageData.payload.searchableContent,
          target: targetId
        }
        try {
          messageApi.messageSend(message)
        } catch (error) {
          console.error('转发消息失败:', error, '消息:', message)
          message.error('转发消息失败，请重试')
        }
      }
    } else if (forwardModel.value === 'together') {
      // 合并转发

      let content = ''
      // 如果是群聊消息
      if (rowInfo.value.targetType == 2) {
        content = '群聊的聊天记录'
      } else {
        //收者用户名
        const targetName = rowInfo.value.target.displayName
        // 接发送者用户名
        const loginUser = localStorage.getItem('loginUser')
        let senderName = JSON.parse(loginUser).name
        //拼接content字段
        content = `${senderName}和${targetName}的聊天记录`
      }
      // 展示的消息内容
      let showContent = ''
      let ms = []
      for (const item of selectedMsg.value) {
        //取出最前面的最多三条item.messageData.payload.searchableContent内容，拼接成showContent
        if (ms.length < 3) {
          // 只取前三条消息
          showContent += item.messageData.payload.searchableContent + ' '
        }
        // 构建消息结构
        const message = {
          'uid': item.messageData.messageId,
          'type': item.messageData.conv.type,
          'target': item.messageData.conv.target,
          'line': item.messageData.conv.line,
          'from': item.senderInfo.userId,
          'direction': 0,
          'cc': item.messageData.payload.searchableContent,
          'csc': item.messageData.payload.searchableContent,
          'serverTime': item.messageData.timestamp,
          'ctype': item.messageData.payload.type,
          'mru': item.messageData.payload.remoteMediaUrl,
          'status': 0,
          'mt': 0
        }
        ms.push(message)
      }
      const msObject = {
        'ms': ms
      }
      const jsonString = JSON.stringify(msObject)
      const uint8Array = new TextEncoder().encode(jsonString)
      const base64String = btoa(String.fromCharCode.apply(null, uint8Array))
      // 构建引用消息结构
      // 确保showContent不会太长，最多显示50个字符
      if (showContent.length > 50) {
        showContent = showContent.substring(0, 47) + '...'
      }

      // 如果showContent为空，设置一个默认值
      if (!showContent.trim()) {
        showContent = '聊天记录'
      }

      const data = {
        'convType': 0,
        'target': targetId,
        'payloadType': 11,
        'content': content,
        'searchableContent': showContent.trim(),
        'quoteBase': base64String
      }

      //调用发消息接口
      messageApi.messageSend(data)
    }

  } catch (error) {
    console.error(error)
  } finally {
    // 转发完成后，退出多选模式
    isMultiSelectMsg.value = false
    // 清空转发消息列表
    selectedMsg.value = []
  }
}



const activateTab = (index, item) => {
  //取消多选模式
  isMultiSelectMsg.value = false
  selectedMsg.value = []

  item.isPush = false
  activeTab.value = index
  rowInfo.value = item
  detailList.value = []
  readList.value = []
  loading.value = true
  userCard.value = {}
  if (item.targetType == 1 && item.target && item.target.type == 0) {
    getUserCard(item.target.userId)
  }
  getDetail(item)
  if (item.targetType == 2) {
    getGroupMember(item.groupInfo.target_id) //获取全部群成员
  }
  const regex = /^\d+$/ //匹配由数字组成的字符串
  isRobot.value = item.targetType == 1 && !regex.test(item.target.userId)
  //触发后 减去总消息已读数量
  appStore.decrementAllUnReadCount(item.lastMessage.unreadMessageTotal)
  //触发后 设置成已读数量
  item.lastMessage.unreadMessageTotal = 0
  //对应阅读数量为0
  getUnreadCount(item)

//选择联系人后，把光标自动定位到消息输入框
  focusMessageArea()
}
// 获取单个对话框的消息记录
const getDetail = async (item) => {
  try {
    const res = await messageApi.getTargetConvs({
      userId: useWFlowStore().loginUser.id,
      targetId: item.targetType == 1 ? item.target.userId : item.groupInfo.target_id,
      targetType: item.targetType
    })
    //判断是否存在“引用消息”
    res.data.convList.forEach((item) => {
      const content = item.messageData
      // 如果 content.payload 中存在 base64edData 字段，并且解码后的 base64Data 字段中存在 'quote' 的字段，则为引用消息
      if (content?.payload?.base64edData && isValidBase64(content.payload.base64edData)) {
        try {
          const base64Data = atob(content.payload.base64edData) // 解码 Base64
          const uint8Array = new Uint8Array(base64Data.length)
          for (let i = 0; i < base64Data.length; i++) {
            uint8Array[i] = base64Data.charCodeAt(i)
          }
          const decodedString = new TextDecoder('utf-8').decode(uint8Array)
          const parsedData = JSON.parse(decodedString) // 解析 JSON
          if (parsedData?.quote) { // 检查 quote 字段是否存在
            htmlQuoteMsgSendValue.value = content.payload.searchableContent
            htmlQuoteMsgQuoteValue.value = `${parsedData.quote.n}：${parsedData.quote.d}`
            createQuoteHtmlMsg()
            // 再把 htmlQuoteMsg.value 赋值给 content.payload.searchableContent，以便显示引用消息
            content.payload.searchableContent = htmlQuoteMsg.value
          }
        } catch (error) {
          // 忽略错误，继续执行
          console.error('Error processing message:', error)
        }
      }
    })
    detailList.value = res.data.convList
    nextTick(() => {
      if (scrollTarget.value) {
        // 使用 $el 获取组件的根 DOM 元素
        const scrollElement = scrollTarget.value.$el
        if (scrollElement && typeof scrollElement.scrollTo === 'function') {
          scrollElement.scrollTo(0, scrollElement.scrollHeight)
          setTimeout(() => {
            scrollElement.scrollTo(0, scrollElement.scrollHeight)
          }, 300)
        }
      }
    })
  } finally {
    loading.value = false
  }
}
// 消息列表遍历数据
const loading = ref(false)
const leftList = ref([])
const getList = async (needLoading = true) => {
  loading.value = needLoading
  try {
    const res = await messageApi.getUserConvs({ userId: useWFlowStore().loginUser.id })
    // targetType 列表成员类型  1：用户、机器人  2：群聊
    if (res.data) {
      leftList.value = res.data.convList
      if (Object.keys(store.jumpMessage).length > 0) {
        fromSearch(store.jumpMessage)
      }
    }
  } finally {
    loading.value = false
  }
}
const radio = ref('0') //left-top状态栏
const radioList = ref([])

// 类型1001查看详情
const processVisible = ref(false)
const selectInstance = ref('')
const isNodeId = ref('')
// 新成员申请ref
const newApplyRef = ref()
const handleDetail = (e) => {
  let row = JSON.parse(e)
  let params = new URLSearchParams(row.url.split('?')[1])
  if (row.url.includes('/#/InstancePreview?')) {
    selectInstance.value = params.get('instanceId')
    isNodeId.value = params.get('nodeId')
    processVisible.value = true
  }
  if (row.url.includes('/#/notifyRead?')) {
    router.push('/notify/list/person?id=' + params.get('id'))
  }
  if (row.title == '新成员申请') {
    newApplyRef.value.openNewApply()
  }
  if (row.label == '智能工资条') {
    message.warning('请至手机端查看工资条详情内容')
  }
  if (row.title == '实时定位申请') {
    message.warning('请至手机端处理该申请')
  }
}

//入职完善抽屉时候显示
const ruzhiDrawerVisible = ref(false)
const detailRef = ref()
//入职完善的抽屉表单
const handleRuZhi = () => {
  ruzhiDrawerVisible.value = true
}

const signQrCodeVisible = ref(false)
const signQrCodeUrl = ref('')

//入职签约点击事件
const handleSign = async (val) => {

  // signQrCodeVisible.value=true

  // signQrCodeUrl.value='https://element-plus.org/images/element-plus-logo.svg'
  // return
  signQrCodeUrl.value = ''
  val = JSON.parse(val)
  console.log('handleSign=', val)
  if (val.url) {
    const res = await messageApi.getApplicationKeyContractNewUrl({
      contractUrl: val.url
    })

    const imgRes = await LoginApi.staffApplyQrcode({ url: res.data })


    console.log('imgRes=', imgRes)
    signQrCodeUrl.value = imgRes

    signQrCodeVisible.value = true

  }


}


// 文件点击预览事件
const readFileRef = ref()
const fileOpen = (row) => {
  let obj = {
    name: row.searchableContent,
    url: row.previewMediaUrl,
    downloadUrl: row.remoteMediaUrl
  }
  readFileRef.value.open(obj, true, '1')
}

// 插入数据时的处理 处理部分项为置顶时
const insertNode = (matchItem, insetArr, type) => {
  // console.log(matchItem, insetArr)
  // 在每个符合条件的 item 上执行
  if (matchItem.length > 0) {
    matchItem.forEach((item) => {
      if (item.top || item.mute) {
        const obj = { ...insetArr }
        if (item.top) obj.top = item.top
        if (item.mute) obj.mute = item.mute
        obj.hidden = false
        leftList.value.unshift(obj)
      } else {
        elseInsert(insetArr, type)
      }
    })
  } else {
    elseInsert(insetArr, type)
  }
}

const elseInsert = (item, type) => {
  item.hidden = false
  const haveTop = leftList.value.filter((item) => item.top)
  if (haveTop.length > 0) {
    const firstThreeItems = leftList.value.slice(0, haveTop.length)
    const remainingItems = leftList.value.slice(haveTop.length)
    leftList.value = [...firstThreeItems, item, ...remainingItems]
    if (type == 'fromSearch-else') {
      activateTab(haveTop.length, item)
    }
    if (type == 'im-push-send') {
      activeTab.value = haveTop.length
    }
    if (type == 'im-push-receive') {
      if (Object.keys(rowInfo.value).length > 0) {
        let a = 0
        leftList.value.forEach((item, index) => {
          if (item.targetType == 1 && rowInfo.value.targetType == 1) {
            if (rowInfo.value.target.userId == item.target.userId) {
              a = index
            }
          }
          if (item.targetType == 2 && rowInfo.value.targetType == 2) {
            if (rowInfo.value.groupInfo.target_id == item.groupInfo.target_id) {
              a = index
            }
          }
        })
        activeTab.value = a
      }
    }
  } else {
    leftList.value.unshift(item)
    if (type == 'fromSearch-else') {
      activateTab(0, item)
    }
    if (type == 'im-push-send') {
      activeTab.value = 0
    }
    if (type == 'im-push-receive') {
      if (Object.keys(rowInfo.value).length > 0) {
        let a = 0
        leftList.value.forEach((item, index) => {
          if (item.targetType == 1 && rowInfo.value.targetType == 1) {
            if (rowInfo.value.target.userId == item.target.userId) {
              a = index
            }
          }
          if (item.targetType == 2 && rowInfo.value.targetType == 2) {
            if (rowInfo.value.groupInfo.target_id == item.groupInfo.target_id) {
              a = index
            }
          }
        })
        activeTab.value = a
      }
    }
  }

  if (type == '取消置顶') {
    activeTab.value = haveTop.length
    if (rowEqualityJudgment(item, rowInfo.value)) {
      rowInfo.value = item
    }
  }
}

// pc消息推送
const pcRemind = (matchItem, insetArr, samePerson) => {
  let shouldPush = matchItem.length === 0 || matchItem.some((item) => !item.mute) //不是免打扰
  if (shouldPush) {
    pushMsgPc(samePerson)
  }
}
// pc 推送系统提醒
const pushMsgPc = (samePerson) => {
  // samePerson=true 当前聊天框接收新消息
}

// 已读
const getSendRead = async (send, target, type) => {
  try {
    if (isVisible.value == true) {
      await messageApi.sendRead({
        sender: useWFlowStore().loginUser.id,
        targetId: target,
        convType: type
      })
    } else {
      console.log('当前页面元素未展示 不调用已读', isVisible.value)
    }
  } finally {
  }
}

// push 详情消息
const pushDetailList = (content, type) => {
  //如果content.payload中存在base64edData字段，并且解码后的base64Data字段中存在'quote'的字段，则为引用消息
  if (content?.payload?.base64edData && isValidBase64(content.payload.base64edData)) {
    const base64Data = atob(content.payload.base64edData) // 解码 Base64
    const parsedData = JSON.parse(base64Data) // 解析 JSON
    if (parsedData?.quote) { // 检查 quote 字段是否存在
      htmlQuoteMsgSendValue.value = content.payload.searchableContent
      createQuoteHtmlMsg()
      //再把htmlQuoteMsg.value赋值给content.payload.searchableContent，以便显示引用消息
      content.payload.searchableContent = htmlQuoteMsg.value
    }
  }
  detailList.value.push({
    messageData: {
      timestamp: new Date().getTime(),
      payload: content.payload,
      readStatus: type == 'im-push-send' ? 0 : null,
      unreadUser: groupMember.value,
      readUser: '',
      messageId: content.messageId
    },
    senderInfo: content.senderInfo
  })
  setTimeout(() => {
    scrollTarget.value.scrollTo(0, scrollTarget.value.scrollHeight)
  }, 100)
}
// 校验base64是否正常解析
const isValidBase64 = (str) => {
  try {
    // 检查 Base64 字符串长度是否合法
    if (!str || str.length % 4 !== 0 || /[^A-Za-z0-9+/=]/.test(str)) {
      return false
    }

    // 尝试解码 Base64
    const decodedData = atob(str)

    // 检查解码后的数据是否是合法的 JSON
    JSON.parse(decodedData)
    return true
  } catch (error) {
    return false
  }
}

// 发送消息
const isFocus = ref(false)
const isDisabled = ref(false)
const inputFocus = () => {
  isFocus.value = true
}
const inputBlur = () => {
  isFocus.value = false
}

// 修改 sendMsg 函数
const sendMsg = async () => {
  isDisabled.value = true
  //把 at 消息改为纯文本发送
  if (currentMessage.value.includes('data-msgtype="at"')) {
    currentMessage.value = atMessageToText(currentMessage.value)
  }
  splitByImage(currentMessage.value)
}

//发送@消息使用的所有群成员列表
const atGroupMembers = ref([])
//被@的群成员列表
const atSelectedMembers = ref([])
//当前登录用户的 id
const myId = useUserStore().getUser.id
//选择@群成员的弹窗是否显示
const atDialogVisible = ref(false)
//是否多选模式
const atIsMultipleMode = ref(false)
//切换到多选模式
const atToggleMode = () => {
  atIsMultipleMode.value = !atIsMultipleMode.value
  atSelectedMembers.value = []
  //从atGroupMembers中删除 value=all_members的元素
  atGroupMembers.value = atGroupMembers.value.filter(member => member.value != 'all_members')
}
//处理多选
const atHandleMultipleSelectedMembers = () => {
  // 删除手动输入的@符号
  const content = messageArea.value.innerHTML
  messageArea.value.innerHTML = content.slice(0, -1) // 删除最后一个字符(@符号)

  // 批量拼接@消息
  atSelectedMembers.value.forEach((memberId) => {
    // 根据 memberId 找到对应的成员信息
    const member = atGroupMembers.value.find(m => m.value == memberId)
    if (!member) return

    const messageContent = document.createElement('span')
    // 设置 span 的属性
    messageContent.setAttribute('data-id', member.value)
    //添加一个 msg-type 属性，值为 at,以后可以根据这个属性来判断是否是@消息
    messageContent.setAttribute('data-msgType', 'at')
    messageContent.classList.add('customClass')
    messageContent.style.color = '#409EFF'
    messageContent.style.cursor = 'pointer'
    messageContent.contentEditable = 'false'

    // 设置 span 的内容
    messageContent.textContent = `@${member.label}\u00A0` // \u00A0 是不换行空格
    // 将 span 插入到消息区域
    messageArea.value.appendChild(messageContent)
  })
  currentMessage.value = messageArea.value.innerHTML
  // 设置光标位置到最后一个@提及后面
  const lastMention = messageArea.value.lastChild
  if (lastMention) {
    setCursorAfterImage(lastMention)
  }

  // 重置选中状态并关闭弹窗
  atSelectedMembers.value = []
  atDialogVisible.value = false
  atIsMultipleMode.value = false
}

//确定选择@群成员的按钮点击事件
const atHandleConfirm = (member) => {
  const member_id = member.value
  let member_name = member.label
  //如果@的是所有人，则把member.label修改为"所有人"
  if (member_id == 'all_members') {
    member_name = '所有人'
  }
  //单选模式
  if (!atIsMultipleMode.value) {
    // 删除手动输入的@符号
    const content = messageArea.value.innerHTML
    messageArea.value.innerHTML = content.slice(0, -1) // 删除最后一个字符(@符号)

    //拼接@消息
    const messageContent = document.createElement('span')
    // 设置 span 的属性
    messageContent.setAttribute('data-id', member_id)
    //添加一个 msg-type 属性，值为 at,以后可以根据这个属性来判断是否是@消息
    messageContent.setAttribute('data-msgType', 'at')
    messageContent.classList.add('customClass')
    messageContent.style.color = '#409EFF'
    messageContent.style.cursor = 'pointer'
    messageContent.contentEditable = 'false' // 设置为不可编辑

    // 设置 span 的内容
    messageContent.textContent = `@${member_name}\u00A0` // \u00A0 是不换行空格

    // 将 span 插入到消息区域
    messageArea.value.appendChild(messageContent)
    //把当前消息内容保存到currentMessage，不然只有"@xxx"的消息发不出去
    currentMessage.value = messageArea.value.innerHTML
    // 设置光标位置
    setCursorAfterImage(messageContent)
    //关闭弹窗
    atDialogVisible.value = false
  } else {
    //多选模式
  }
}

const handleCtrlEnter = async (e) => {
  if (!e.ctrlKey && e.key === 'Enter') {
    e.preventDefault() // 阻止默认的回车换行行为
    return
  }

  // 检查是否按下了 Ctrl + Enter
  if (e.ctrlKey && e.key === 'Enter') {
    e.preventDefault()
    document.execCommand('insertHTML', false, '<br><br>')
  }
  //是否输入了@符号
  if (e.key === '@' || (e.shiftKey && e.key === '2')) {
    //如果是群聊
    if (rowInfo.value.targetType == 2) {
      try {
        //通过接口获取当前群成员列表
        const groupId = rowInfo.value.groupInfo.target_id
        const response = await messageApi.getMemberListByGroupId({ groupId: groupId })
        atGroupMembers.value = response.data
          //过滤掉自己
          .filter((item) => item.member_id != myId)
          .map((item) => {
            return {
              label: item.alias,
              value: item.member_id,
              avatar: item.avatar
            }
          })
        // 在最前面添加一个数据
        atGroupMembers.value.unshift({
          label: `所有人(${atGroupMembers.value.length})`, // 动态显示数组长度
          value: 'all_members',
          avatar: ''
        })
        //显示选择@群成员的弹窗
        atDialogVisible.value = true
      } catch (error) {
        console.error('获取群成员列表失败:', error)
      }
    } else {
      console.log('不是群聊不处理@')
    }
  }

}

// 修改 keyDown 函数
const keyDown = async (e) => {
  if (e.keyCode == 13 && e.ctrlKey) {
    return
  }
  if (e.keyCode == 13) {
    e.preventDefault() // 阻止默认的回车换行行为，不然光标就消失了
    // 去除前后空白字符后判断是否为空
    if (!messageArea.value) {
      return
    }
    const trimmedContent = messageArea.value.innerText.trim()
    if (!trimmedContent) {
      await focusMessageArea() // 聚焦到消息框并将光标放在末尾
      message.warning('不允许发送空白内容')
      return
    }

    if (!isDisabled.value && currentMessage.value.trim() != '') {
      await sendMsg()
    }
  }
}

// 监听是否来源为搜索联系人 外部跳转携带id打开消息模块的
const store = useMyStore()
watch(
  () => store.jumpMessage,
  (newValue, oldValue) => {
    console.log('Value changed:', newValue, 'from', oldValue)
    if (Object.keys(newValue).length > 0) {
      fromSearch(newValue)
    }
  }
)
const fromSearch = (c) => {
  const obj = JSON.parse(JSON.stringify(c))
  console.log(obj, 'obj')
  // 查找是否已存在
  const condition = (item) => item.target && item.target.userId == obj.id
  const index = leftList.value.findIndex(condition)
  if (index !== -1) {
    const [item] = leftList.value.splice(index, 1)
    item.hidden = false
    if (item.top) {
      leftList.value.unshift(item)
      activateTab(0, item)
    } else {
      elseInsert(item, 'fromSearch-else')
    }
  } else {
    let newObj = {
      targetType: 1,
      target: {
        displayName: obj.name ? obj.name : obj.nickname,
        portrait: obj.avatar ? obj.avatar : '',
        userId: obj.id
      },
      groupInfo: {},
      lastMessage: {
        payload: {
          searchableContent: ''
        }
      },
      timestamp: new Date().getTime()
    }
    elseInsert(newObj, 'fromSearch-else')
  }
  useMyStore().updateValue({}) //清空携带来的store值
}

// 获取targetType2群聊 已读未读人员弹窗名单
const readRef = ref()
const handleUnRead = (item) => {
  readRef.value.open(item)
}
// 获取群成员名单
const groupMember = ref('')
const getGroupMember = async (id) => {
  try {
    const res = await messageApi.groupMembers({ groupId: id })
    const userId = useWFlowStore().loginUser.id
    const arr = res.data.split(',')
    const newArr = arr.filter((item) => item != userId)
    groupMember.value = newArr.join(',')
    // groupMember.value = res.data.split(",").filter(item => item != useWFlowStore().loginUser.id).reduce((acc, num, index) => {
    //   return acc + num + (index < arr.length - 1 ? ',' : '');
    // }, '');
  } catch (e) {
  }
}

// 监听是否外部跳转进消息模块的
watch(
  () => store.jumpMessage,
  (newValue, oldValue) => {
    console.log('Value changed:', newValue, 'from', oldValue)
    if (newValue && Object.keys(rowInfo.value).length > 0) {
      nextTick(() => {
        if (scrollTarget.value) {
          setTimeout(() => {
            scrollTarget.value.scrollTo(0, scrollTarget.value.scrollHeight)
          }, 100)
        }
      })
    }
  }
)
watch(
  () => route.path,
  (newPath) => {
    if (newPath == '/index' && Object.keys(rowInfo.value).length > 0) {
      nextTick(() => {
        if (scrollTarget.value) {
          setTimeout(() => {
            scrollTarget.value.scrollTo(0, scrollTarget.value.scrollHeight)
          }, 100)
        }
      })
    }
  }
)

// 上传图片/附件
const maxFileLength = ref(0)
const endResult = ref([])
const uploadRef = ref()
const uploadRef2 = ref()
const handleImg = (file, fileList) => {
  handleSubmit(fileList, 'img')
}
const handleFile = (file, fileList) => {
  console.log(file, fileList)
  handleSubmit(fileList, 'file')
}
const handleSubmit = (fileList, type) => {
  maxFileLength.value = 0
  let length = fileList.length
  maxFileLength.value = Math.max(length, maxFileLength.value)
  if (maxFileLength.value !== length) {
    return
  }
  setTimeout(() => {
    if (maxFileLength.value !== length) {
      return
    }
    const uploadQueue = []
    loading.value = true
    fileList.forEach((file) => {
      const formData = new FormData()
      formData.append('file', file.raw)
      formData.append('isImg', type == 'img' ? true : false)
      uploadQueue.push(messageApi.uploadFile(formData))
    })
    Promise.all(uploadQueue)
      .then(async (res) => {
        endResult.value = []
        let isFault = false
        res.forEach((item) => {
          if (item.code === 0) {
            submitAll(item.data, type)
          } else {
            isFault = true
          }
        })
        if (isFault) {
          loading.value = false
          ElMessage.error('上传失败，请重新上传')
          return
        }
        uploadRef.value.clearFiles()
        uploadRef2.value.clearFiles()
      })
      .catch(() => {
        loading.value = false
        ElMessage.error('上传失败，请重新上传')
      })
  }, 0)
}
const submitAll = async (item, type) => {
  try {

    let data = {
      convType: rowInfo.value.targetType == 2 ? 1 : 0,
      target:
        rowInfo.value.targetType == 2
          ? rowInfo.value.groupInfo.target_id
          : rowInfo.value.target.userId,
      payloadType: type == 'img' ? 3 : 5, //消息类型
      mediaType: type == 'img' ? 1 : 4, //媒体类型
      searchableContent: type == 'img' ? '[图片]' : type == 'file' ? item.name : '',
      remoteMediaUrl: item.url,
      content: item.size
    }
    await messageApi.messageSend(data)
  } finally {
    loading.value = false
  }
}

// 查看用户id
const userRef = ref()
const handleAvatar = (info) => {
  if (info.type == 0) {
    userRef.value.open(info.userId)
  }
}
// 查看详情对方名片
const handleTopAvatar = (info) => {
  if (info.targetType == 1 && info.target && info.target.type == 0) {
    userRef.value.open(info.target.userId)
  }
}
// 查看详情对方组织岗位
const getUserCard = async (userId) => {
  const res = await messageApi.getUserInfo({ id: userId })
  console.log(res)
  userCard.value = res.data ? res.data : {}
}

const currentMessage = ref('')
const messageArea = ref()

//记住光标的坐标
const mouseX = ref(0)
//光标距离浏览器底部的高度
const mouseDistanceFromBottom = ref(0)
//at联系人弹窗的 style
const atDialogStyle = computed(() => ({
  position: 'fixed',
  left: mouseX.value + 'px',
  bottom: mouseDistanceFromBottom.value + 'px'
}))
const handleInput = () => {
  currentMessage.value = messageArea.value.innerHTML
// 获取光标位置
  const selection = window.getSelection()
  if (selection.rangeCount > 0) {
    const range = selection.getRangeAt(0)
    const rect = range.getBoundingClientRect()
    mouseX.value = rect.x
    //计算光标距离浏览器底部的高度
    mouseDistanceFromBottom.value = window.innerHeight - rect.y
  }
}

// ctrl+v触发该方法
const isDragOver = ref(false)

const handleDragEnter = () => {
  isDragOver.value = true
}

const handleDragOver = () => {
  isDragOver.value = true
}

const handleDragLeave = () => {
  isDragOver.value = false
}

const handleDrop = (event) => {
  isDragOver.value = false
  const dt = event.dataTransfer
  const files = dt.files

  const droppedFiles = []
  const droppedImages = []

  for (let i = 0; i < files.length; i++) {
    const file = files[i]
    if (file.type.startsWith('image/')) {
      droppedImages.push(file)
    } else {
      droppedFiles.push(file)
    }
  }

  // 处理图片文件
  if (droppedImages.length > 0) {
    handleSubmit(
      droppedImages.map((file) => ({ raw: file })),
      'img'
    )
  }

  // 处理非图片文件
  if (droppedFiles.length > 0) {
    const fileNames = droppedFiles.map((file) => file.name).join(', ')
    ElMessageBox.confirm(`确认发送文件 ${fileNames} 吗？`, '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
      .then(() => {
        handleSubmit(
          droppedFiles.map((file) => ({ raw: file })),
          'file'
        )
      })
      .catch(() => {
        ElMessage({
          type: 'info',
          message: '文件发送已取消'
        })
      })
  }
}

const handlePaste = (event) => {
  const clipboardData = event.clipboardData || window.clipboardData
  const items = clipboardData.items

  const pastedFiles = []
  const pastedImages = []

  for (let i = 0; i < items.length; i++) {
    const item = items[i]
    // 检查是否是文件（图像）
    if (item.kind === 'file' && item.type.startsWith('image/')) {
      const file = item.getAsFile()
      pastedImages.push(file)
    } else if (item.kind === 'file') {
      // 处理其他类型的文件
      const file = item.getAsFile()
      pastedFiles.push(file)
    }
  }

  // 处理图片文件
  if (pastedImages.length > 0) {
    handleSubmit(
      pastedImages.map((file) => ({ raw: file })),
      'img'
    )
  }

  // 处理非图片文件
  if (pastedFiles.length > 0) {
    const fileNames = pastedFiles.map((file) => file.name).join(', ')
    ElMessageBox.confirm(`确认发送文件 ${fileNames} 吗？`, '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
      .then(() => {
        handleSubmit(
          pastedFiles.map((file) => ({ raw: file })),
          'file'
        )
      })
      .catch(() => {
        ElMessage({
          type: 'info',
          message: '文件发送已取消'
        })
      })
  }

  let text = ''
  const e = event.originalEvent || event
  if (e.clipboardData && e.clipboardData.getData) {
    text = e.clipboardData.getData('text/plain')
  } else if (window.clipboardData && window.clipboardData.getData) {
    text = window.clipboardData.getData('text')
  }

  if (document.queryCommandSupported('insertText')) {
    document.execCommand('insertText', false, text)
  }
}

const imgVisible = ref(false)
const previewImg = ref('')
const uploadImage = (file) => {
  const reader = new FileReader()
  reader.onload = (e) => {
    const img = document.createElement('img')
    img.src = e.target.result
    img.style.maxHeight = '70px'
    img.style.cursor = 'pointer'
    img.addEventListener('click', (e) => {
      previewImg.value = e.target.src
      imgVisible.value = true
    })
    messageArea.value.appendChild(img)
    currentMessage.value = messageArea.value.innerHTML

    setCursorAfterImage(img)
  }
  reader.readAsDataURL(file) // 读取文件
}
// 设置光标在图片之后
const setCursorAfterImage = (img) => {
  const range = document.createRange()
  const selection = window.getSelection()

  if (selection && selection.rangeCount > 0) {
    range.selectNode(img)
    range.collapse(false)
    selection.removeAllRanges()
    selection.addRange(range)
  }
}

const atMessageToText = (message) => {
  // 创建一个 DOMParser 实例
  const parser = new DOMParser()
  // 将消息解析为 HTML 文档
  const doc = parser.parseFromString(message, 'text/html')
  // 提取整个文档的文本内容
  const textContent = doc.body.textContent || ''
  // 替换 HTML 实体（如 &nbsp;）为普通空格
  return textContent.replace(/&nbsp;/g, ' ').trim()
}
// 处理切割数据
const splitByImage = (html) => {
  // <br>换为\n
  html = html
    .replace(/&nbsp;/g, ' ')
    .replace(/&lt;/g, '<')
    .replace(/&gt;/g, '>')
    .replace(/&amp;/g, '&')
  html = html.replace(/<br\s*\/?>/gi, '\n')
  // 找img标签
  const imgRegex = /(<img[^>]*>)/g
  // 保存拆分后的数组
  const result = []
  let lastIndex = 0
  let match
  // 查找img标签 img前为单独一项 本身为一项 后面为一项
  while ((match = imgRegex.exec(html)) !== null) {
    if (match.index > lastIndex) {
      result.push(html.slice(lastIndex, match.index))
    }
    result.push(match[0])
    lastIndex = match.index + match[0].length
  }
  if (lastIndex < html.length) {
    result.push(html.slice(lastIndex))
  }
  processArray(result)
}
const processArray = async (arr) => {
  const resultList = []
  try {
    for (const item of arr) {
      if (item.startsWith('<img')) {
        try {
          const f = base64ToFile(item)
          const formData = new FormData()
          formData.append('file', f)
          formData.append('isImg', true)
          const file = await messageApi.uploadFile(formData)
          await messageApi.messageSend({
            convType: rowInfo.value.targetType == 2 ? 1 : 0,
            target:
              rowInfo.value.targetType == 2
                ? rowInfo.value.groupInfo.target_id
                : rowInfo.value.target.userId,
            payloadType: 3, //消息类型
            mediaType: 1, //媒体类型
            searchableContent: '[图片]',
            remoteMediaUrl: file.data.url,
            content: file.data.size
          })
          resultList.push(true)
        } catch {
          resultList.push(false)
        }
      } else {
        const result = await callTextAPI(item)
        resultList.push(result)
      }
    }

    const isHasError = resultList.some((item) => !item)
    if (!isHasError) {
      messageArea.value.innerHTML = ''
      currentMessage.value = ''
      //清除引用消息的内容
      resetQuoteMsg()
      await focusMessageArea() // 聚焦到消息框并将光标放在末尾
    }
  } finally {
    isDisabled.value = false
  }
}
/**
 * 把光标定位到消息输入框
 */
const focusMessageArea = async () => {
  setTimeout(() => {
    //使用 setTimeout 来延迟执行 nextTick，以确保 DOM 已经更新
    nextTick(() => {
      if (messageArea.value) {
        messageArea.value.focus()
      }
    })
  }, 0)
}

const base64ToFile = (urlData) => {
  // 解析HTML
  const parser = new DOMParser()
  const doc = parser.parseFromString(urlData, 'text/html')
  const imgElement = doc.querySelector('img')
  const imgSrc = imgElement ? imgElement.getAttribute('src') : null
  // return imgSrc
  console.log(imgSrc)
  const base64Data = imgSrc.split(',')[1]
  const binaryString = atob(base64Data)
  const length = binaryString.length
  const uint8Array = new Uint8Array(length)
  for (let i = 0; i < length; i++) {
    uint8Array[i] = binaryString.charCodeAt(i)
  }
  const blob = new Blob([uint8Array], { type: 'application/octet-stream' })
  return new File([blob], 'image.png', { type: 'application/octet-stream' })
}

const callTextAPI = async (item) => {
  try {
    // 提取需要的值
    const { targetType, groupInfo, target } = rowInfo.value
    const { u, i, n, d } = quoteMsg.value

    // 构建 quoteObj
    const quoteObj = { quote: { u, i, n, d } }
    // 编码 quoteBase
    let quoteBase = ''
    if (Object.keys(quoteObj.quote).every(key => quoteObj.quote[key] !== undefined)) {
      quoteBase = btoa(unescape(encodeURIComponent(JSON.stringify(quoteObj))))
    }

    // 构建 sendData
    const sendData = {
      convType: targetType === 2 ? 1 : 0,
      target: targetType === 2 ? groupInfo?.target_id : target?.userId,
      payloadType: 1,
      searchableContent: item ?? '',
      quoteBase
    }

    await messageApi.messageSend(sendData)
    return true
  } catch (error) {
    console.error('callTextAPI发送消息失败:', error) // 增加错误日志
    return false
  }
}


onMounted(() => {
  // console.log("触发onMounted")
  window.addEventListener('resize', handleResize)
  window.addEventListener('keydown', keyDown)
  getList()
  if (targetElement.value) {
    observer.observe(targetElement.value) // 开始观察目标元素
  }
})
onUnmounted(() => {
  // console.log("触发onUnmounted")
  window.removeEventListener('resize', handleResize)
  window.removeEventListener('keydown', keyDown, false)
  observer.disconnect() // 停止观察
})

// // 创建 Audio 对象
// const notificationSound = new Audio(receiveMsgNotification);
// // 播放声音提示的函数
// function playNotificationSound() {
//   notificationSound.play()
//     .catch(error => {
//       console.error('播放声音失败:', error);
//     });
// }
// 点击选择部门
const groupPicker = ref()
const groupPickerType = ref('')
const groupPickerTitle = ref('')
const groupId = ref(null)

const handleCreateGroup = (type, groupIdVal) => {
  groupPickerType.value = type
  groupPickerTitle.value = ''
  groupId.value = null

  if (type == 'createGroup') {
    groupPickerTitle.value = '发起群聊'
  } else if (type == 'add') {
    groupPickerTitle.value = '添加联系人'
    // groupId.value=  groupInfo.target_id

    groupId.value = groupIdVal

    console.log('handleCreateGroup add val=', groupIdVal)
  } else if (type == 'remove') {
    groupPickerTitle.value = '移除联系人'
    groupId.value = groupIdVal
  }

  groupPicker.value.show(groupIdVal)
}

// 接收信息
let memberIds = ref([])
let deptIds = ref([])
const selected = async (users, value2) => {
  console.log('selected users=', users)
  memberIds.value = []
  deptIds.value = []
  users.forEach((item) => {

    if (item.type == 'user') {
      memberIds.value.push(item.id)
    }
    if (item.type == 'dept') {
      deptIds.value.push(item.id)
    }
    // deptIds.value.push({
    //   member_id: item.id,
    //   alias: item.name,
    //   type: 0
    // })
  })


  console.log('memberIds.value=', memberIds.value)
  console.log('deptIds.value=', deptIds.value)

  if (groupPickerType.value == 'createGroup') {
    await messageApi.createMessageGroup({
      owner: useUserStore().getUser.id,
      name: value2,
      type: 2,
      portrait: '',
      memberIds: memberIds.value,
      deptIds: deptIds.value
    })
  } else if (groupPickerType.value == 'add') {
    await messageApi.addMessageGroupMember({
      target_id: groupId.value,
      memberIds: memberIds.value,
      deptIds: deptIds.value
    })
    showGroupInfo(currentGroupInfo.value.target_id)
  } else if (groupPickerType.value == 'remove') {

    await messageApi.delMessageGroupMember({
      group_id: groupId.value,
      members: memberIds.value
    })
    showGroupInfo(currentGroupInfo.value.target_id)
    // await messageApi.createMessageGroup({
    //   owner: useUserStore().getUser.id,
    //   name: value2,
    //   type: 2,
    //   portrait: '',
    //   groupMemberCommands: targetUser.value
    // })
  }
}

// 群信息
const groupDrawer = ref(false)
const currentGroupInfo = ref({})
const currentGroupUser = ref([])
// 群展开类型
const groupDrawerType = ref(1)
// 是否是群成员搜索状态
const isGroupMemberSearch = ref(false)
// 群成员搜索值
const groupMemberSearchValue = ref('')
// 是否是群名称编辑状态
const isGroupNameEdit = ref(false)
// 群名称编辑值
const groupNameValue = ref('')
// 是否是群主
const isGroupOwner = ref(false)

// 当前群是否置顶 是否免打扰
const groupTop = computed(() => rowInfo.value.top)
const groupNoDisturb = computed(() => rowInfo.value.mute)

const showGroupInfo = async (groupId) => {
  console.log('showGroupInfo groupId=', groupId)
  const currentGroupInfoResult = await messageApi.getMessageGroupInfo({
    groupId: groupId
  })
  currentGroupInfo.value = currentGroupInfoResult.data
  console.log('currentGroupInfo=', currentGroupInfo)

  const currentGroupUserResult = await messageApi.getMemberListByGroupId({
    groupId: groupId
  })
  currentGroupUser.value = currentGroupUserResult.data
  // console.log('currentGroupUser=', currentGroupUser)
  isGroupOwner.value = currentGroupUser.value.some(item => item.type == 2 && item.member_id == useUserStore().getUser.id)
  // console.log('isGroupOwner=', isGroupOwner.value)

  groupDrawerTypeChange(1)
  groupDrawerType.value = 1
  groupDrawer.value = true
}

const showUserInfo = (userId) => {
  console.log('showUserInfo userId=', userId)
  userRef.value.open(userId)
}

const userDialogSetMsg = () => {
  console.log('userDialogSetMsg')
  groupDrawer.value = false
}

// 群展示类型 1是默认，2是群成员列表
const groupDrawerTypeChange = (type) => {
  groupDrawerType.value = type

  isGroupMemberSearch.value = false
  groupMemberSearchValue.value = ''
  isGroupNameEdit.value = false
  groupNameValue.value = ''
}

const groupNameRef = ref(null)
const editGroupName = () => {
  groupNameValue.value = currentGroupInfo.value.name
  isGroupNameEdit.value = true
  setTimeout(() => {
    groupNameRef.value.focus()
  }, 100)
}

const groupNameChange = async () => {
  console.log('groupNameChange groupNameValue=', groupNameValue.value)
  if (groupNameValue.value && groupNameValue.value != currentGroupInfo.value.name) {
    await messageApi.updateMessageGroupInfo({
      group_id: currentGroupInfo.value.target_id,
      type: 0,
      value: groupNameValue.value
    })

    await showGroupInfo(currentGroupInfo.value.target_id)
    isGroupNameEdit.value = false
    groupNameValue.value = ''
  } else {
    groupNameValue.value = ''
    isGroupNameEdit.value = false
  }
}

// 退出群聊
const handleExitGroup = async () => {
  try {
    await messageApi.exitMessageGroup({
      userId: useUserStore().getUser.id,
      groupId: currentGroupInfo.value.target_id
    })
    groupDrawer.value = false
    await getList(false)
    activateTab(0, leftList.value[0])
  } catch (error) {
    console.log(error)
  }
}

// 解散群组
const disbandedGroup = async () => {
  try {
    await messageApi.dismissMessageGroup({
      groupId: currentGroupInfo.value.target_id
    })
    groupDrawer.value = false
    await getList(false)
    activateTab(0, leftList.value[0])
  } catch (error) {
    console.log(error)
  }
}

const handleGroupTop = (val) => {
  console.log('handleGroupTop val=', val)

  let type = '取消置顶'
  if (rowInfo.value.top) {
    T
    type = '取消置顶'
  } else {
    type = '置顶会话'
  }

  getSetTop(type, rowInfo.value, activeTab.value)
}
const handleGroupNoDisturb = (val) => {
  console.log('handleGroupNoDisturb val=', val)

  let type = rowInfo.value.mute ? '取消免打扰' : '消息免打扰'

  getSetTop(type, rowInfo.value, activeTab.value)
}

const rowEqualityJudgment = (row, row2) => {
  if (row.targetType == 1 && row2.target && row.target.userId == row2.target.userId) {
    return true
  } else if (
    row.targetType == 2 &&
    row2.groupInfo &&
    row.groupInfo.target_id == row2.groupInfo.target_id
  ) {
    return true
  }

  return false
}


const emojiShow = ref(false)

const changeEmoji = () => {

  emojiShow.value = !emojiShow.value
}
const chooseEmoji = async (emoji) => {

  emojiShow.value = false

  // console.log("emoji=",emoji)

  //拼接@消息
  const messageContent = document.createElement('span')
  messageContent.textContent = `messageContent` // \u00A0 是不换行空格
  messageArea.value.innerHTML += emoji
  currentMessage.value = messageArea.value.innerHTML

  const range = document.createRange() // 创建一个范围对象
  range.selectNodeContents(messageArea.value) // 选择元素内的所有内容
  range.collapse(false) // 将范围折叠到末尾
  const selection = window.getSelection() // 获取当前的选择对象
  selection.removeAllRanges() // 移除所有已有的范围
  selection.addRange(range) // 添加新的范围，光标会移动到末尾


}
const vClickOutside = () => {

  console.log('vClickOutside')

  changeEmoji()
}


// 检测文字是否是链接
const isLink = (str) => {
  const linkRegex = /^(http:\/\/|https:\/\/)?([\w-]+(\.[\w-]+)+([\w.,@?^=%&:/~+#-]*[\w@?^=%&/~+#-])?)$/
  return linkRegex.test(str)
}

// 引用图片弹框相关状态
const showImagePreview = ref(false)
const imageUrl = ref('')
const textClick = async (str, base64edData) => {
  console.info('textClick==>', str)
  // 如果是链接，保留原有逻辑
  if (isLink(str)) {
    window.open(str, '_blank')
  } else if (str.includes('[图片]') && isValidBase64(base64edData)) {
    try {
      // 解析 base64edData
      const decodedData = JSON.parse(atob(base64edData))
      const imageId = decodedData?.quote?.u // 获取图片 ID

      if (imageId) {
        // 调用接口获取图片链接
        const messageInfo = await messageApi.getImMessageById({
          messageId: imageId
        })
        imageUrl.value = messageInfo.data?.payload.remoteMediaUrl
        showImagePreview.value = true// 显示弹框
      }
    } catch (error) {
      console.error('解析或获取图片失败:', error)
    }
  }
}
// 关闭图片预览
const closeViewer = () => {
  showImagePreview.value = false
  imageUrl.value = '' // 可选：清空 URL
}

</script>
<style lang="less" scoped>
.quote-message {
  display: flex;
  align-items: flex-start;
  margin-bottom: 8px;
  padding: 8px 12px;
  background-color: #f5f7fa;
  border-radius: 4px;

  .quote-content {
    flex: 1;
    overflow: hidden;
    padding-left: 10px;
    border-left: 3px solid #67C23A;

    .quote-sender {
      font-size: 12px;
      color: #606266;
      margin-bottom: 4px;
    }

    .quote-text {
      font-size: 14px;
      color: #303133;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }

  .quote-close {
    padding: 2px;
    margin-left: 8px;
    color: #909399;

    &:hover {
      color: #606266;
    }
  }
}

.rowBox {
  margin: 0 0 0 20px;
  position: fixed;
  left: 180px;
  right: 0;
  top: 81px;
  bottom: 0;
  font-size: 14px;
}

.isCol {
  left: 64px;
  margin-left: 0 !important;
}

.isCBox-else {
  left: 0;
}

.el-col {
  height: 100%;
}

.left-col {
  padding: 0 !important;
  border-right: 1px solid #eceded;
  box-sizing: border-box;
  background: #fff;
}

.el-col-4 {
  // max-width: 14%;
}

p {
  margin: 0;
}

.forDiv {
  overflow-y: auto;
  height: calc(100% - 41px);
  position: relative;
  padding: 2px 0;
}

.contentBox {
  margin: 0 6px;
  padding: 12px 0 12px 16px;
  display: flex;
  cursor: pointer;
  margin-bottom: 1px;
  position: relative;
}

.active,
.contentBox:hover {
  background: #f3f4f7;
  border-radius: 6px;
}

//群组头像样式
.groupDiv1 {
  background: #3370ff;
  color: #ffffff;
  text-align: center;
}

//单人聊天样式
.div1 {
  width: 42px;
  height: 42px;
  background: #3370ff;
  border-radius: 6px;
  text-align: center;
  line-height: 42px;
  color: #ffffff;
  box-sizing: border-box;
  font-size: 12px;
  margin-right: 8px;
}

.group-container {
  position: relative;
  width: 42px;
  height: 42px;
  overflow: hidden;
  border-radius: 6px;
  background: #f0f0f0; // 浅灰色背景
  margin-right: 8px;
}

.group-avatar {
  position: absolute;
  display: flex;
  justify-content: center;
  align-items: center;
}

.group-member-avatar,
.groupDiv1 {
  width: 100%;
  height: 100%;
  box-sizing: border-box;
}

.group-member-avatar {
  background: transparent;

  .el-avatar--square {
    --el-avatar-border-radius: none;
  }
}

.top-content {
  cursor: pointer;
  display: flex;
}

.div1-avatar {
  border-radius: 6px;
  box-sizing: border-box;
  margin-right: 8px;

  .el-avatar {
    background: transparent;
  }
}

.div2 {
  flex: 1;
  box-sizing: border-box;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.p2-1 {
  margin-bottom: 2px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.p2-2,
.p3-1 {
  font-size: 12px;
  color: #7d8082;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  font-family: auto;
}

.p3-1 {
  margin-top: 3px;
}

.p4-1 {
  height: 17px;
}

.div3 {
  width: 70px;
  text-align: right;
  padding-right: 6px;
  height: 20px;
}

.left-top {
  margin: 0 6px;
  padding: 6px 0;
  border-bottom: 1px solid #ebeced;

  .blue-button {
    background: #e9f0ff;
    border-radius: 13px;
    font-weight: 500;
    font-size: 12px;
    color: #3370ff;
    padding: 4px 12px;
    cursor: pointer;
  }
}

.rightCol {
  display: flex;
  flex-direction: column;
}

.right-top {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: #f3f4f7;
  padding: 6px 12px 6px 20px;
  // width: 100%;
  box-shadow: inset 0px -1px 0px 0px #eceded;
}


.p2-1-1 {
  // line-height: 34px;
  font-size: 14px;
}

.div2-2 {
  display: flex;
  flex-direction: column;
  justify-content: center;

  .p2-2 {
    margin-top: 2px;
  }
}


.footerDiv {
  border-top: 1px solid #e1e3e5;
  padding: 10px 20px 20px;
}

.message-area {
  transition: border-color 0.3s, background-color 0.3s;
}

.message-area.drag-over {
  border-color: #3370ff;
  background-color: rgba(51, 112, 255, 0.1);
  position: relative;
}

.message-area.drag-over::after {
  content: "拖入文件实现发送";
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: #3370ff;
  font-size: 16px;
  font-weight: bold;
  pointer-events: none;
}

.textareaDiv {
  --el-input-hover-border-color: none;
  --el-input-clear-hover-color: none;
  --el-input-focus-border-color: none;
  --el-input-bg-color: transparent;

  :deep(.el-textarea__inner) {
    height: 126px;
    border: none;
    resize: none;
    box-shadow: none;
    padding: 0;
  }
}

.enterDiv {
  float: right;
  margin-top: 10px;

  .spanTip {
    display: inline-block;
    line-height: 32px;
    margin-right: 10px;
    color: #bfc0c1;
  }

  .el-button--primary {
    background: #3370ff;
  }

  .is-disabled {
    background: #a0cfff;
  }
}

.specialBlue {
  color: #3370ff;
}

.specialGrey {
  color: #c0c4cc;
}

.readStatusSpan {
  margin-top: 2px;
  display: inline-block;
}

.readStatusSpan-click {
  cursor: pointer;
}

.topClass {
  position: relative;
}

.topClass::before {
  content: '';
  position: absolute;
  top: 4px;
  right: 4px;
  width: 0;
  height: 0;
  border-left: 10px solid transparent;
  border-bottom: 10px solid #b9d6f7;
  border-radius: 2px;
  transform: rotate(270deg);
}

.muteClass {
  position: absolute;
  bottom: 12px;
  width: 14px;
}

.messageHornClass {
  position: absolute;
  right: 2px;
  bottom: 5px;
  width: 16px;
}

.closeClass {
  position: absolute;
  left: 0px;
  top: 0;
  bottom: 0;
  font-size: 12px;
  padding: 4px 2px 0;
  color: #9a9da0;
  display: none;
  line-height: 52px;
  height: 100%;
  box-sizing: border-box;
  width: 28px;
}

.contentBox:hover .closeClass {
  display: block;
}

.eventClass {
  margin: -4px -4px 6px;
  display: flex;
  align-items: center;

  .up-div {
    border-radius: 4px;
    padding: 4px 4px 2px;
    margin-right: 10px;

    img {
      width: 20px;
      // height: 19px;
      cursor: pointer;
    }
  }

  .up-div:hover {
    background: #d9dcde;
  }
}

/* 自定义徽章样式 */
.custom-badge {
  --el-badge-size: 15px;
  --el-badge-padding: 5px;
  --el-badge-radius: 15px;
  --el-badge-font-size: 12px;
  --el-badge-color: #ffffff; /* 调整徽章文字颜色 */
  --el-badge-background: #f22727;
  z-index: 1; /* 确保徽章在顶层 */
}

.birthdayPush {
  background: #fff;
  padding: 12px;
  border-radius: 8px;
  width: 362px;
  box-sizing: border-box;

  .birthdayP1 {
    align-items: center;
    display: flex;
    font-size: 15px;
    margin-bottom: 12px;

    .birthdayImg1 {
      width: 16px;
      height: 16px;
      margin-right: 8px;
    }
  }

  .birthdayP2 {
    font-size: 15px;
    font-weight: bold;
    margin-bottom: 8px;
  }

  .birthdayP3 {
    margin-bottom: 16px;
    line-height: 21px;
  }

  .birthdayP4 {
    position: relative;

    .avatarP {
      position: absolute;
      font-size: 22px;
      color: #fff;
      top: 87px;
      left: 150px;
    }

    .avatarImg {
      position: absolute;
      top: 62px;
      left: 132px;
      width: 80px;
      height: 80px;
      border-radius: 50%;
    }
  }
}

.placeholderImage {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  font-size: 14px;
  color: #909399;
}

.avatarBox {
  cursor: pointer;
}

.message-area {
  // border: 1px solid #ddd;
  height: 120px;
  padding: 0;
  overflow-y: auto;
  box-sizing: border-box;
  // margin: -5px -10px;

  img {
    margin: 0 10px;
  }
}

.message-area:focus {
  outline: none;
}
</style>
<style>
.p4 p {
  padding: 0 !important;
  margin: 0;
}

.dialog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0;
}

.member-list {
  max-height: 300px;
  overflow-y: scroll;
  display: flex;
  flex-direction: column;
}

/* 隐藏滚动条 */
.member-list::-webkit-scrollbar {
  width: 0; /* 隐藏垂直滚动条 */
  height: 0; /* 隐藏水平滚动条 */
  background: transparent; /* 可选：设置背景透明 */
}

.member-item {
  display: flex;
  align-items: center;
  padding: 8px 0;
  cursor: pointer;
}

.member-content {
  display: flex;
  align-items: center; /* 确保头像和名称在同一水平线上 */
}

.member-item .el-checkbox {
  margin-right: 10px; /* 调整 checkbox 和头像之间的距离 */
}

.member-name {
  margin-left: 12px;
  flex: 1;
}


.multi-select-footer {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 10px 15px;
  background-color: #fff;
  border-top: 1px solid #ebeef5;
  height: 194px; /* 与输入框高度一致 */
  box-sizing: border-box;
}

.selected-count {
  font-size: 14px;
  color: #606266;
  margin-bottom: 15px;
}

.action-buttons {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 40px;
  width: 100%;
}

.action-button {
  display: flex;
  flex-direction: column;
  align-items: center;
  cursor: pointer;
}

.action-button img {
  width: 24px;
  height: 24px;
  margin-bottom: 5px;
}

.action-button span {
  font-size: 12px;
  color: #606266;
}

.action-button.cancel span {
  color: #606266;
}

.action-button.forward span {
  color: #3370FF;
}

.action-button.disabled {
  opacity: 0.5;
  cursor: not-allowed;
}
</style>
