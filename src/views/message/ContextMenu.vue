<template>
  <div v-if="visible" :style="menuStyle" class="context-menu">
    <button @click="handleAction(dataObj.top ? '取消置顶' : '置顶会话')">{{ dataObj.top ? '取消置顶' : '置顶会话' }}</button>
    <button @click="handleAction(dataObj.mute ? '取消免打扰' : '消息免打扰')" class="splitTop">
      {{ dataObj.mute ? '取消免打扰' : '消息免打扰' }}
    </button>
    <button @click="handleAction('移除会话')" class="splitBottom">移除会话</button>
  </div>
</template>

<script setup lang="ts">
const windowHeight = ref(window.innerHeight)
const handleResize = () => {
  windowHeight.value = window.innerHeight
}

const position = ref({ x: 0, y: 0 })

const menuStyle = computed(() => {
  const offset = (windowHeight.value - position.value.y) < 150 ? -150 : 0
  return {
    left: `${position.value.x}px`,
    top: `${position.value.y + offset}px`
  }
})



const visible = ref(false)
const clickedElement = ref<HTMLElement | null>(null)
const target = ref<HTMLElement | null>(null)
const dataObj = ref<any>(null)
const dataIndex = ref<string | null>(null)

const showMenu = (event: MouseEvent) => {
  console.log(event, 'event')
  event.preventDefault()
  position.value = { x: event.pageX, y: event.pageY }
  visible.value = true
  clickedElement.value = event.target as HTMLElement

  target.value = clickedElement.value;
  while (target.value) {
    if (target.value.classList.contains('contentBox')) {
      const dataObjAttr = target.value.getAttribute('data-obj')
      if (dataObjAttr) {
        try {
          dataObj.value = JSON.parse(dataObjAttr)
        } catch (error) {
          dataObj.value = null
        }
      } else {
        dataObj.value = null
      }
      dataIndex.value = target.value.getAttribute('data-index')
      console.log('Data obj:', dataObj.value, dataIndex.value)
      break
    }
    target.value = target.value.parentElement as HTMLElement
  }

  window.addEventListener('resize', handleResize)
  window.addEventListener('click', hideMenu)
}



const hideMenu = () => {
  visible.value = false
  window.removeEventListener('resize', handleResize)
  window.removeEventListener('click', hideMenu)

}

const handleAction = (action: string) => {
  console.log(clickedElement.value, 'clickedElement.value')
  // if (action === '置顶会话' || action === '消息免打扰') {
  // let target = clickedElement.value
  // while (target) {
  //   if (target.classList.contains('contentBox')) {
  //     const dataObj = target.getAttribute('data-obj')
  //     const dataIndex = target.getAttribute('data-index')
  //     console.log('Data obj:', dataObj, dataIndex)
  //     emit('setTop', action, dataObj, dataIndex)
  //     break
  //   }
  //   target = target.parentElement as HTMLElement // 向上查找父元素
  // }
  emit('setTop', action, dataObj.value, dataIndex.value)
  // }
  hideMenu()
}


defineExpose({ showMenu })
const emit = defineEmits(['setTop'])
</script>

<style scoped>
.context-menu {
  position: fixed;
  background: #fff;
  border: 1px solid #ddd;
  box-shadow: 0px 0px 6px rgba(0, 0, 0, 0.2);
  z-index: 1000;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  padding: 6px 4px;
  font-size: 14px;
}

.context-menu button {
  border: none;
  background: #fff;
  padding: 6px 10px;
  width: 100%;
  cursor: pointer;
  text-align: left;
  border-radius: 4px;
  margin-bottom: 2px;
}

.splitTop {
  position: relative;
  margin-bottom: 4px !important;
}

.splitBottom {
  margin-top: 4px !important;
}

.splitTop::after {
  position: absolute;
  bottom: -4px;
  left: 0;
  right: 0;
  width: 100%;
  height: 1px;
  content: '';
  background: #f0f0f0;
}

.context-menu button:hover {
  background: #f0f1f2;
}

.el-button {}
</style>