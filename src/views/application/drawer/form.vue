<template>
  <el-drawer :title="dialogTitle" v-model="dialogVisible" class="custom-detail-header is-custom-other" size="34%">
    <el-form ref="formRef" :model="formData" :rules="formRules" v-loading="formLoading" label-position="top"
      label-width="150px">
      <!-- <icon name="el-icon-coordinate w-h-center" /> -->
      <img src="@/assets/imgs/logoBlue.png" class="logo-top" v-if="fileList.length == 0" />
      <img :src="fileList[0].url" class="logo-top" v-if="fileList.length > 0" />
      <el-form-item label="应用分类" prop="lifeCenterTypeId">
        <el-select v-model="selectedType" value-key="id" placeholder="请选择分类" clearable>
          <el-option v-for="item in typeListAll" :key="item.id" :label="item.name" :value="item" />
        </el-select>
      </el-form-item>
      <el-form-item label="应用名称" prop="name">
        <el-input v-model="formData.name" placeholder="请输入应用名称" clearable />
      </el-form-item>
      <el-form-item label="移动端链接" prop="homepageLink">
        <span class="defaultSpan">https://</span>
        <el-input v-model="formData.homepageLink" placeholder="" clearable class="defaultInput">
        </el-input>
      </el-form-item>
      <el-form-item label="PC端链接" prop="pcHomepageLink">
        <span class="defaultSpan">https://</span>
        <el-input v-model="formData.pcHomepageLink" placeholder="" clearable class="defaultInput" />
      </el-form-item>
      <el-form-item label="授权使用范围" prop="scopeType">
        <el-select v-model="formData.scope.scopeType" placeholder="请选择授权使用范围">
          <el-option label="全部成员" value="all" />
          <el-option label="部分成员" value="part" />
          <el-option label="企业管理员" value="admin" />
        </el-select>
        <div v-if="formData.scope.scopeType == 'part'" class="tagDiv">
          <el-tag type="info" @click="handleDept">+ 添加</el-tag>
          <org-picker title="请选择" ref="orgPicker" multiple :selected="targetUser" @ok="selected" />
          <el-tag v-for="(item, index) in targetUser" :key="index" closable @close="targetUser.splice(index, 1)">
            {{ item.name }}
          </el-tag>
        </div>
      </el-form-item>
      <el-form-item label="应用图标" class="iconItem">
        <!-- <el-input v-model="formData.icon" placeholder="请输入应用描述" clearable /> -->
        <el-upload class="upload-demo" action="" :auto-upload="false" :file-list="fileList" :limit="1"
          :on-change="onChange" :on-remove="onRemove" :on-exceed="onExceed" list-type="picture-card"
          :on-preview="handlePictureCardPreview" :class="{ 'isImgBox': fileList.length > 0 }">
          <el-icon class="avatar-uploader-icon" v-if="fileList.length == 0">
            <Plus />
          </el-icon>
        </el-upload>
        <p class="tipP">请上传JPG/PNG格式、240*240px以上、1:1的无圆角图标；未设置则默认展示系统图标。</p>
        <el-image-viewer v-if="imgVisible" :url-list="[dialogImageUrl]" @close="imgVisible = false" />
      </el-form-item>
      <el-form-item label="应用描述">
        <el-input v-model="formData.description" placeholder="请输入应用描述" clearable />
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="footerBox">
        <span class="tip">应用将会被添加至当前组织应用中心「未分组中」。</span>
        <el-button @click="submitForm" type="primary" :disabled="formLoading">
          {{ formType == 'create' ? '添加' : '更新' }}
        </el-button>
      </div>
    </template>
  </el-drawer>
</template>
<script setup lang="ts">
import { Plus } from '@element-plus/icons-vue'
import { applicationApi } from '@/api/application/list'
import OrgPicker from '@/components/common/OrgPicker.vue'
import * as FileApi from '@/api/infra/file'

type TypeOption = {
  id: number,
  name: string,
}

const props = defineProps<{ typeListAll: TypeOption[] }>()

const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const selectedType = ref<TypeOption>() // 选中的分类
const formData = ref<{
  lifeCenterTypeId: number | undefined,
  name: any,
  homepageLink: any,
  pcHomepageLink: any,
  scope: {
    scopeType: any,
    scopeObjects: any[] // 假设 scopeObjects 是一个数组
  },
  description: any,
  icon: any
}>({
  lifeCenterTypeId: undefined,
  name: undefined,
  homepageLink: undefined,
  pcHomepageLink: undefined,
  scope: {
    scopeType: undefined,
    scopeObjects: [] // 初始化为空数组
  },
  description: undefined,
  icon: undefined
})
const formRules = reactive({
  name: [{ required: true, message: '请输入应用名称', trigger: 'blur' }],
  // homepageLink: [{ required: true, message: '请输入链接', trigger: 'blur' }],
  // pcHomepageLink: [{ required: true, message: '请输入链接', trigger: 'blur' }],
})

watch(selectedType, async (newValue) => {
  formData.value.lifeCenterTypeId = newValue?.id
})

const haveIcon = ref(false)
const formRef = ref() // 表单 Ref
/** 打开弹窗 */
const rowId = ref('')
const open = async (type: string, row) => {
  rowId.value = ''
  if (row) {
    rowId.value = row.id
  }
  // return
  resetForm()
  dialogVisible.value = true
  dialogTitle.value = type == 'create' ? '新增应用' : '编辑应用'
  formType.value = type
  formData.value.scope.scopeType = 'all'
  targetUser.value = []
  if (type == 'update') {
    formLoading.value = true
    try {
      const res = await applicationApi.getApplication({ id: row.id })
      formData.value.name = res.name
      formData.value.homepageLink = res.homepageLink ? res.homepageLink.substring(8) : ''
      formData.value.pcHomepageLink = res.pcHomepageLink ? res.pcHomepageLink.substring(8) : ''
      formData.value.scope.scopeType = res.scope.scopeType
      formData.value.description = res.appDesc
      selectedType.value = props.typeListAll.find(item => item.id === res.lifeCenterTypeId);

      if (res.appIcon) {
        haveIcon.value = true
        fileList.value.push({ url: res.appIcon })
      }

      if (res.scope.scopeType == 'part') {
        res.scope.scopeObjects.forEach(item => {
          targetUser.value.push({
            id: item.objectId,
            type: item.objectType,
            name: item.objectName
          })
        })
      }
    } finally {
      formLoading.value = false
    }
  }
}


const fileList = ref(<any>[])//el-upload绑定的fileList
const endResult = ref(<any>[])//最终提交的fileList

const onRemove = (_: any, list: any) => {
  haveIcon.value = false
  fileList.value = list
}
const onChange = (_: any, list: any) => {
  haveIcon.value = false
  fileList.value = list
}
const dialogImageUrl = ref('')
const imgVisible = ref(false)
const handlePictureCardPreview = (file) => {
  dialogImageUrl.value = file.url
  imgVisible.value = true
}
const onExceed = () => {
  ElMessage.warning("当前最多只能上传1张图片，请移除后上传")
}

// 点击选择部门
const orgPicker = ref()
const handleDept = () => {
  orgPicker.value.show()
}
// 接收部门信息
let targetUser = ref<any[]>([])
const selected = (users: any) => {
  console.log(users, 'users')
  targetUser.value = []
  users.forEach((item) => {
    targetUser.value.push({
      id: item.id,
      name: item.name,
      type: item.type
    })
  })
}

defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  console.log(formData.value.homepageLink)
  // return
  // 校验表单
  await formRef.value.validate()
  if (formData.value.scope.scopeType == 'part' && targetUser.value.length == 0) {
    return message.warning('请选择授权使用范围成员')
  }
  formLoading.value = true
  if (fileList.value.length > 0 && !haveIcon.value) {
    const uploadQueue = <any>[]
    fileList.value.forEach((file: any) => {
      uploadQueue.push(
        FileApi.updateFile({ file: file.raw })
      )
    })
    Promise.all(uploadQueue).then(async res => {
      endResult.value = []
      let isFault = false;
      res.forEach((item: any) => {
        if (item.code === 0) {
          endResult.value.push(item.data)
        } else {
          isFault = true
        }
      })
      if (isFault) {
        ElMessage.error("图片上传失败，请重新上传")
        formLoading.value = false
        return
      }
      submitAll()
    }).catch(() => {
      ElMessage.error("图片上传失败，请重新上传")
      formLoading.value = false
    })
  } else {
    submitAll()
  }
}
const submitAll = async () => {
  // console.log(endResult.value)
  // return
  try {
    if (selectedType.value === undefined) return message.warning('请选择分类')
    if (!formData.value.pcHomepageLink && !formData.value.homepageLink) return message.warning('请输入链接')
    if (formData.value.scope.scopeType == 'part') {
      formData.value.scope.scopeObjects = []
      targetUser.value.forEach((item) => {
        formData.value.scope.scopeObjects.push({
          objectType: item.type,
          objectId: item.id,
          objectName: item.name,
        })
      })
    }
    let obj = {
      // 1应用管理 2生活中心
      type: 1,
      lifeCenterTypeId: formData.value.lifeCenterTypeId,
      name: formData.value.name,
      pcHomepageLink: formData.value.pcHomepageLink ? 'https://' + formData.value.pcHomepageLink : '',
      homepageLink: formData.value.homepageLink ? 'https://' + formData.value.homepageLink : '',
      scope: formData.value.scope,
      description: formData.value.description,
      icon: haveIcon.value ? fileList.value[0].url : endResult.value.length > 0 ? endResult.value[0] : '',
      id: rowId.value ? rowId.value : null
    }
    console.log(obj)
    // return
    await applicationApi.createApplication(obj)
    message.success('操作成功')
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}


/** 重置表单 */
const resetForm = () => {
  formData.value = {
    lifeCenterTypeId: undefined,
    name: undefined,
    homepageLink: undefined,
    pcHomepageLink: undefined,
    scope: {
      scopeType: undefined,
      scopeObjects: []
    },
    description: undefined,
    icon: undefined
  }
  formRef.value?.resetFields()
  selectedType.value = undefined
  endResult.value = []
  fileList.value = []
  haveIcon.value = false
}

</script>
<style lang="less" scoped>
.el-input,
.el-select {
  width: 100%;
}

.el-input {
  --el-input-border: none;
  --el-input-hover-border: transparent;
  --el-input-focus-border: transparent;
  --el-input-border-color: transparent;
  --el-input-hover-border-color: transparent;
  --el-input-focus-border-color: transparent;
}

:deep(.el-input__wrapper) {
  padding: 0 11px 0 0;
}

.el-select {
  --el-select-border: none;
  --el-select-border-color-hover: transparent;
  --el-select-close-hover-color: transparent;
  --el-select-input-focus-border-color: transparent;
  --el-select-disabled-border: transparent;
}

:deep(.el-select__wrapper) {
  box-shadow: none !important;
  padding-left: 0;
}

:deep(.el-form-item.is-error .el-input__wrapper, .el-form-item.is-error .el-input__wrapper.is-focus, .el-form-item.is-error .el-input__wrapper:focus, .el-form-item.is-error .el-input__wrapper:hover, .el-form-item.is-error .el-select__wrapper, .el-form-item.is-error .el-select__wrapper.is-focus, .el-form-item.is-error .el-select__wrapper:focus, .el-form-item.is-error .el-select__wrapper:hover, .el-form-item.is-error .el-textarea__inner, .el-form-item.is-error .el-textarea__inner.is-focus, .el-form-item.is-error .el-textarea__inner:focus, .el-form-item.is-error .el-textarea__inner:hover) {
  box-shadow: none !important;
}

:deep(.el-form-item__label) {
  margin-bottom: 0 !important;
}

:deep(.el-input__inner) {
  color: #303133;
}

.defaultInput {
  :deep(.el-input__wrapper) {
    padding: 0 11px 0 48px;
  }
}

.el-form {
  background: #fff;
  padding: 50px 20px 10px !important;
}

.el-form-item {
  margin-bottom: 10px;
  padding-top: 15px;
  border-top: 1px solid #E1E2E4;
  position: relative;
  color: #303133;
}

.defaultSpan {
  position: absolute;
  left: 0;
  z-index: 1;
  width: 48px;
}

.footerBox {
  text-align: center !important;

  // border-top: 1px solid #eee !important;
  .tip {
    color: #909399;
    font-size: 14px;
    margin-bottom: 12px;
    display: block;
    text-align: left;
  }

  .el-button {
    width: 100%;
    height: 44px;
    background: #3370FF;
  }
}

:deep(.el-form-item.is-required:not(.is-no-asterisk).asterisk-left>.el-form-item__label:before) {
  display: none;
}

.tagDiv {
  margin-top: 6px;

  :deep(.el-tag) {
    padding: 5px 7px;
    --el-icon-size: 12px;
    margin-right: 8px;
  }

  .el-tag.el-tag--info {
    background: #fff;
    color: #606266;
    cursor: pointer;
  }
}

:deep(.el-icon-coordinate) {
  width: 60px;
  height: 60px;
  background: #FFE8B8;
  border-radius: 8px;
  font-size: 30px;
  color: #FF9200;
  margin-right: 8px;
  margin: 0 auto 30px;
}

.logo-top {
  width: 60px;
  height: 60px;
  border-radius: 8px;
  margin: 0 auto 30px;
  display: flex;
}

:deep(.el-form-item--default .el-form-item__error) {
  padding-top: 12px;
}

.iconItem {
  :deep(.el-form-item__content) {
    display: inline;
  }

  :deep(.el-upload--picture-card) {
    width: 100px;
    height: 100px;
    background: #ECEDED;
    border: none;
    margin-top: 4px;
    border-radius: 4px;
  }

  :deep(.el-upload--picture-card:hover) {
    border: 1px dashed #ccc;
  }

  .isImgBox {
    margin-top: 4px;
    height: 100px;
  }

  :deep(.isImgBox .el-upload--picture-card) {
    display: none;
  }

  :deep(.el-upload-list--picture-card .el-upload-list__item) {
    height: 100px;
    width: 100px;
  }

  .tipP {
    margin: 0;
    line-height: normal;
    margin-top: 4px;
  }
}
</style>
<style>
.is-custom-other .el-drawer__body {
  background: #F3F4F7 !important;
  padding: 0 !important;
}

.is-custom-other .el-drawer__footer {
  padding: 20px;
}
</style>
