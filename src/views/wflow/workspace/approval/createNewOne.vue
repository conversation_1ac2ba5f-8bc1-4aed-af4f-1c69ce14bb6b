<template>
  <Dialog v-model="dialogVisible" title="新建办一下" width="830px">
    <el-form ref="formRef" label-width="10px">
      <el-form-item v-show="belongToValue == 1">
        <div style="max-width: 350px">
          <el-button
            icon="el-icon-user"
            type="primary"
            size="default"
            @click="$refs.orgPicker.show(newTenantId.tenantId)"
            >选择接收人</el-button
          >
          <org-picker type="user"  multiple ref="orgPicker" :selected="Uservalue" @ok="selected" />
          <div style="margin-top: 5px">
            <el-tag
              size="small"
              style="margin: 5px"
              closable
              v-for="(dept, i) in Uservalue"
              @close="delDept(i)"
              >{{ dept.name }}</el-tag
            >
          </div>
        </div>
      </el-form-item>
      <el-form-item>
        <div class="xufu">
          <el-input
            class="custom-placeholder"
            v-model="textarea"
            type="textarea"
            placeholder="请输入消息内容"
            :autosize="{ minRows: 4 }"
          >
            <!-- <template #prefix>
              <img src="../../../assets/imgs/xinxi.png" alt="" />
            </template> -->
          </el-input>
          <img class="imgs" src="@/assets/imgs/xinxi.png" alt="" />
        </div>
      </el-form-item>

      <el-form-item>
        <div class="daiban" @click="dBans(viUrl)">
          <img src="@/assets/imgs/xiaoren.svg" alt="" />
          <div>待办任务通知</div>
        </div>
      </el-form-item>

      <el-form-item>
        <div style="margin-bottom: 10px">
          <el-select
            v-model="remindMinutes"
            disabled
            placeholder="选择提醒时间"
            style="width: 300px"
          >
            <template #prefix>
              <img src="@/assets/imgs/tixing.png" alt="" />
            </template>
            <el-option
              v-for="option in options"
              :key="option.id"
              :label="option.name"
              :value="option.id"
            >
              <!-- <div>{{ option.name + '-' + option.name}}</div> -->
            </el-option>
          </el-select>
        </div>
      </el-form-item>

      <el-form-item>
        <div class="newEdit">
          <!-- <div
            :class="{
              first_child1: index == remainingButtones.length - 1,
              active: index == checkindexRemaining
            }"
            v-for="(item, index) in remainingButtones"
            :key="index"
            @click="chooseRemaining(index)"
          >
            {{ item.text }}
          </div> -->
          <div @click="dingShi">
            <img v-if="!dingShiFs" src="@/assets/imgs/moren.png" alt="" />
            <img v-else src="@/assets/imgs/yixuan.png" alt="" />
            <div v-if="!dateTimes"> 定时发送 </div>
            <div v-if="dateTimes">{{ newTi }} {{ newTi1 }}发送</div>
          </div>

          <div @click="fenBie">
            <img v-if="!fenBieFs" src="@/assets/imgs/moren.png" alt="" />
            <img v-else src="@/assets/imgs/yixuan.png" alt="" />
            <div>分别发送</div>
          </div>
        </div>
      </el-form-item>
    </el-form>
    <div class="hide_input">
      <el-date-picker
        ref="datePickers"
        v-model="dateTimes"
        popper-class="custom-date-picker"
        type="datetime"
        placeholder="定时发送"
        value-format="YYYY-MM-DD HH:mm"
        time-format="HH:mm"
        @change="changeDateTimes"
        @visible-change="xiaoshi"
      />
    </div>
    <template #footer>
      <el-button :disabled="formLoading" type="primary" @click="submitForm">发 送</el-button>
    </template>
  </Dialog>

  <org-picker
    title="选择要转交的人员"
    ref="orgPicker"
    type="user"
    multiple
    :selected="[]"
    @ok="selected"
  />

  <el-drawer
    :size="isMobile ? '100%' : '560px'"
    direction="rtl"
    title="审批详情"
    v-model="processVisible"
    class="custom-detail-header"
  >
    <instance-preview
      v-if="processVisible"
      :instance-id="propObj.instanceId"
      :newUrl="propObj.newUrl"
      :noId="propObj.nodeId"
      @handler-after="handlerAfter"
    ></instance-preview>
  </el-drawer>
</template>

<script lang="ts" setup>
import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'
import { ChatDotRound, Bell } from '@element-plus/icons-vue'
import { defaultProps, handleTree } from '@/utils/tree'
import * as orderItApi from '@/api/system/orderIt'
import { Icon } from '@/components/Icon'
import OrgPicker from '@/components/common/OrgPicker.vue'

defineOptions({ name: 'SystemRoleAssignMenuForm' })
// 新的
import type { CheckboxValueType } from 'element-plus'
import dayjs from 'dayjs'
import request from '@/config/axios'

import { fa } from 'element-plus/es/locale'
import { log } from 'console'
const isMobile = computed(() => window.screen.width < 450)
const processVisible = ref(false)

const dingShiFs = ref(false)
const fenBieFs = ref(false)
const dateTimes = ref('')
const newTi = ref('')
const newTi1 = ref('')
const datePickers = ref(null)
// 定时发送
const dingShi = () => {
  console.log('触发')
  dingShiFs.value = !dingShiFs.value
  if (dingShiFs.value) {
    if (datePickers.value) {
      datePickers.value.focus()
    }
  } else {
    dateTimes.value = ''
  }
}
const changeDateTimes = (val) => {
  console.log(val)
  newTi.value = dayjs(val).format('M月D日')
  newTi1.value = val.substring(10)
  console.log(newTi)
  console.log(newTi1)
  console.log(dateTimes)
}

const xiaoshi = (val) => {
  console.log(val)
  if (!val) {
    console.log(dateTimes.value)
    if (!dateTimes.value) {
      dingShiFs.value = false
    }
  }
}

// 分别发送
const fenBie = () => {
  fenBieFs.value = !fenBieFs.value
}
const handleVisibleChange = (visible) => {
  if (!visible) {
    dingShiFs.value = false
  }
}

const selectValue = ref('1')
const inputValue = ref('')
const dateValue = ref('')
const dateValue1 = ref('')
const size = ref<'default' | 'large' | 'small'>('default')
const startTime = ref('')
const endTime = ref('')
const addressValue = ref('')
const textarea = ref('')
const selectList = ref([]) // 存储下拉框列表
const selectedValues = ref([]) // 存储选中的值
const placeholderText = ref('添加会议标题')
const checkboxValue = ref([])
const flag = ref(false) // 弹窗的是否展示
const isReminder = ref(false) // 弹窗的是否展示
const checked1 = ref(false)
const isButon = ref(false)
const isCloseButon = ref(false)
const repeatDateDialog = ref(false)
const count = ref(2)
const num = ref(1)
const repeatDateValue = ref('1')
const endRepetitionValue = ref('2')
const flagValue = ref('0')
const duration = ref('60分钟')
const actiove = ref(0)
const isWeekMonthYear = ref(0)
const selectiveRepeat = ref('1')
const belongToValue = ref('1')
const calendarValue = ref('1')
const remindMinutes = ref('')
const viUrl = ref('')

const dateOptions = ref([
  { id: 1, text: '一', isSelected: false },
  { id: 2, text: '二', isSelected: false },
  { id: 3, text: '三', isSelected: false },
  { id: 3, text: '四', isSelected: false },
  { id: 3, text: '五', isSelected: false },
  { id: 3, text: '六', isSelected: false },
  { id: 3, text: '日', isSelected: false }
])
const options = ref([
  { id: '0', name: '不再次提醒' },
  { id: '1', name: '5分钟后未读的人,会再DING一下' },
  { id: '2', name: '10分钟后未读的人,会再DING一下' },
  { id: '3', name: '15分钟后未读的人,会再DING一下' }
]) // 定义选项数组对象

const Uservalue = ref([])

const selected = (va) => {
  Uservalue.value = va.map((item, i) => {
    return {
      name: item.name,
      id: item.id
    }
  })
}
const delDept = (i) => {
  console.log(Uservalue.value, 'Uservalue.valueUservalue.value')

  Uservalue.value.splice(i, 1)
}

// 新的结束地方

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用

const formRef = ref() // 表单 Ref
const menuOptions = ref<any[]>([]) // 菜单树形结构
const menuExpand = ref(false) // 展开/折叠
const treeRef = ref() // 菜单树组件 Ref
const treeNodeAll = ref(false) // 全选/全不选
const propObj = ref({})
const newTenantId = reactive({
tenantId:''
})
/** 打开弹窗 */
// const open = async (row: RoleApi.RoleVO) => {
const open = async (val) => {
  console.log(val)
  propObj.value = val
  newTenantId.tenantId = val.tenantId
  dialogVisible.value = true
  dingShiFs.value = false
  fenBieFs.value = false
  textarea.value = ''
  newTi.value = ''
  newTi1.value = ''
  dateTimes.value = ''
  Uservalue.value = []
  remindMinutes.value = '0'
  getUrgent(val)
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

const getUrgent = async (val) => {
  console.log(val)

  // const data = await orderItApi.getUrgent({  instanceId: val.instanceId,nodeId: val.nodeId })
  const data = await orderItApi.getUrgent(val.instanceId, val.nodeId)
  Uservalue.value = []
  console.log(data)

  data.data.recipientList.map((item) => {
    Uservalue.value.push({
      name: item.userName,
      id: item.userId + ''
    })
  })
  textarea.value = data.data.content
  // if (data.remindFlag == 0) {
  //   dingShiFs.value = true
  // } else {
  //   dingShiFs.value = false
  // }

  // if (data.respSendFlag == 0) {
  //   fenBieFs.value = true
  // } else {
  //   fenBieFs.value = false
  // }

  viUrl.value = data.data.contentUrl
  console.log(data)
}
const dBans = async (val) => {
  console.log(val)
  return
  const data = await request.get({ url: val })
  propObj.value.newUrl = val
  processVisible.value = true
  dialogVisible.value = false

  console.log(data)
  // processVisible.value = true
}

const handlerAfter = async () => {
  processVisible.value = false
}

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  console.log(dateTimes.value)
  let newRecipientIds = []
  Uservalue.value.map((item) => {
    newRecipientIds.push(item.id)
  })
  try {
    const data = {
      // id: 7892,
      // initiaterId: 18686,
      dingType: '1',
      recipientIds: newRecipientIds,
      content: textarea.value,
      respSendFlag: fenBieFs.value ? '0' : '1',
      remindFlag: dingShiFs.value ? '0' : '1',
      remindDate: dateTimes.value,
      remindType: remindMinutes.value,
      contentUrl: viUrl.value
    }
    console.log(data)
    // return
    await orderItApi.createDing(data)
    // return
    message.success('操作成功')
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
  return

  // 校验表单
  if (!formRef) return
  // const valid = await formRef.value.validate()
  if (!valid) return
  // 提交请求
  formLoading.value = true
  try {
    const data = {
      menuIds: [
        ...(treeRef.value.getCheckedKeys(false) as unknown as Array<number>), // 获得当前选中节点
        ...(treeRef.value.getHalfCheckedKeys() as unknown as Array<number>) // 获得半选中的父节点
      ]
    }
    await PermissionApi.assignRoleMenu(data)
    message.success(t('common.updateSuccess'))
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}
</script>
<style lang="scss" scoped>
/* 去掉边框线的样式 */
:deep .no-border .el-select__wrapper {
  box-shadow: 0 0 0 0px !important;
}

.newEdit {
  display: flex;
  font-size: 14px;
  // color: #DCDFE6;
  height: 40px;
  line-height: 40px;
  // cursor: pointer;
  > div:nth-child(1):hover {
    background-color: #f3f4f7;
  }
  > div:nth-child(2):hover {
    background-color: #f3f4f7;
  }
  > div:nth-child(1) {
    display: flex;
    align-items: center;
    border-top: 1px solid #dcdfe6;
    border-left: 1px solid #dcdfe6;
    border-bottom: 1px solid #dcdfe6;
    width: 384px;
    text-align: center;
    border-radius: 40px 0 0 40px;
    padding-left: 10px;
    > div {
      margin-left: 6px;
    }
  }

  > div:nth-child(2) {
    display: flex;
    align-items: center;
    border: 1px solid #dcdfe6;
    width: 384px;
    text-align: center;
    border-radius: 0 40px 40px 0;
    padding-left: 10px;
    > div {
      margin-left: 6px;
    }
  }
  > div:nth-child(3) {
    border-top: 1px solid #1890ff;
    border-bottom: 1px solid #1890ff;
    border-right: 1px solid #1890ff;
    width: 150px;
    text-align: center;
  }
  > div:nth-child(4) {
    border-top: 1px solid #1890ff;
    border-bottom: 1px solid #1890ff;
    border-right: 1px solid #1890ff;
    width: 150px;
    border-radius: 0 4px 4px 0;
    text-align: center;
  }
}
:deep.xufu {
  position: relative;
  width: 100%;
  .imgs {
    position: absolute;
    left: 13px;
    top: 5px;
  }
  .el-textarea__inner {
    text-indent: 39px;
  }
  .imgs1 {
    position: absolute;
    bottom: 6px;
    right: 10px;
  }
}
.daiban {
  width: 280px;
  // height: 36px;
  border-radius: 4px;
  border: 1px solid #dcdfe6;
  display: flex;
  align-items: center;
  padding: 10px;
  height: 26px;
  > div {
    margin-left: 10px;
  }
}
</style>
