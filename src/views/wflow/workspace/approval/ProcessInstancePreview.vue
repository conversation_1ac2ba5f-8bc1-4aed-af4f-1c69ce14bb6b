<template>
  <el-tooltip class="item" effect="dark" content="再次提交" v-if="showButton">
    <icon name="el-icon-promotion" class="custom-el-icon-again" @click="handleAgain" />
  </el-tooltip>
  <el-tooltip class="item" effect="dark" content="打印">
    <icon name="el-icon-printer" class="custom-el-icon-printer" @click="print" />
  </el-tooltip>
  <el-tooltip class="item" effect="dark" content="分享">
    <img
      src="@/assets/image/share.svg"
      alt=""
      class="custom-el-icon-share"
      @click="selectUserNew"
    />
  </el-tooltip>

  <div v-loading="loading" class="preview">
    <div v-if="instanceData.instanceId">
      <div class="title">
        <div class="title-info">
          <div class="top-info">
            <div class="code">审批编号：{{ instanceData.instanceId }}</div>
          </div>
          <div class="name">
            <span>{{ instanceData.staterUser.name }}提交的{{ instanceData.processDefName }}</span>
            <el-tag size="small" :type="status.type">{{ status.text }}</el-tag>
          </div>
          <div class="code" style="margin-top: 6px !important">
            <span>{{ instanceData.tenantName }}</span>
          </div>
          <img v-if="status.img" :src="status.img" :style="isMobile ? 'right: -20px' : ''" />
        </div>
      </div>
      <div class="form">
        <form-render
          class="process-form"
          :config="instanceData.formConfig"
          :mode="isMobile ? 'MOBILE' : 'PC'"
          ref="form"
          :forms="instanceData.formItems"
          v-model="instanceData.formData"
        />
      </div>
      <div class="process">
        <process-progress
          :result="instanceData.result"
          :status="instanceData.status"
          :progress="instanceData.progress"
          :instanceId="newInstanceId"
          :tenantId="instanceData.tenantId"
          @success="withdrawOk"
        />
      </div>
    </div>
    <div class="actions">
      <div class="comment" @click="comment">
        <icon name="el-icon-chatlineround" />
        <div>评论</div>
      </div>
      <div v-if="instanceData.result === 'RUNNING'" style="display: flex">
        <template v-if="instanceData.operationPerm">
          <div v-if="activeTasks.length > 0 && showMore" class="action-more">
            <el-dropdown>
              <div class="comment">
                <icon name="el-icon-more"></icon>
                <div>更多</div>
              </div>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item v-if="opPerms?.transfer?.show" @click="handler('transfer')">
                    <icon name="iconfont icon-zhuanyi-16" />
                    {{ opPerms?.transfer?.alisa }}
                  </el-dropdown-item>
                  <el-dropdown-item v-if="opPerms?.recall?.show" @click="handler('recall')">
                    <icon name="el-icon-failed" />
                    {{ opPerms?.recall?.alisa }}
                  </el-dropdown-item>
                  <el-dropdown-item v-if="opPerms?.afterAdd?.show" @click="handler('afterAdd')">
                    <icon name="iconfont icon-zhaopinguanli" />
                    {{ opPerms?.afterAdd?.alisa }}
                  </el-dropdown-item>
                  <el-dropdown-item v-if="enableCancel" @click.native="handler('cancel')">
                    <icon name="el-icon-refreshleft" />
                    撤销
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
          <div v-if="activeTasks.length > 0" class="ok-refuse">
            <el-button v-if="opPerms?.refuse?.show" @click="handler('refuse')">
              {{ opPerms?.refuse?.alisa }}
            </el-button>
            <el-button type="primary" v-if="opPerms?.agree?.show" @click="handler('agree')">
              {{ opPerms?.agree?.alisa }}
            </el-button>
          </div>
        </template>
        <div class="comment" @click="handler('cancel')" v-else-if="enableCancel">
          <icon name="el-icon-refreshleft"></icon>
          <div>撤销</div>
        </div>
        <div class="comment" @click="cuiban">
          <icon name="el-icon-bell"></icon>
          <div>催办</div>
        </div>
      </div>
    </div>
    <Dialog v-model="printVisible" width="700px" title="打印预览">
      <template #title>
        <div>
          <span>打印预览</span>
          <el-radio-group
            style="margin: 0 30px"
            v-model="printCheck"
            @change="renderPrint"
            v-if="printTemplateConfig.customPrint"
          >
            <el-radio :label="false">默认模板</el-radio>
            <el-radio :label="true">自定义模板</el-radio>
          </el-radio-group>
        </div>
      </template>
      <div
        v-if="printCheck"
        id="printDom"
        ref="print"
        v-html="printTemplateConfig.printTemplate"
      ></div>
      <default-printer v-else ref="print" :status="status" :instance="instanceData" />
      <template #footer>
        <el-button @click="printVisible = false">取 消</el-button>
        <el-button type="primary" @click="doPrint">打 印</el-button>
      </template>
    </Dialog>
    <w-dialog
      v-model="actionVisible"
      :width="isMobile ? '100%' : '500px'"
      :title="actionDesc.title"
      okText="提 交"
      @ok="doAction"
      :needOther="
        (actionType == 'agree' || actionType == 'refuse') && nextInstanceId && nextNodeId
          ? true
          : false
      "
      otherText="确定并批阅下一份"
    >
      <process-action
        ref="action"
        @success="handlerOk"
        @fail="actionLoading = false"
        :form-data="formData"
        v-if="actionVisible"
        :active-tasks="activeTasks"
        :initiator="getInitiator"
        :instance="instanceData"
        :action="actionType"
        :action-desc="actionDesc"
        show-sign
      />
    </w-dialog>
    <!--  -->
    <createNewOne ref="createOne" />
    <org-picker
      title="选择转发"
      ref="orgPicker"
      multiple
      :selected="[]"
      :tenantId="tenantId"
      @ok="selected"
    />
  </div>

  <!--  选择转发联系人弹窗-->
  <el-dialog
    v-model="pickUserDialogVisible"
    title="选择联系人"
    width="800px"
    :close-on-click-modal="false"
    destroy-on-close
    class="pick-user-dialog"
  >
    <div class="container">
      <!-- Left Section -->
      <div class="w-1/2 border-r pr-4">
        <el-input
          v-model="pickUserSearch"
          placeholder="搜索名字、拼音、手机号..."
          prefix-icon="Search"
          clearable
          @input="handlePickUserSearch"
        />

        <div class="mt-4 space-y-2">
          <template v-if="!pickUserSearch">
            <div class="text-gray-500">输入关键字搜索联系人</div>
          </template>
          <template v-else-if="pickUserSearch && pickUserSearchResult.length > 0">
            <div class="space-y-2 max-h-96 overflow-y-auto hide-scrollbar">
              <div
                v-for="contact in pickUserSearchResult"
                :key="contact.id"
                class="p-2 rounded cursor-pointer hover:bg-gray-100 flex items-center"
                @click="handlePickUserSelect(contact)"
              >
                <el-radio :label="contact.id" v-model="pickUserSelectedList">
                  {{ ' ' }}
                </el-radio>
                <el-avatar :size="40" :src="contact.portrait" shape="square" />
                <div class="ml-3">
                  <div
                    class="font-medium"
                    v-html="highlightKeyword(contact.name, pickUserSearch)"
                  ></div>
                  <template v-if="contact.type === 1">
                    <div class="text-sm text-gray-500">{{ contact.postNames }}</div>
                    <div class="text-sm text-gray-500">{{ contact.objectName }}</div>
                  </template>
                  <template v-else>
                    <div class="text-sm text-gray-500">
                      包含：<span class="text-blue-500">{{ pickUserSearch }}</span>
                    </div>
                  </template>
                </div>
              </div>
            </div>
          </template>
          <template v-else>
            <div class="text-gray-500">sorry，没有搜索到相关联系人</div>
          </template>
        </div>
      </div>
      <el-divider direction="vertical" />

      <!-- Right Section -->
      <div class="w-1/2">
        <div class="text-gray-500 mb-4">最近聊天</div>
        <div class="space-y-2 max-h-96 overflow-y-auto hide-scrollbar">
          <div
            v-for="contact in pickUserRightList"
            :key="contact.id"
            @click="handlePickUserSelect(contact)"
            class="p-2 rounded cursor-pointer hover:bg-gray-100 flex items-center"
          >
            <el-radio :label="contact.id" v-model="pickUserSelectedList">
              {{ ' ' }}
            </el-radio>
            <el-avatar :size="40" :src="contact.portrait" shape="square" />
            <div class="ml-3">
              <div class="font-medium">{{ contact.name }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" @click="pickUserHandleConfirm" :disabled="!pickUserSelectedList"
          >确定
        </el-button>
        <el-button @click="pickUserDialogVisible = false">取消</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script>
import FormRender from '@/views/wflow/common/form/FormRender.vue'
import ProcessProgress from './ProcessProgress.vue'
import ProcessAction from './ProcessAction.vue'
import { getFormAndProcessProgress, processShare } from '@/api/processTask'
import { getCustomPrintConfig } from '@/api/process'
import moment from 'moment'
import { showFailToast } from 'vant'
import Print, { bindVar, getVal } from '@/utils/print'
import DefaultPrinter from './DefaultPrinter.vue'
import createNewOne from './createNewOne.vue'
import OrgPicker from '@/components/common/OrgPicker.vue'
import { useMyStore } from '@/store/modules/jump'
import { getTenantId } from '@/utils/auth'
import { searchUserFromDeptAndChatByName } from '@/api/org'
import { getUserConvs } from '@/api/message'
import { useWFlowStore } from '@/store/modules/wflow'
import { generateGroupAvatar, generateTextAvatar } from '@/utils/avatar'

export default {
  name: 'ProcessInstancePreview',
  components: {
    DefaultPrinter,
    FormRender,
    ProcessProgress,
    ProcessAction,
    createNewOne,
    OrgPicker
  },
  props: {
    instanceId: {
      type: String,
      required: true
    },
    nodeId: {
      type: String,
      required: false
    }
  },
  emits: ['handler-after'],
  data() {
    return {
      printVisible: false,
      actionVisible: false,
      cuiBanVisible: false,
      printCheck: false,
      loading: false,
      actionLoading: false,
      actionType: 'agree',
      instanceData: {},
      agreeVisible: false,
      printTemplateConfig: {
        customPrint: false,
        printTemplate: ''
      },
      tenantId: '',
      selectArr: [],
      newInstanceId: '',
      newNodeId: '',
      nextInstanceId: '',
      nextNodeId: '',
      showButton: false,

      //联系人弹窗相关的
      pickUserDialogVisible: false,
      //弹窗左侧搜索框
      pickUserSearch: '',
      //搜索结果列表
      pickUserSearchResult: [],
      //右侧的联系人列表(最近联系人)
      pickUserRightList: [],
      //选中的联系人，目前只能单选
      pickUserSelectedList: null
    }
  },
  computed: {
    loginUser() {
      return this.$wflow.loginUser
    },
    isRootUser() {
      return this.instanceData?.staterUser?.id === this.loginUser?.id
    },
    enableCancel() {
      try {
        return this.instanceData.externSetting.enableCancel
      } catch (e) {
        return false
      }
    },
    isMobile() {
      return window.screen.width < 450
    },
    opPerms() {
      const opPerms = this.instanceData.operationPerm || {}
      for (let key in opPerms) {
        if (!opPerms[key]) {
          opPerms[key] = { alisa: '', show: false }
        }
      }
      return opPerms
    },
    showMore() {
      return (
        this.opPerms?.transfer?.show ||
        this.opPerms?.recall?.show ||
        this.opPerms?.afterAdd?.show ||
        this.enableCancel
      )
    },
    formatFormData() {
      let val = {}
      getVal(this.instanceData.formData || {}, this.instanceData.formItems || [], val)
      val.ownerDept = this.instanceData.starterDept
      val.owner = this.instanceData.staterUser.name
      val.startTime = this.instanceData.startTime
      val.finishTime = this.instanceData.finishTime
      val.code = this.instanceData.instanceId
      return val
    },
    status() {
      let status = {
        text: this.instanceData.status
      }
      switch (this.instanceData.result) {
        case 'RUNNING':
        case 'COMPLETE':
          status.type = ''
          status.img = null
          break
        case 'PASS':
          status.type = 'success'
          status.img = this.$getLocalRes('agree.png')
          break
        case 'CANCEL':
          status.type = 'info'
          status.img = this.$getLocalRes('recall.png')
          break
        case 'REFUSE':
          status.type = 'danger'
          status.img = this.$getLocalRes('refuse.png')
          break
      }
      return status
    },
    activeTasks() {
      let tasks = []
      ;(this.instanceData.progress || []).forEach((task) => {
        if (task.users) {
          task.users.forEach((tk) => {
            if (
              tk.user &&
              tk.user.id == this.loginUser.id &&
              tk.taskId &&
              !this.$isNotEmpty(tk.finishTime)
            ) {
              tasks.push(tk)
            }
          })
        } else {
          if (
            task.user &&
            task.user.id == this.loginUser.id &&
            task.taskId &&
            !this.$isNotEmpty(task.finishTime)
          ) {
            tasks.push(task)
          }
        }
      })
      console.log(tasks, 'activeTasks-tasks')
      return tasks
    },
    getInitiator() {
      return (this.instanceData.progress || [])
        .filter((item) => item.name === '发起人' && item.user && Object.keys(item.user).length > 0)
        .map((item) => ({
          ...item.user,
          type: 'user'
        }))
    },
    formData() {
      //过滤出可编辑的表单字段
      let formFields = []
      let formData = {}
      this.getEnableEditForm(this.instanceData.formItems || [], formFields)
      formFields.forEach((k) => {
        formData[k] = this.instanceData.formData[k]
      })
      return formData
    },
    actionDesc() {
      switch (this.actionType) {
        case 'agree':
          return {
            tip: this.isRootUser ? '备注' : '审批意见',
            title: this.isRootUser ? '提交审批' : '同意审批'
          }
        case 'refuse':
          return { tip: '驳回意见', title: '拒绝审批' }
        case 'comment':
          return { tip: '评论内容', title: '添加评论' }
        case 'beforeAdd':
          return { tip: '加签意见', title: '前方增加审批人' }
        case 'afterAdd':
          return { tip: '加签意见', title: '后方增加审批人' }
        case 'transfer':
          return { tip: '转交意见', title: '转交给其他人审批' }
        case 'cancel':
          return { tip: '撤销原因', title: '撤销当前流程' }
        case 'recall':
          return { tip: '退回意见', title: '退回到之前节点' }
      }
    }
  },
  created() {
    this.newInstanceId = this.instanceId
    this.newNodeId = this.nodeId
    this.getInstanceData()
    this.getPrintConfig()
  },
  mounted() {},
  methods: {
    // 再次提交
    handleAgain() {
      this.$emit('handler-after')
      useMyStore().handleOpenForm(true)
      useMyStore().handleFormObj(this.instanceData)
    },
    // 分享
    selectUser() {
      this.$refs.orgPicker.optType = 'share'
      this.$refs.orgPicker.show()
    },
    selected(users) {
      this.selectArr = users
      this.submitShare()
    },
    // 分享
    submitShare() {
      this.loading = true
      let instanceId = this.newInstanceId
      processShare(instanceId, this.selectArr)
        .then((res) => {
          this.loading = false
          if (res.code == 0) {
            this.$message.success('转发成功')
          } else {
            this.$message.error(res.msg)
          }
        })
        .catch((err) => {
          this.loading = false
          this.$message.error('转发失败')
        })
    },
    // 催办
    cuiban() {
      let obj = {
        instanceId: this.newInstanceId,
        nodeId: this.newNodeId,
        tenantId: this.tenantId
      }
      this.$refs.createOne.open(obj)
    },
    //根据退回进行分段拆解
    splitByRecall(progress) {
      //提取分段时间点
      let points = progress
        .filter((p) => p.result === 'recall')
        .map((p) => new Date(p.startTime).getTime())
      if (points.length > 0) {
        let blocks = [] //分段流程块组
        let pointer = 0 //定点索引
        points.push(new Date().getTime())
        points.forEach((point) => {
          let block = []
          for (let i = pointer; i < progress.length; i++) {
            let startTime = new Date(progress[i].startTime).getTime()
            if (startTime <= point) {
              block.push(progress[i])
            } else {
              pointer = i
              break
            }
          }
          //存段
          blocks.push(block)
        })
        //按段处理
        let processNodes = []
        blocks.forEach((block) => processNodes.push(...this.mergeTask(block)))
        return processNodes
      }
      return this.mergeTask(progress)
    },
    //合并活动节点，此处执行一段合并算法用来处理退回导致节点重合的问题
    mergeTask(progress) {
      let merge = []
      progress.forEach((pg) => {
        // let i = merge.findIndex((n) => ( n.approvalMode == pg.approvalMode == 'AND'))
        let i = merge.findIndex(
          (n) =>
            (n.nodeId === pg.nodeId && n.startTime === pg.startTime) ||
            (n.nodeId === pg.nodeId &&
              n.approvalMode == pg.approvalMode &&
              !pg.finishTime &&
              !n.finishTime)
        )
        if (i > -1) {
          //存在则合并到对象
          if (merge[i].users) {
            //已经合并过了
            merge[i].finishTime = pg.finishTime
            merge[i].users.push(pg)
            merge[i].result = this.getApprovalResult(merge[i], pg, pg.approvalMode)
            merge[i].comment = merge[i].comment.concat(pg.comment)
            merge[i].approvalMode = pg.approvalMode
            merge[i].nodeType = pg.nodeType
          } else {
            //没有就合并
            merge[i] = {
              agree: null,
              name: pg.name,
              nodeType: pg.nodeType,
              approvalMode: pg.approvalMode,
              nodeId: pg.nodeId,
              result: this.getApprovalResult(merge[i], pg, pg.approvalMode),
              startTime: merge[i].startTime,
              finishTime: pg.finishTime,
              comment: merge[i].comment.concat(pg.comment),
              users: [merge[i], pg]
            }
            merge[i].comment = merge[i].comment.sort(
              (a, b) => moment(a.createTime) - moment(b.createTime)
            )
          }
        } else {
          if (!(!pg.result && pg.finishTime && pg.nodeType == 'APPROVAL')) {
            merge.push(pg)
          }
        }
      })
      return merge
    },
    getInstanceData() {
      this.instanceData.progress = []
      this.loading = true
      getFormAndProcessProgress(this.newInstanceId, this.newNodeId)
        .then((rsp) => {
          this.nextInstanceId = rsp.data.nextInstanceId
          this.nextNodeId = rsp.data.nextTaskDefKey
          this.tenantId = rsp.data.tenantId
          this.loading = false
          this.instanceData = rsp.data
          this.instanceData.progress = this.splitByRecall(rsp.data.progress)
          if (rsp.data.tenantId == getTenantId()) {
            this.showButton = true
          }
        })
        .catch((err) => {
          this.loading = false
          this.$err(err, '获取审批实例数据失败')
        })
    },
    getApprovalResult(oldVal, newVal, mode) {
      if (mode === 'OR') {
        return newVal.result ? newVal.result : oldVal.result
      } else if (mode === 'AND') {
        let rs = oldVal.result || newVal.result
        return rs === 'recall' ? 'recall' : rs
      } else {
        return newVal.result
      }
    },
    comment() {
      this.actionType = 'comment'
      this.actionVisible = true
    },
    handler(action) {
      this.actionType = action
      this.actionVisible = true
    },
    doAction(e) {
      if (this.actionType !== 'recall') {
        this.$refs.form.validate((valid) => {
          if (valid) {
            this.$refs.action.submitAction(e)
          } else {
            this.actionVisible = false
            if (this.isMobile) {
              showFailToast('请完善表单')
            } else {
              this.$message.warning('请完善表单')
            }
          }
        })
      } else {
        this.$refs.action.submitAction(e)
      }
    },
    print() {
      this.printVisible = true
    },
    doPrint() {
      Print(this.printCheck ? this.$refs.print : this.$refs.print.$el)
    },
    scanQr() {
      this.$message.warning('敬请期待')
    },
    handlerOk(e) {
      this.actionVisible = false
      // return
      if (e == 'otherEvent') {
        this.newInstanceId = this.nextInstanceId
        this.newNodeId = this.nextNodeId
        this.getInstanceData()
        this.getPrintConfig()
        this.$emit('handler-after', 'next')
      } else {
        if (this.actionType == 'comment') {
          this.getInstanceData()
          this.getPrintConfig()
        } else {
          this.getInstanceData()
          this.getPrintConfig()
          this.$emit('handler-after')
        }
      }
    },
    withdrawOk() {
      this.getInstanceData()
      this.getPrintConfig()
    },
    getEnableEditForm(forms, fields) {
      forms.forEach((f) => {
        if (f.name === 'SpanLayout') {
          this.getEnableEditForm(f.props.items, fields)
        } else if (f.name === 'TableList') {
          fields.push(f.id)
        } else if (f.perm === 'E') {
          fields.push(f.id)
        }
      })
    },
    getPrintConfig() {
      getCustomPrintConfig(this.newInstanceId)
        .then((rsp) => {
          this.printTemplateConfig = rsp.data
        })
        .catch((err) => {
          this.$err(err, '获取打印模板配置失败')
        })
    },
    renderPrint(val) {
      if (val) {
        this.$nextTick(() => {
          bindVar(this.printTemplateConfig.printTemplate, this.formatFormData, 'printDom')
        })
      }
    },
    selectUserNew() {
      // 清空旧数据
      this.pickUserSearch = ''
      this.pickUserSelectedList = null
      this.pickUserSearchResult = []
      try {
        this.loading = true
        // 加载右侧联系人:目前用的是消息列表的最近联系人，后续可能使用单独的接口
        getUserConvs({ userId: useWFlowStore().loginUser.id }).then((rsp) => {
          this.pickUserRightList = []
          const promises = [] // 用于存储所有异步操作的 Promise

          rsp.data.convList.forEach((item) => {
            if (item.groupInfo) {
              // 解析 extra 字段
              const extra = JSON.parse(item.groupInfo.extra)
              // 提取 groupMemberList 并转换为目标格式
              const groupMemberList = extra.groupMemberList.map((member) => ({
                name: member.alias, // 使用 alias 作为 name
                avatar: member.avatar || '' // 如果 avatar 为空，设置为空字符串
              }))

              // 保存当前索引
              const index = this.pickUserRightList.length

              // 生成群头像并插入到正确的位置
              const promise = generateGroupAvatar(groupMemberList).then((dataUrl) => {
                item.groupInfo.portrait = dataUrl
                let { target_id, type, portrait, name } = item.groupInfo
                this.pickUserRightList[index] = { id: target_id, name, portrait, type }
              })

              promises.push(promise)
            } else if (item.target) {
              // 如果 target 存在，从 target 中提取字段
              let { userId, type, displayName, portrait } = item.target
              //如果没有头像，则生成文字头像
              if (!portrait) {
                portrait = generateTextAvatar(displayName)
              }
              this.pickUserRightList.push({ id: userId, name: displayName, portrait, type })
            }
          })

          // 等待所有异步操作完成后再显示弹窗
          Promise.all(promises).then(() => {
            this.pickUserDialogVisible = true
          })
        })
      } catch (e) {
        console.error(e)
      } finally {
        this.loading = false
      }
    },

    handlePickUserSearch() {
      // 如果已经有定时器，清除之前的定时器
      if (this.debounceTimer) {
        clearTimeout(this.debounceTimer)
      }

      // 设置新的定时器，延迟执行搜索
      this.debounceTimer = setTimeout(() => {
        this.executeSearch()
      }, 600)
    },

    executeSearch() {
      this.loading = true
      let userName = this.pickUserSearch.trim()
      if (userName === '') {
        this.loading = false
        return
      }
      this.pickUserSearchResult = []

      searchUserFromDeptAndChatByName({
        keyWord: userName
      })
        .then((rsp) => {
          // 处理头像
          const promises = rsp.data.map((item) => {
            // 如果 portrait 有值，直接使用
            if (item.portrait) {
              return Promise.resolve(); // 不需要异步操作，返回一个已解决的 Promise
            }

            // 如果 portrait 为空
            if (item.type === 1) {
              // 单个用户：使用 name 生成文字头像
              item.portrait = generateTextAvatar(item.name);
              return Promise.resolve(); // 不需要异步操作，返回一个已解决的 Promise
            } else if (item.type === 2) {
              // 群组：生成群聊头像
              try {
                // 检查 extra 是否存在且是有效的 JSON 字符串
                if (!item.extra || typeof item.extra !== 'string') {
                  throw new Error('extra 字段无效');
                }

                const extra = JSON.parse(item.extra);

                const groupMemberList = extra.map((member) => ({
                  name: member.alias, // 使用 alias 作为 name
                  avatar: member.avatar || '' // 如果 avatar 为空，设置为空字符串
                }));

                return generateGroupAvatar(groupMemberList).then((dataUrl) => {
                  item.portrait = dataUrl; // 更新群组头像
                });
              } catch (error) {
                console.error('解析 extra 失败:', error);
                item.portrait = ''; // 提供一个默认的头像
                return Promise.resolve(); // 返回一个已解决的 Promise，避免中断流程
              }
            } else {
              // 其他类型（如果有）
              item.portrait = ''; // 提供一个默认的头像
              return Promise.resolve(); // 返回一个已解决的 Promise
            }
          });

          // 等待所有异步操作完成
          return Promise.all(promises).then(() => {
            this.pickUserSearchResult = rsp.data; // 更新搜索结果
          });
        })
        .catch((err) => {
          this.$err(err, '获取用户信息失败');
          console.log('获取用户信息失败', err);
        })
        .finally(() => {
          this.loading = false;
        });
    },
    handlePickUserSelect(contact) {
      //把 id 放到选中列表
      this.pickUserSelectedList = contact.id
      //根据原来的方法组装一个对象
      let targetUser = {
        id: contact.id,
        name: contact.name,
        type: 'user'
      }
    //  this.selectArr.push(targetUser)// 多选可用
      this.selectArr = [targetUser]; // 单选可用,让 selectArr 始终只有一个选中的对象
  //    console.log('this.selectArr', this.selectArr)
    },
    highlightKeyword(text, keyword) {
      if (!text || !keyword) return text
      // 使用正则表达式全局匹配关键字，并忽略大小写
      const regex = new RegExp(`(${keyword})`, 'gi')
      // 将匹配到的关键字替换为带有蓝色样式的 span 标签
      return text.replace(regex, '<span class="text-blue-500">$1</span>')
    },

    pickUserHandleConfirm() {
      this.loading = true
      let instanceId = this.newInstanceId
      processShare(instanceId, this.selectArr)
        .then((res) => {
          this.loading = false
          if (res.code == 0) {
            this.pickUserDialogVisible = false
            this.$message.success('转发成功')
          } else {
            this.$message.error(res.msg)
          }
        })
        .catch((err) => {
          this.loading = false
          this.$message.error('转发失败')
          console.log(err)
        })
    }
  }
}
</script>

<style lang="less" scoped>
.preview {
  background: #f3f4f7;
  position: relative;
  height: 100%;

  & > div:nth-child(1) {
    overflow-y: auto;
    height: 100%;
    //padding-bottom: 50px;
  }

  .actions {
    height: 70px;
    padding: 10px 15px;
    position: absolute;
    width: 100%;
    bottom: 0;
    left: 0;
    z-index: 99;
    background: white;
    /* border-top: 1px solid #dcdfe6; */
    /* box-shadow: 0 0 15px 0px #dad8d8; */
    display: flex;
    box-sizing: border-box;
    border-top: 0.625rem solid #f3f4f7;

    .ok-refuse {
      position: absolute;
      right: 15px;
      top: 14px;
    }

    .comment {
      text-align: center;
      font-size: 13px;
      color: #000;
      margin-right: 25px;

      :deep(.icon) {
        font-size: 18px;
      }

      &:hover {
        color: @theme-primary;
      }
    }
  }
}

.process {
  background: white;
  padding: 15px 15px 50px;
}

.title {
  background: white;
  // display: flex;
  flex-direction: row;
  flex-wrap: nowrap;
  position: relative;
  padding: 15px 15px;
  margin-bottom: 10px;

  .leftAvatar {
    margin-right: 12px;
  }

  .title-info {
    .name {
      font-size: 16px;
      color: #303133;
      font-weight: bold;
      margin-top: 8px;
      display: flex;
      align-items: center;

      .avatar {
        display: inline-block;
        margin-right: 4px;
      }

      .el-tag {
        // margin-top: -4px;
        margin-left: 8px;
      }
    }

    .top-info {
      display: flex;
      justify-content: space-between;
      align-items: center;
      color: #898989;
    }

    .code {
      font-size: 12px;
      // margin-top: 10px;
      color: #898989;
      position: relative;
    }

    img {
      width: 80px;
      height: 80px;
      position: absolute;
      right: 10px;
      bottom: -30px;
      z-index: 1;
    }
  }

  .extend-options {
    position: absolute;
    right: 0;
    top: 0;

    :deep(.icon) {
      cursor: pointer;
      font-size: 16px;

      &:hover {
        color: @theme-primary;
      }
    }
  }
}

.form {
  // margin: 15px 0;
  // padding: 10px 10px 1px 10px;
  // background: white;
}

:deep(.title .avatar .name) {
  display: none;
}

// :deep(.title .avatar .a-img) {
//   border-radius: 0;
// }

// :deep(.title .avatar .a-img>div) {
//   border-radius: 8px;
// }

.tag-my {
  border-radius: 10px;
  background: #fff;
}

:deep(.el-dropdown) {
  line-height: normal !important;
}

:deep(.el-dropdown-menu__item i) {
  margin-right: 0;
}

.custom-el-icon-share {
  position: fixed;
  top: 59px;
  right: 50px;
  cursor: pointer;
  font-size: 16px;
  width: 16px;
}

.custom-el-icon-printer {
  position: fixed;
  top: 60px;
  right: 85px;
  cursor: pointer;
  font-size: 16px;
  color: #72767b;
}

.custom-el-icon-again {
  position: fixed;
  top: 59px;
  right: 120px;
  cursor: pointer;
  font-size: 16px;
  width: 16px;
  color: #72767b;
}

.dialog-footer {
  display: flex;
  justify-content: flex-start;
  margin-top: 15px;
  gap: 12px;
}

:deep(.el-dialog__body) {
  padding-top: 20px;
}

.container {
  max-height: 600px;
  min-height: 300px;
  display: flex;
  align-items: stretch; /* 让子元素高度一致 */
  height: 100%; /* 确保父容器有高度 */
}

.mb-4 {
  //字体加粗
  font-weight: bold;
}

/* 隐藏滚动条 */
.hide-scrollbar::-webkit-scrollbar {
  display: none;
}

.hide-scrollbar {
  scrollbar-width: none;
  -ms-overflow-style: none;
}
</style>
