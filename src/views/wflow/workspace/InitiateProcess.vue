<template>
  <div v-loading="loading" v-loading.fullscreen.lock="fullscreenLoading">
    <el-row :gutter="20">
      <el-col :span="15" style="border-right: 1px solid #e8e8e8">
        <div v-if="userDepts.length > 1" style="margin-bottom: 10px">
          本次发起部门：
          <el-radio-group v-model="userDeptId">
            <el-radio :label="dept.id" :key="dept.id" v-for="dept in userDepts"
            >{{ dept.name }}
            </el-radio>
          </el-radio-group>
        </div>
        <!--渲染表单-->
        <form-render
          v-if="!loading"
          class="process-form"
          ref="form"
          mode="PC"
          :forms="forms"
          v-model="formData"
          :config="form.formConfig"
        />
      </el-col>
      <el-col :span="9">
        <!--渲染执行流程-->
        <process-render
          @render-ok="$emit('render-ok')"
          ref="process"
          v-if="!loading && userDeptId"
          :process-def-id="form.processDefId"
          :dept-id="userDeptId"
          v-model="processUsers"
          :forms="forms"
          :formData="formData"
          :process="process"
        />
      </el-col>
    </el-row>
  </div>
</template>

<script>
import ProcessRender from '../process/ProcessRender.vue'
import FormRender from '@/views/wflow/common/form/FormRender.vue'
import {getUserDepts} from '@/api/org'
import {getInstanceFormData} from '@/api/processTask'
import {getModelById} from '@/api/modelGroup'
import {startProcess, saveUserSign} from '@/api/process'

export default {
  name: 'InitiateProcess',
  components: {
    FormRender,
    ProcessRender
  },
  props: {
    code: {
      type: String,
      required: true
    },
    instanceId: {
      type: String,
      required: false
    }
  },
  data() {
    return {
      loading: false,
      formData: {},
      processUsers: {},
      userDepts: [],
      userDeptId: null,
      form: {
        formId: '',
        formName: '',
        logo: {},
        formItems: [],
        process: {},
        remark: ''
      },
      fullscreenLoading: false,
      isFirst: true,
      autoSaveInterval: null, // 定时器ID
      autoSaveRate: 5000 // 定时器时间间隔
    }
  },
  watch: {
    formData: {
      handler(newVal, oldVal) {
        console.log('formData changed:', newVal, 'Old value:', oldVal);
        localStorage.setItem(this.code + 'autoSaveFormData', JSON.stringify(this.formData))
        this.$emit('auto-save-ok', this.formData)
      },
      deep: true
    }
  },
  mounted() {
    localStorage.setItem('signConfig', '')
    localStorage.setItem('signPath', '')
    this.loadFormInfo(this.code)
    this.getUserDept()
  },

  computed: {
    forms() {
      return this.$wflow.design.formItems
    },
    process() {
      return this.$wflow.design.process
    }
  },
  methods: {
    getUserDept() {
      console.log(this.$wflow.loginUser)
      getUserDepts(this.$wflow.loginUser.id)
        .then((rsp) => {
          this.userDepts = rsp.data
          if (this.userDepts.length > 0) {
            this.userDeptId = this.userDepts[0].id
          }
        })
        .catch((err) => {
          this.$err(err, '获取用户部门信息失败')
        })
    },
    getDraftData(e) {
      for (let key in this.formData) {
        this.formData[key] = e[key]
      }
    },
    loadFormData() {
      if (this.$isNotEmpty(this.instanceId)) {
        getInstanceFormData(this.instanceId)
          .then((rsp) => {
            for (let key in this.formData) {
              this.formData[key] = rsp.data[key]
            }
          })
          .catch((e) => {
            this.$err(e, '加载原始数据失败')
          })
      }
    },
    loadFormInfo(id) {
      this.loading = true;
      const localFormData = localStorage.getItem(this.code + 'autoSaveFormData');
      console.log('未提交的数据：', localFormData);

      // 检查 localFormData 是否为 null
      if (localFormData !== null) {
        // 将 localFormData 转换为对象
        const parsedLocalFormData = JSON.parse(localFormData);

        // 获取 parsedLocalFormData 的所有值
        const parsedLocalFormDataValues = Object.values(parsedLocalFormData);

        // 过滤掉空字符串，如果手动删除了某个值，则该值为空字符串。
        const filteredValues = parsedLocalFormDataValues.filter(value => value !== '');

        // 检查 filteredValues 是否为空
        if (filteredValues.length > 0) {
          this.$confirm('检测到有未提交的表单数据，是否恢复？', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(() => {
            this.formData = parsedLocalFormData;
          });
        } else {
          this.formData = {};
        }
      } else {
        this.formData = {};
      }

      getModelById(id)
        .then((rsp) => {
          console.log('1');
          this.loading = false;
          let form = rsp.data;
          form.logo = JSON.parse(form.logo);
          this.form = form;
          // 构建表单及校验规则
          this.$wflow.design = form;
          this.loadFormData();
        })
        .catch((err) => {
          this.loading = false;
          this.$err(err, '获取流程模型失败');
        });
    },
    validate(call) {
      this.$refs.form.validate((validForm) => {
        this.$refs.process.validate((validProcess) => {
          call(validForm, validProcess)
        })
      })
    },
    validateCondition(call) {
        this.$refs.process.validateCondition((conditionFlag) => {
          call(conditionFlag)
        })
    },
    submit() {
      if (!this.isFirst) {
        return
      }
      this.isFirst = false
      // this.fullscreenLoading = true
      let startParams = {
        deptId: this.userDeptId,
        formData: this.formData,
        processUsers: this.processUsers
      }
      startProcess(this.form.processDefId, startParams)
        .then((rsp) => {
          if (
            localStorage.getItem('signConfig') == 1 &&
            localStorage.getItem('signPath') != 'null'
          ) {
            saveUserSign({
              sign: localStorage.getItem('signPath'),
              id: this.$wflow.loginUser.id
            }).then((res) => {
              if (res.code != 0) {
                this.$message.success(res.msg)
              }
            })
          }

          this.$message.success('提交成功')
          this.$emit('ok')
          // this.fullscreenLoading = false
          this.isFirst = true

          // 清空本地存储
          localStorage.removeItem(this.code + 'autoSaveFormData')
        })
        .catch((err) => {
          // this.fullscreenLoading = false
          this.isFirst = true
          this.$emit('fail')
          this.$err(err, '发起审批失败')
        })
    },
    // 保存草稿
    submitDraft() {
      if (!this.isFirst) {
        return
      }
      this.isFirst = false
      this.$emit('draft', this.formData)
      this.isFirst = true

      // 清空本地存储
      localStorage.removeItem(this.code + 'autoSaveFormData')
    }
  },
  emits: ['ok', 'fail', 'render-ok', 'draft', 'auto-save-ok']
}
</script>

<style lang="less" scoped>
.process-form {
  :deep(.el-form-item__label) {
    padding: 0 0;
  }
}
</style>
