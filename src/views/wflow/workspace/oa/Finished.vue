<template>
  <div v-loading="loading" class="contentChild">
    <el-form :inline="true">
      <el-form-item label="类型">
        <el-select
          v-model="approvalTypes"
          placeholder="请选择类型"
          filterable
          clearable
          remote
          :remote-method="hanlderNameFilterChange"
          remote-show-suffix
          multiple
          :loading="nameFilterLoading"
          class="!w-240px"
        >
          <el-option
            v-for="(item,findex) in filterNameList"
            :key="item.formId"
            :label="item.formName"
            :value="item.formName"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="发起人">
        <el-input
          placeholder="请输入发起人"
          clearable
          class="!w-240px"
          @keyup.enter="handleChange"
          v-model="startUser"
          @clear="handleChange"
        />
      </el-form-item>
      <el-form-item label="公司名称">
        <el-select
          v-model="params.companyName"
          placeholder="请输入公司名称"
          filterable
          clearable
          class="!w-240px"
        >
          <el-option
            v-for="item in companyList"
            :key="item.id"
            :label="item.name"
            :value="item.name"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="任务开始时间">
        <el-date-picker
          v-model="startTime"
          value-format="YYYY-MM-DD "
          type="daterange"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          class="!w-240px"
          @change="selectStart"
        />
      </el-form-item>
      <el-form-item label="处理完成时间">
        <el-date-picker
          v-model="finishTime"
          value-format="YYYY-MM-DD "
          type="daterange"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          class="!w-240px"
          @change="selectEnter"
        />
      </el-form-item>
      <el-form-item label="内容检索" prop="createTime">
        <el-input
          placeholder="请输入"
          clearable
          class="!w-240px mr-10px"
          @keyup.enter="handleChange"
          v-model="content"
          @clear="handleChange"
        />
      </el-form-item>
      <el-form-item>
        <el-button icon="el-icon-search" @click="handleChange">搜索</el-button>
        <el-button
            @click="handleDown"
            icon="el-icon-download"
            type="success"
            plain
            v-loading.fullscreen.lock="loadingAll"
        >导出
        </el-button>
      </el-form-item>
    </el-form>

    <el-table :data="dataList" @row-click="showProcess">
      <el-table-column
        fixed
        prop="processDefName"
        label="审批类型"
        show-overflow-tooltip
        min-width="120px"
      >
        <template v-slot="scope">
          <el-tag
            size="small"
            type="success"
            v-if="scope.row.superInstanceId !== scope.row.instanceId"
            >子
          </el-tag>
          <span style="margin-left: 5px">{{ scope.row.processDefName }}</span>
        </template>
      </el-table-column>
      <el-table-column fixed prop="content" label="摘要" min-width="200px">
        <template #default="scope">
          <el-tooltip placement="right" popper-class="custom-popper-class">
            <template #content>
              <div v-html="tooltipContent(scope.row.content)" class="customTooltip"></div>
            </template>
            <div class="contentScope">{{ scope.row.content }}</div>
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column prop="owner" show-overflow-tooltip label="发起人" min-width="100px">
        <template v-slot="scope">
          <avatar
            :size="26"
            :name="scope.row.owner ? scope.row.owner.name : ''"
            :src="scope.row.owner ? scope.row.owner.avatar : ''"
            class="custom-avatar"
          />
        </template>
      </el-table-column>
      <el-table-column show-overflow-tooltip prop="tenantName" label="公司名称" min-width="140px" />
      <el-table-column show-overflow-tooltip prop="taskName" label="任务节点名" />
      <el-table-column
        show-overflow-tooltip
        prop="taskCreateTimeStr"
        label="任务开始时间"
        min-width="120px"
      ></el-table-column>
      <el-table-column
        show-overflow-tooltip
        prop="taskEndTimeStr"
        label="处理完成时间"
        min-width="120px"
      ></el-table-column>
      <el-table-column show-overflow-tooltip prop="duration" label="处理耗时" min-width="120px">
        <template v-slot="scope">
          {{ scope.row.execTime ? scope.row.execTime : this.getDuration(scope.row) }}
        </template>
      </el-table-column>
      <el-table-column prop="status" label="处理结果">
        <template v-slot="scope">
          <el-tag :type="getTaskResult({ result: scope.row.taskResult }).type">
            {{ getTaskResult({ result: scope.row.taskResult }).text }}
          </el-tag>
        </template>
      </el-table-column>
    </el-table>
    <div>
      <el-pagination
        background
        :page-sizes="[10, 20, 30, 50, 100]"
        layout="total, sizes,prev, pager, next,jumper"
        :total="total"
        :page-size="params.pageSize"
        v-model:current-page="params.pageNo"
        @size-change="handleSizeChange"
      ></el-pagination>
    </div>
    <el-drawer
      :size="isMobile ? '100%' : '560px'"
      direction="rtl"
      title="审批详情"
      :z-index="1000"
      v-model="processVisible"
      class="custom-detail-header"
    >
      <instance-preview
        v-if="processVisible"
        :instance-id="selectInstance.instanceId"
        @handler-after="handlerAfter"
      ></instance-preview>
    </el-drawer>
  </div>
</template>

<script>
import taskApi from '@/api/processTask'
import moment from 'moment'
import InstancePreview from '../approval/ProcessInstancePreview.vue'
import { getTaskResult } from '@/utils/ProcessUtil.js'
import { useMyStore } from '@/store/modules/jump'
import download from "@/utils/download";

export default {
  name: 'Finished',
  components: { InstancePreview },
  data() {
    return {
      total: 0,
      params: {
        pageSize: 10,
        pageNo: 1,
        finished: null,
        code: '',
        companyName: ''
      },
      openItemDl: false,
      selectInstance: {},
      loading: false,
      processVisible: false,
      formList: [],
      dataList: [],
      searchInput: '',
      // approvalType: '',
      startUser: '',
      content: '',
      // companyName: '',
      startTime: [],
      finishTime: [],
      typeList: [],
      companyList: [],
      approvalTypes: [],
      filterNameList:[],
      nameFilterLoading:false,
      loadingAll: false,
    }
  },
  computed: {
    isMobile() {
      return window.screen.width < 450
    },
    launchSuccess() {
      const store = useMyStore()
      return store.launchSuccess
    }
  },
  watch: {
    params: {
      deep: true,
      handler() {
        this.getIdoList()
      }
    },
    launchSuccess(newValue, oldValue) {
      if (newValue) {
        this.getIdoList()
      }
    }
  },
  mounted() {
    this.getGroupModelList()
    this.getIdoList()
    this.groupTenantList()
  },
  methods: {
    hanlderNameFilterChange(key){
      if(key){
        this.nameFilterLoading = true
        setTimeout(() => {
          this.nameFilterLoading = false
          this.filterNameList = this.typeList.filter(e=>{
          return e.formName.includes(key)
        })
        }, 100);
      }else{
        this.filterNameList = this.typeList
      }
    },
    tooltipContent(content) {
      return content.replace(/\n/g, '<br/>')
    },
    //  类型下拉
    getGroupModelList() {
      taskApi.groupModelList({tenantFlag:true}).then((res) => {
        this.typeList = res.data
      })
    },
    // 公司名称下拉
    groupTenantList() {
      taskApi.groupTenantList().then((res) => {
        console.log(res, 'sss')
        this.companyList = res.data
      })
    },
    handleSizeChange(val) {
      this.params.pageSize = val
    },
    handleChange() {
      if (this.params.pageNo == 1) {
        this.getIdoList()
      } else {
        this.params.pageNo = 1
      }
    },
    selectEnter(v) {
      // if (!v) {
      this.getIdoList()
      // }
    },
    selectStart(v) {
      // if (!v) {
      this.getIdoList()
      // }
    },
    getTaskResult,
    handleDown() {
      //加类型限制，以防导出数据过大，卡死
      if (!this.approvalTypes || this.approvalTypes.length === 0) {
        return this.$message.warning('请选择流程类型')
      }
      this.loadingAll = true
      let query = {
        ...this.params,
        value: this.searchInput,
        approvalTypes: this.approvalTypes.join(';'),
        startUser: this.startUser,
        content: this.content,
        companyName: this.companyName,
        startTime: this.startTime && this.startTime.length > 0 ? this.startTime.join(',') : '',
        finishTime: this.finishTime && this.finishTime.length > 0 ? this.finishTime.join(',') : ''
      }
      taskApi
          .processIdoExport(query)
          .then((data) => {
            this.loadingAll = false
            download.excel(
                data,
                (this.approvalTypes && this.approvalTypes.length > 0 ? this.approvalTypes.join('、') : '审批流程') + '_导出数据.xlsx'
            )
          })
          .catch((e) => {
            this.loadingAll = false
          })
    },
    getIdoList() {
      this.loading = true
      let query = {
        ...this.params,
        value: this.searchInput,
        approvalTypes: this.approvalTypes.join(';'),
        startUser: this.startUser,
        content: this.content,
        companyName: this.companyName,
        startTime: this.startTime && this.startTime.length > 0 ? this.startTime.join(',') : '',
        finishTime: this.finishTime && this.finishTime.length > 0 ? this.finishTime.join(',') : ''
      }
      taskApi
        .getIdoList(query)
        .then((rsp) => {
          this.loading = false
          this.total = rsp.data.total
          this.dataList = rsp.data.records
        })
        .catch((e) => {
          this.loading = false
        })
    },
    submitForm() {
      this.$refs.processForm.validate((validForm, validProcess) => {
        if (!this.isMobile) {
          if (validForm && validProcess) {
            this.$refs.processForm.submit()
          } else {
            this.$message.warning('请完善表单/流程选项😥')
          }
        }
      })
    },
    showProcess(row) {
      this.processVisible = true
      this.selectInstance = row
    },
    getDuration(row) {
      // let end = this.$isNotEmpty(row.taskEndTime)
      //   ? row.taskEndTime
      //   : moment().format('YYYY-MM-DD HH:mm:ss')
      // return this.$timeCoverStr(row.createTime, end)

      const startDate = new Date(row.taskCreateTimeStr)
      const endDate = row.taskEndTime ? new Date(row.taskEndTime) : new Date()
      const timeDifference = endDate - startDate

      const seconds = Math.floor((timeDifference / 1000) % 60)
      const minutes = Math.floor((timeDifference / 1000 / 60) % 60)
      const hours = Math.floor((timeDifference / (1000 * 60 * 60)) % 24)
      const days = Math.floor(timeDifference / (1000 * 60 * 60 * 24))

      const parts = []
      if (days > 0) parts.push(`${days}天`)
      if (hours > 0) parts.push(`${hours}小时`)
      if (minutes > 0) parts.push(`${minutes}分钟`)
      if (seconds > 0 || parts.length === 0) parts.push(`${seconds}秒`)
      // if (minutes > 0 || parts.length === 0) parts.push(`${minutes}分钟`)
      return parts.join('')
    },
    handlerAfter() {
      this.processVisible = false
      this.getIdoList()
    }
  }
}
</script>

<style scoped lang="less">
.contentChild {
  background: #fff;
  padding: 20px;
}

.el-pagination {
  justify-content: right;
  margin-top: 20px;
}

.searchBox {
  .el-form-item {
    margin-right: 10px;
  }
}

.textTi {
  margin-right: 10px;
  font-size: 14px;
  color: #606266;
}

/deep/ .textTi .el-select__placeholder.is-transparent {
  color: #333 !important;
}

/deep/ .custom-detail-header .el-drawer.ltr,
.el-drawer.rtl {
  height: auto !important;
}

.contentScope {
  white-space: pre-wrap;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 5;
  overflow: hidden;
  text-overflow: ellipsis;
}

:deep(.custom-avatar .a-img > div) {
  font-size: 10px !important;
}
</style>
<style lang="less">
.customTooltip {
  min-width: 200px;
  max-width: 400px;
  white-space: pre-wrap;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 20;
  overflow: hidden;
  text-overflow: ellipsis;
}

.custom-popper-class {
  background: #45484a;
}

.custom-popper-class .el-popper__arrow::before {
  border: 1px solid #45484a;
  background: #45484a;
}
</style>
