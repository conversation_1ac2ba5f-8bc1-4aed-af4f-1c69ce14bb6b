<template>
  <div v-loading="loading" class="formsBox openItemDiv" v-if="openItemDl"
    :class="{ 'isOaClass': isOA, 'isFormClass': type == 'forms' }">
    <div class="form-topItem">
      <el-button @click="goBack" icon="el-icon-arrowleft" link />
      <ul class="openItem-tab-list">
        <li v-for="(tab, index) in tabList" :key="index" @click="activateTab(index)"
          :class="{ active: activeTab === index }">
          {{ tab.title }}{{ index == 0 ? '-' + (selectInstance.processDefName || selectInstance.formName) : '' }}
        </li>
      </ul>
    </div>
    <div class="form-contentItem">
      <initiate-process ref="processForm" :node-id="selectInstance.nodeId" :instance-id="selectInstance.instanceId"
        :code="selectInstance.formId" v-show="openItemDl && activeTab == 0" @ok="processOk" @draft="getDraft" @autoSaveOk="autoSaveOkHandler" />
      <processList ref="processListRef" v-show="activeTab == 1"></processList>
      <draftsList ref="draftsListRef" @edit="getEdit" v-show="activeTab == 2"></draftsList>
      <div class="content-footer" v-show="activeTab == 0">
        <el-button type="primary" @click="submitForm">提 交</el-button>
        <el-button @click="draftForm">保存草稿</el-button>
        <span
          style="color: red;font-size: 12px;margin-left: 10px;"
          v-if="lastAutoSaveTime">已自动保存于：{{ lastAutoSaveTime }}</span>
      </div>
    </div>
  </div>
</template>
<script>
import InitiateProcess from '../InitiateProcess.vue'
import processList from './myProcess.vue'
import draftsList from './myDrafts.vue'
import taskApi from '@/api/processTask'
import { useMyStore } from '@/store/modules/jump'
export default {
  props: {
    isOA: false,
    type: ''
  },
  components: {
    InitiateProcess,
    processList,
    draftsList,
  },
  data() {
    return {
      tabList: [
        { title: '发起', content: '1' },
        { title: '查看数据', content: '2' },
        { title: '草稿箱', content: '3' }
      ],
      activeTab: 0,
      openItemDl: false,
      selectInstance: {},
      loading: false,
      processVisible: false,
      draftId: '',
      submitLoading: false,
      lastAutoSaveTime: ''
    }
  },
  computed: {
    launchSuccess() {
      const store = useMyStore()
      return store.launchSuccess
    },
    formClose() {
      const store = useMyStore()
      return store.formClose
    },
  },
  watch: {
    launchSuccess(newValue, oldValue) {
      if (newValue) {
        this.processOk()
      }
    },
    formClose(newValue, oldValue) {
      if (newValue) {
        this.goBack()
      }
    }
  },
  methods: {
    open(row) {
      this.selectInstance = {}
      this.selectInstance = row
      this.activeTab = 0
      this.draftId = ''
      this.openItemDl = true
      this.$nextTick(() => {
        this.$refs.processListRef.getName(this.selectInstance.processDefName || this.selectInstance.formName)
        this.$refs.draftsListRef.getId(this.selectInstance.formId)
      })
      if (this.type == 'forms') {
        useMyStore().handleLaunchStatus(true)
      }
    },
    goBack() {
      this.openItemDl = false
      const v = this.isOA ? 'secondClose' : ''
      this.$emit('close', v)
      useMyStore().handleLaunchStatus(false)
    },
    activateTab(index) {
      this.activeTab = index
    },
    processOk() {
      if (this.isOA) {
        useMyStore().handleLaunchStatus(false)
      }
      useMyStore().handleLaunchSuccess(true)
      this.openItemDl = false
      // 删除草稿箱
      if (this.draftId) {
        taskApi.getDraftsDelete({ id: this.draftId })
      }
    },
    submitForm() {
      this.$refs.processForm.validateCondition((conditionFlag)=>{
        if(conditionFlag) {
          this.$refs.processForm.validate((validForm, validProcess) => {
            if (!this.isMobile) {
              if (validForm && validProcess) {
                useMyStore().handleLaunchSuccess(false)
                this.$refs.processForm.submit()
              } else {
                this.$message.warning('请完善表单/流程选项😥')
              }
            }
          })
        } else{
          this.$message.error('没有满足条件的分支😥，请联系管理员检查流程的条件分支配置是否正确！')
        }
      })
    },
    // 保存草稿
    draftForm() {
      this.$refs.processForm.submitDraft()
    },
    // 保存草稿
    getDraft(e) {
      this.loading = true
      if (this.draftId) {
        taskApi.getDraftsUpdate({
          formName: this.selectInstance.processDefName || this.selectInstance.formName,
          formId: this.selectInstance.formId,
          formData: JSON.stringify(e),
          id: this.draftId
        }).then((res) => {
          this.loading = false
          this.$message.success('更新草稿成功')
          this.$refs.draftsListRef.getId(this.selectInstance.formId)
        }).catch((e) => {
          this.loading = false
        })
      } else {
        taskApi.getDraftsCreate({
          formName: this.selectInstance.processDefName || this.selectInstance.formName,
          formId: this.selectInstance.formId,
          formData: JSON.stringify(e)
        }).then((res) => {
          this.loading = false
          this.$message.success('保存草稿成功')
          this.$refs.draftsListRef.getId(this.selectInstance.formId)
        }).catch((e) => {
          this.loading = false
        })
      }
    },
    // 草稿继续编辑
    getEdit(id) {
      this.draftId = id
      taskApi.getDraftsDetail({ id: id }).then((res) => {
        this.activeTab = 0
        this.$refs.processForm.getDraftData(JSON.parse(res.data.formData))
      }).catch((e) => {
        this.loading = false
      })
    },
    autoSaveOkHandler(){
      //更新自动保存时间，格式是：10:10:10
      const now = new Date();
      const hours = now.getHours().toString().padStart(2, '0');
      const minutes = now.getMinutes().toString().padStart(2, '0');
      const seconds = now.getSeconds().toString().padStart(2, '0');
      this.lastAutoSaveTime = hours + ':' + minutes + ':' + seconds;
    }
  }
}
</script>
<style scoped>
.isFormClass{
  top:-65px;
}
</style>
