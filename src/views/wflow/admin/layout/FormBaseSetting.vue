<template>
  <div class="base-setup" @click="showIconSelect = false">
    <el-form ref="baseSetting" :model="setup" label-position="top" label-width="80px">
      <el-form-item label="流程图标" v-if="!isSubProc">
        <icon :key="setup.logo.icon" :name="setup.logo.icon" :style="'background:' + setup.logo.background"/>
        <span class="change-icon">
          <span>
            <span>选择背景色</span>
            <el-color-picker v-model="setup.logo.background" show-alpha size="default" :predefine="colors"/>
          </span>
          <span>
            <span>选择图标</span>
            <el-popover placement="bottom-start" width="402" trigger="click">
              <div class="icon-select">
                <icon :name="i" v-for="(i, id) in icons" :key="id" @click="iconClick(i)"/>
              </div>
              <template #reference>
                <icon :key="setup.logo.icon" :name="setup.logo.icon"/>
              </template>
            </el-popover>
            <!--<icon :name="setup.icon" @click.stop="showIconSelect = true"></icon>-->
          </span>
        </span>
      </el-form-item>
      <el-form-item label="流程名称" :rules="getRule('请输入流程名称')" prop="formName">
        <el-input v-model="setup.formName" size="default"/>
      </el-form-item>
      <el-form-item label="所在分组" :rules="getRule('请选择流程分组')" class="group" prop="groupId">
        <el-select v-model="setup.groupId" size="default" placeholder="请选择分组">
          <el-option v-for="(op, index) in fromGroup" :key="index" v-show="op.groupId > 1" :label="op.groupName" :value="op.groupId"/>
        </el-select>
        <el-popover placement="bottom-end" title="新建流程分组" width="300" trigger="click">
          <el-input v-model="newGroup" size="default" placeholder="请输入新的分组名">
            <template #append>
              <el-button type="primary" @click="addGroup" class="isButton">提交</el-button>
            </template>
          </el-input>
          <template #reference>
            <el-button icon="el-icon-plus" size="default"  type="primary">新建分组</el-button>
          </template>
        </el-popover>
      </el-form-item>
      <el-form-item label="流程说明">
        <el-input placeholder="请输入流程说明" v-model="setup.remark" size="default" type="textarea" show-word-limit :autosize="{ minRows: 2, maxRows: 5 }" maxlength="500"/>
      </el-form-item>
      <template v-if="!isSubProc">
        <el-form-item label="消息通知方式" :rules="getRule('请选择消息通知方式')">
          <el-select v-model="setup.settings.notify.types" size="default" placeholder="选择消息通知方式" style="width: 30%" clearable multiple collapse-tags>
            <el-option v-for="(wc, index) in notifyTypes" :label="wc.name" :key="index" :value="wc.type"/>
          </el-select>
          <el-input v-model="setup.settings.notify.title" size="default" style="width: 68%; float: right" placeholder="消息通知标题"/>
        </el-form-item>
        <el-form-item label="谁可以管理此流程">
          <el-select v-model="setup.settings.admin" size="default" @click="selectUser('admin')" value-key="name" class="select-u" placeholder="请选择可以管理此流程的人员" clearable multiple>
            <el-option v-for="(wc, index) in setup.settings.admin" :label="wc.name" :key="index" :value="wc"/>
          </el-select>
        </el-form-item>
      </template>
    </el-form>
    <org-picker title="请选择可以管理此流程的人员" multiple ref="orgPicker" :selected="select" @ok="selected"/>
  </div>
</template>

<script>
import OrgPicker from '@/components/common/OrgPicker.vue'
import { getModelGroups, createModelGroup } from '@/api/modelGroup'
import iconfont from '@/assets/iconfont/iconfont.json'

export default {
  name: 'FormBaseSetting',
  components: { OrgPicker },
  data() {
    return {
      a:'',
      b:[],
      nowUserSelect: null,
      showIconSelect: false,
      select: [],
      newGroup: '',
      fromGroup: [],
      notifyTypes: [
        { type: 'APP', name: '应用内通知' },
       // { type: 'EMAIL', name: '邮件通知' },
       // { type: 'SMS', name: '短信通知' },
       // { type: 'WX', name: '微信通知' },
       // { type: 'DING', name: '钉钉通知' },
      ],
      colors: [
        '#ff4500',
        '#ff8c00',
        '#ffd700',
        '#90ee90',
        '#00ced1',
        '#1e90ff',
        '#c71585',
        'rgba(255, 69, 0, 0.68)',
        'rgb(255, 120, 0)',
        'hsl(181, 100%, 37%)',
        'hsla(209, 100%, 56%, 0.73)',
        '#c7158577',
      ],
      icons: [
        'el-icon-deletefilled',
        'el-icon-tools',
        'el-icon-goods',
        'el-icon-warning',
        'el-icon-circleplus',
        'el-icon-camerafilled',
        'el-icon-promotion',
        'el-icon-briefcase',
        'el-icon-platform',
        'el-icon-avatar',
        'el-icon-histogram',
        'el-icon-stamp',
        'el-icon-checked',
      ],
      rules: {
        formName: [{}],
        groupId: [],
      },
    }
  },
  computed: {
    setup() {
      return this.$wflow.design
    },
    isSubProc(){
      return this.setup.isSubProc || false;
    }
  },
  created() {
    this.loadIconfont()
  },
  mounted() {
    this.getGroups()
  },
  methods: {
    iconClick(i){
      this.setup.logo.icon = i
    },
    getRule(msg) {
      return [{ required: true, message: msg, trigger: 'blur' }]
    },
    loadIconfont() {
      if (iconfont && iconfont.id) {
        iconfont.glyphs.forEach((icon) => {
          this.icons.push(
            `${iconfont.font_family} ${iconfont.css_prefix_text}${icon.font_class}`
          )
        })
      }
    },
    getGroups() {
      getModelGroups({}, this.isSubProc).then((rsp) => {
          this.fromGroup = rsp.data
      }).catch((err) => this.$err(err, '获取分组异常'))
    },
    addGroup() {
      if (this.newGroup.trim() !== '') {
        createModelGroup({name: this.newGroup.trim()}, this.isSubProc)
          .then((rsp) => {
            this.$ok(rsp, '新增分组成功')
            this.getGroups()
          })
          .catch((err) => this.$err(err, '新增分组失败'))
      }
    },
    selected(select) {
      this.setup.settings[this.nowUserSelect] = select
      //this.setup[this.nowUserSelect] = select
    },
    selectUser(key) {
      this.select = this.setup.settings[key]
      this.nowUserSelect = key
      this.$refs.orgPicker.show()
    },
    validate() {
      this.$refs.baseSetting.validate()
      let err = []
      if (!this.$isNotEmpty(this.setup.formName)) {
        err.push('流程名称未设置')
      }
      if (!this.$isNotEmpty(this.setup.groupId)) {
        err.push('流程分组未设置')
      }
      if (!this.isSubProc && this.setup.settings.notify.types.length === 0) {
        err.push('审批消息通知方式未设置')
      }
      return err
    },
  },
}
</script>

<style lang="less" scoped>
:deep(.el-select-dropdown) {
  display: none;
}
.icon-select {
  display: flex;
  flex-wrap: wrap;
  :deep(.icon) {
    display: flex;
    align-items: center;
    cursor: pointer;
    font-size: 20px;
    width: 20px;
    height: 20px;
    padding: 10px;
    max-width: 38px !important;
    &:hover {
      box-shadow: 0 0 10px 2px #c2c2c2;
    }
  }
}
:deep(.select-u) {
  width: 100%;
}
.base-setup {
  overflow: auto;
  margin: 0 auto;
  width: 600px;
  height: calc(100vh - 105px);
  background: #ffffff;
  margin-top: 10px;
  padding: 15px 20px;
  :deep(.icon):first-child {
    position: relative;
    cursor: pointer;
    font-size: xx-large;
    color: #ffffff;
    border-radius: 10px;
    padding: 10px;
  }

  .change-icon {
    margin-left: 20px;

    span {
      font-size: small;
      color: #7a7a7a;
      margin-right: 15px;
    }

    :deep(.icon) {
      cursor: pointer;
      color: #7a7a7a;
      font-size: x-large;
    }
  }

  :deep(.el-form-item__label) {
    padding: 0;
    font-weight: bold;
  }
}
:deep(.group) {
  .el-select {
    width: calc(100% - 130px);
  }

  .el-button {
    margin-left: 10px;
    width: 120px;
  }
}
::-webkit-scrollbar {
  width: 4px;
  height: 4px;
  background-color: #f8f8f8;
}
::-webkit-scrollbar-thumb {
  border-radius: 16px;
  background-color: #e8e8e8;
}
.isButton{
  background:#409eff !important;
  color:#fff !important;
  border-radius: 0 4px 4px 0;
}
</style>
