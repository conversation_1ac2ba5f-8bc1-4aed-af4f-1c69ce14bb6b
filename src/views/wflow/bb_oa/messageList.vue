<template>
  <div class="positionDiv">
    <span class="cardTitle">通知</span>
    <el-input clearable placeholder="搜索通知" prefix-icon="el-icon-search" class="cardInput" v-model="inputTitle"
      @change="handleChange" />
    <div v-if="notify.total > 0">
      <div v-for="(msg, index) in notify.records" :key="index" class="eachBox">
        <div class="header-t" @click="toNotify(msg)">
          <icon name="el-icon-successfilled" v-if="msg.type == 'SUCCESS'" style="color: #02b068"></icon>
          <icon name="el-icon-warningfilled" v-else-if="msg.type == 'WARNING'" style="color: #f78f5f"></icon>
          <icon name="el-icon-circleclosefilled" v-else-if="msg.type == 'ERROR'" style="color: #f25643"></icon>
          <icon name="el-icon-infofilled" v-else style="color: #8c8c8c"></icon>
          <span>{{ msg.title }}</span>
        </div>
        <div class="p1-div" @click="toNotify(msg)">
          <p class="p1" v-for="(item, index) in msg.content.split('\\split')" :key="index">
            {{ item }}
          </p>
        </div>
        <div class="header-t2">
          <span>{{ toTime(msg.createTime) }}</span>
          <el-button type="text" @click="getReadNotify(msg.id)">标记为已读</el-button>
        </div>
      </div>
      <div class="notify-action" v-show="notify.total > 0">
        <el-button type="primary" link @click="--params.pageNo" :disabled="params.pageNo <= 1">上一页</el-button>
        <el-button type="primary" link @click="getReadNotify(null)">本页已读</el-button>
        <el-button type="primary" link @click="++params.pageNo"
          :disabled="notify.total <= params.pageSize * notify.current">下一页
        </el-button>
      </div>
    </div>
    <el-empty :image-size="50" description="暂无消息" v-if="notify.total == 0"></el-empty>
    <el-drawer size="560px" direction="rtl" title="审批详情" v-model="processVisible" class="custom-detail-header">
      <instance-preview v-if="processVisible" :node-id="isNodeId" :instance-id="selectInstance"
        @handler-after="processVisible = false" />
    </el-drawer>
  </div>
</template>

<script lang="ts" setup>
import { getUserNotify, readNotify } from '@/api/notify'
import InstancePreview from "@/views/wflow/workspace/approval/ProcessInstancePreview.vue";
import { useWebSocket } from '@vueuse/core'
import { useOriginUrl } from '@/utils/useOriginUrl'
const { server } = useOriginUrl()
const params = ref({
  pageSize: 5,
  pageNo: 1
})
const notify = ref({
  total: 0,
  current: 1,
  records: [],
})
const selectInstance = ref('')
const processVisible = ref(false)
const isNodeId = ref('')
const visible = ref(false)
const message = useMessage()
watch(
  () => params.value, () => {
    getUserNotifyList();
  },
  { deep: true }
)
const inputTitle = ref('')
const handleChange = () => {
  params.value.pageNo = 1
  getUserNotifyList()
}
const getUserNotifyList = async () => {
  try {
    const res = await getUserNotify({
      ...params.value,
      value: inputTitle.value
    })
    notify.value = res.data
  } catch (e: any) {

  }
}
const toNotify = (msg) => {
  visible.value = false
  isNodeId.value = msg.nodeId
  if (msg.instanceId) {
    selectInstance.value = msg.instanceId
    processVisible.value = true
  }
}
const getReadNotify = (id) => {
  let list = id ? [id] : notify.value.records.map(n => n.id)
  readNotify(list).then(() => {
    message.success('已读成功')
    getUserNotifyList()
  }).catch(() => {
    message.error('已读失败')
  })
}
const toTime = (e) => {
  const date = new Date(e);
  const Y = date.getFullYear() + '-';
  const M = (date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1) + '-';
  const D = (date.getDate() < 10 ? '0' + (date.getDate()) : date.getDate()) + ' ';
  const h = (date.getHours() < 10 ? '0' + (date.getHours()) : date.getHours()) + ':';
  const m = (date.getMinutes() < 10 ? '0' + (date.getMinutes()) : date.getMinutes());
  // const s = (date.getSeconds() < 10 ? '0' + (date.getSeconds()) : date.getSeconds());
  const time1 = Y + M + D + h + m;
  // return time1.substring(5, 16)
  return time1
}


/** WebSocket **/

/** 发起 WebSocket 连接 */
const { data } = useWebSocket(server.value, {
  autoReconnect: true,
  heartbeat: true
})
watchEffect(() => {
  if (!data.value) {
    return
  }
  try {
    if (data.value === 'pong') {
      return
    }
    // 2.1 解析 type 消息类型
    const jsonMessage = JSON.parse(data.value)
    const type = jsonMessage.type
    if (!type) {
      message.error('未知的消息类型：' + data.value)
      return
    }
    // 2.2 消息类型：demo-message-receive
    if (type === 'wflow-push') {
      getUserNotifyList()
      return
    }
    // message.error('未处理消息：' + data.value)
  } catch (error) {
    message.error('处理消息发生异常：' + data.value)
    console.error(error)
  }
})

onMounted(() => {
  getUserNotifyList()
})
</script>

<style lang="less" scoped>
.notify-action {
  display: flex;
  padding-top: 10px;
  justify-content: space-between;
}

.eachBox {
  border-bottom: 1px solid #EBEEF5;
  padding-bottom: 10px;
  margin-bottom: 10px;
}

.header-t {
  display: flex;
  align-items: center;
  justify-content: left;
  color: #303133;
  font-size: 15px;
  font-weight: bold;
  cursor: pointer;

  span {
    margin-left: 4px
  }
}

.p1 {
  color: #909399;
  font-size: 14px;
  display: block;
  margin: 0;
  // margin-bottom: 5px;
  cursor: pointer;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
  overflow: hidden;
  text-overflow: ellipsis;
  padding: 0;
  white-space: pre-wrap;
}

.p1-div {
  margin: 5px 0;
}

.header-t2 {
  display: flex;
  align-items: center;
  justify-content: left;
  font-size: 14px;
  color: #909399;

  span {
    margin-right: 10px
  }

  .el-button {
    padding: 0;
    height: auto;
    font-size: 12px;
  }
}

.cardTitle {
  cursor: auto;
}

.positionDiv {
  position: relative;
}

.cardInput {
  position: absolute;
  right: 0;
  top: -6px;
  width: 200px;
}
</style>