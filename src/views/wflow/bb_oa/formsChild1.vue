<template>
  <div>
    <el-card class="oaCard transitionNone" :class="{ 'nextSubOa': nextSub }">
      <div>
        <el-radio v-model="radio" label="0" border>待处理</el-radio>
        <el-radio v-model="radio" label="1" border>已处理</el-radio>
        <el-radio v-model="radio" label="2" border>已发起</el-radio>
        <el-radio v-model="radio" label="3" border>我收到的
          <el-badge :value="unReadNum" :max="99" class="badgeClass" v-if="unReadNum > 0"></el-badge>
        </el-radio>
      </div>
      <un-finished v-show="radio == '0'" class='contentMargin'></un-finished>
      <finished v-show="radio == '1'" class='contentMargin'></finished>
      <my-submit v-show="radio == '2'" class='contentMargin' :isOA="true"></my-submit>
      <cc-me v-show="radio == '3'" class='contentMargin' @unReadNum="getNum"></cc-me>
    </el-card>
  </div>
</template>

<script>
import unFinished from '@/views/wflow/workspace/oa/UnFinished.vue'
import finished from '@/views/wflow/workspace/oa/Finished.vue'
import mySubmit from '@/views/wflow/workspace/oa/MySubmit.vue'
import ccMe from '@/views/wflow/workspace/oa/CcMe.vue'
import { useMyStore } from '@/store/modules/jump'
export default {
  props: {
    type: ''
  },
  components: {
    unFinished,
    finished,
    mySubmit,
    ccMe
  },
  data() {
    return {
      radio: '0',
      unReadNum: 0,
      nextSub: false,
    }
  },
  computed: {
    launchStatus() {
      const store = useMyStore()
      return store.launchStatus
    },
  },
  watch: {
    launchStatus(newValue, oldValue) {
      console.log('launchStatus', oldValue, newValue);
      // this.oaClose(newValue)
      this.nextSub = newValue
    },
  },
  created() {
  },
  methods: {
    oaClose(e) {
      if (e == 'close') {
        this.nextSub = false
      } else {
        this.nextSub = true
      }
    },
    getNum(e) {
      this.unReadNum = e
      this.$emit('unReadNum', e)
    },
  },

}
</script>

<style lang="less" scoped>
:deep .el-radio__inner {
  border-radius: 20px;
  display: none;
}

:deep.el-radio.is-bordered.is-checked {
  background: #e0f1ff !important;
  border: none !important
}

:deep.el-radio__input.is-checked+.el-radio__label {
  color: #0089ff
}

:deep.el-radio.is-bordered {
  border: none;
  background: #eaeced;
  color: #171a1d;
  border-radius: 4px;
}

:deep.el-radio {
  margin-right: 15px;
  padding: 0 15px 0 7px;
}

.contentMargin {
  margin: 0 -20px
}

.badgeClass {
  position: absolute;
  right: -6px;
  top: -5px;
  --el-badge-radius: 50%;

  :deep(.el-badge__content) {
    padding: 3px;
    border: none;
    font-size: 11px;
    width: 16px;
    height: 16px;
    box-sizing: border-box;
  }
}
</style>