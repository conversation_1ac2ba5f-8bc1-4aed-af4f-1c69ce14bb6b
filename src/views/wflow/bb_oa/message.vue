<template>
  <div>
    <div>
      <el-popover placement="bottom-end" width="360" trigger="click" :visible="visible" ref="myPopover">
        <el-input clearable placeholder="搜索通知" prefix-icon="el-icon-search" class="searchInput"
          v-show="notify.total > 0" v-model="inputTitle" @change="handleChange" />
        <div class="notify">
          <el-empty :image-size="50" description="暂无消息" v-if="notify.total === 0"></el-empty>
          <div v-for="msg in notify.records" :key="msg.id" class="notify-item">
            <el-row>
              <el-col :span="2">
                <div class="notify-item-type-icon">
                  <icon name="el-icon-successfilled" v-if="msg.type === 'SUCCESS'" style="color: #02b068"></icon>
                  <icon name="el-icon-warningfilled" v-else-if="msg.type === 'WARNING'" style="color: #f78f5f">
                  </icon>
                  <icon name="el-icon-circleclosefilled" v-else-if="msg.type === 'ERROR'" style="color: #f25643">
                  </icon>
                  <icon name="el-icon-infofilled" v-else style="color: #8c8c8c"></icon>
                </div>
              </el-col>
              <el-col :span="22">
                <div class="notify-item-title" @click="toNotify(msg)" :title="msg.title">{{ msg.title }}</div>
                <p class="notify-item-content" v-for="(item, index) in msg.content.split('\\split')" :key="index">
                  {{ item }}
                </p>
              </el-col>
            </el-row>
            <span class="notify-item-time">{{ toTime(msg.createTime) }}</span>
            <el-button type="primary" link class="notify-btn" @click="getReadNotify(msg.id)">标记为已读</el-button>
          </div>
        </div>
        <div class="notify-action" v-show="notify.total > 0">
          <el-button type="primary" link @click="--params.pageNo" :disabled="params.pageNo <= 1">上一页</el-button>
          <el-button type="primary" link @click="getReadNotify(null)">本页已读</el-button>
          <el-button type="primary" link @click="++params.pageNo"
            :disabled="notify.total <= params.pageSize * notify.current">下一页
          </el-button>
        </div>
        <template #reference>
          <el-badge :hidden="notify.total === 0" :value="notify.total" @click="visible = !visible">
            <icon name="el-icon-bell"></icon>
          </el-badge>
        </template>
      </el-popover>
    </div>
    <el-drawer :size="isMobile ? '100%' : '560px'" direction="rtl" title="审批详情" v-model="processVisible"
      class="custom-detail-header">
      <instance-preview v-if="processVisible" :node-id="isNodeId" :instance-id="selectInstance"
        @handler-after="processVisible = false" />
    </el-drawer>
  </div>
</template>

<script lang="ts" setup>
import { getUserNotify, readNotify } from '@/api/notify'
import InstancePreview from "@/views/wflow/workspace/approval/ProcessInstancePreview.vue";
import { useWebSocket } from '@vueuse/core'
import { useOriginUrl } from '@/utils/useOriginUrl'
const { server } = useOriginUrl()

const params = ref({
  pageSize: 5,
  pageNo: 1
})
const notify = ref({
  total: 0,
  current: 1,
  records: [],
})
const selectInstance = ref('')
const processVisible = ref(false)
const isNodeId = ref('')
const visible = ref(false)
const message = useMessage()
watch(
  () => params.value, () => {
    getUserNotifyList();
  },
  { deep: true }
)
const inputTitle = ref('')
const handleChange = () => {
  params.value.pageNo = 1
  getUserNotifyList()
}
const getUserNotifyList = async () => {
  try {
    const res = await getUserNotify({
      ...params.value,
      value: inputTitle.value
    })
    notify.value = res.data
  } catch (e: any) {

  }
}
const toNotify = (msg) => {
  visible.value = false
  isNodeId.value = msg.nodeId
  if (msg.instanceId) {
    selectInstance.value = msg.instanceId
    processVisible.value = true
  }
}
const getReadNotify = (id) => {
  let list = id ? [id] : notify.value.records.map(n => n.id)
  readNotify(list).then(() => {
    message.success('已读成功')
    getUserNotifyList()
  }).catch(() => {
    message.error('已读失败')
  })
}
const toTime = (e) => {
  const date = new Date(e);
  const Y = date.getFullYear() + '-';
  const M = (date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1) + '-';
  const D = (date.getDate() < 10 ? '0' + (date.getDate()) : date.getDate()) + ' ';
  const h = (date.getHours() < 10 ? '0' + (date.getHours()) : date.getHours()) + ':';
  const m = (date.getMinutes() < 10 ? '0' + (date.getMinutes()) : date.getMinutes());
  // const s = (date.getSeconds() < 10 ? '0' + (date.getSeconds()) : date.getSeconds());
  const time1 = Y + M + D + h + m;
  return time1.substring(5, 16)
}

/** WebSocket **/
const { data } = useWebSocket(server.value, {
  autoReconnect: true,
  heartbeat: true
})
watchEffect(() => {
  if (!data.value) {
    return
  }
  try {
    if (data.value === 'pong') {
      return
    }
    // 2.1 解析 type 消息类型
    const jsonMessage = JSON.parse(data.value)
    const type = jsonMessage.type
    if (!type) {
      message.error('未知的消息类型：' + data.value)
      return
    }
    // 2.2 消息类型：demo-message-receive
    if (type === 'wflow-push') {
      getUserNotifyList()
      return
    }
    // message.error('未处理消息：' + data.value)
  } catch (error) {
    message.error('处理消息发生异常：' + data.value)
    console.error(error)
  }
})

onMounted(() => {
  getUserNotifyList()
})
</script>

<style lang="less" scoped>
.notify {
  max-height: 450px;
  background: #f3f3f3;
  overflow-y: auto;

  .notify-item:nth-child(1) {
    border: none
  }

  .notify-item:last-child {
    border-bottom: 1px solid #f3f3f3;
  }

  .notify-item {
    border-top: 1px solid #f3f3f3;
    padding: 10px 5px;
    background: white;
    position: relative;
    border-radius: 5px;

    .notify-item-title {
      cursor: pointer;
      color: #3b3b3b;
      width: 76%;
      text-overflow: ellipsis;
      overflow: hidden;
      white-space: nowrap;

      &:hover {
        color: #333;
        font-weight: bold;
      }
    }

    .notify-item-content {
      color: #8c8c8c;
      padding: 0;
      font-size: smaller;
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 3;
      overflow: hidden;
      text-overflow: ellipsis;
      padding: 0;
      white-space: pre-wrap;
      margin: 0;
      line-height: 140%;
    }

    .notify-item-time {
      position: absolute;
      right: 4px;
      top: 12px;
      font-size: 12px;
      color: #8c8c8c;
      // margin-left: 25px;
    }

    .notify-btn {
      // position: absolute;
      // right: 5px;
      // top: 10px;
      margin-left: 22px;
      font-size: 12px;
    }

    .notify-item-type-icon {
      font-size: 18px;
    }
  }
}

.notify-action {
  display: flex;
  padding-top: 10px;
  justify-content: space-between;
}

.searchInput {
  margin-bottom: 10px;
}
</style>