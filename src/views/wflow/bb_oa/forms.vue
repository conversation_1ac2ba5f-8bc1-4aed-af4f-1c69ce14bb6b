<template>
  <div>
    <div class="relative bg-[#fff] formsHeader">
      <ul class="tab-list">
        <li v-for="(tab, index) in tabs" :key="index" @click="activateTab(index)"
          :class="{ active: activeTab === index, 'unReadCC': unReadNum > 0 && index == 1 }">
          <el-badge :value="unReadNum" :max="99" class="badgeClass" v-if="unReadNum > 0"></el-badge>
          {{ tab.title }}
        </li>
      </ul>
    </div>
    <detail0 v-show="activeTab == 0" @getType="getType" class="detailChild"></detail0>
    <detail1 v-show="activeTab == 1" ref="child1" @unReadNum="getNum" class="detailChild"></detail1>
  </div>
</template>

<script>
import { useRoute } from 'vue-router'
import detail0 from './formsChild0.vue'
import detail1 from './formsChild1.vue'
const route = useRoute()
export default {
  name: 'formsList',
  components: {
    detail0,
    detail1
  },
  data() {
    return {
      tabs: [
        { title: '首页', content: '1' },
        { title: '审批中心', content: '2' }
      ],
      activeTab: 0,
      unReadNum: 0
    }
  },
  watch: {
    '$route.query.type'() {
      console.log(this.$route.query.type)
      this.activeTab = 1
    }
  },
  methods: {
    activateTab(index) {
      this.activeTab = index
    },
    getType(e) {
      this.activeTab = 1
      this.$refs.child1.radio = e
    },
    getNum(e) {
      this.unReadNum = e
    },
  },
  created() {
    console.log(this.$route.query.type, 'this.$route.query.type')
    if (this.$route.query.type) {
      console.log('type有值')
      // this.activeTab = 1
    } else {
      console.log('type无值')
    }
  }
}
</script>

<style lang="less" scoped>
.detailChild {
  padding-top: 48px;
}

.tab-list {
  list-style-type: none;
  display: flex;
  padding: 6px 15px;
  background: #fff;
  // margin: -10px;
  position: fixed;
  width: 100%;
  z-index: 1;
  box-shadow: inset 0px -1px 0px 0px #ECEDED;
}

.tab-list li {
  cursor: pointer;
  padding: 8px 20px;
  margin-right: 10px;
  border-radius: 5px;
  font-size: 15px;
}

.tab-list li:hover {
  background: #eee;
}

.tab-list li.active {
  position: relative;
}

.tab-list li.active::before {
  position: absolute;
  bottom: 0;
  content: '';
  width: 20px;
  height: 3px;
  background: #303133;
  left: 0;
  right: 0;
  margin: auto;
  border-radius: 1px;
}

.formsHeader {
  left: -15px;
  right: -15px;
  width: 100%;
  top: -15px;
  width: calc(100% + 30px);
}

.badgeClass {
  display: none;
}

.unReadCC {
  position: relative;

  .badgeClass {
    display: block;
    position: absolute;
    right: 2px;
    top: 2px;
    --el-badge-radius: 50%;

    :deep(.el-badge__content) {
      padding: 3px;
      border: none;
      font-size: 11px;
      width: 16px;
      height: 16px;
      box-sizing: border-box;
    }
  }
}
</style>