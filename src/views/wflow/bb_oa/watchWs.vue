<template></template>
<script lang="ts" setup>
import { useWebSocket } from '@vueuse/core'
import { useOriginUrl } from '@/utils/useOriginUrl'

const { server } = useOriginUrl()
import { useMyStore } from '@/store/modules/jump'

const message = useMessage()

import { useAppStore } from '@/store/modules/app'

const appStore = useAppStore()

import receiveMsgNotification from '@/assets/audio/receive_msg_notification.mp3'
// 创建 Audio 对象
const notificationSound = new Audio(receiveMsgNotification)

// 播放声音提示的函数
function playNotificationSound() {
  notificationSound.play().catch((error) => {
    console.error('播放声音失败:', error)
  })
}

import * as MessageApi from '@/api/message/index'

// 闪烁测试
// 收到新消息时
const showNotification = () => {
  console.log('触发了 showNotification')
  if(window.electron){
    console.log('触发了 window.electron')
    window.electron.sendMessage()
  }
  
}

// 用户查看消息后
const clearNotification = () => {
  if(window.electron){
    window.electron.cancelMessage()
  }
}


/** WebSocket **/
// console.log('ws地址', server.value)
const { data, send } = useWebSocket(server.value, {
  autoReconnect: true,
  // heartbeat: true,
  heartbeat: {
    message: 'ping', // 发送的心跳消息内容
    interval: 30000,  // 每 30 秒发送一次心跳消息
    pongTimeout: 5000 // 等待服务器响应的超时时间为 5 秒
  },
  onConnected(ws) {
    console.log('WebSocket 连接成功', ws);
    MessageApi.getUserMessageTotal()
    // 可以在这里执行一些初始化操作
  },
})

watchEffect(() => {
  if (!data.value) {
    return
  }
  try {
    if (data.value === 'pong') {
      return
    }
    // 2.1 解析 type 消息类型
    const jsonMessage = JSON.parse(data.value)
    const type = jsonMessage.type
    const content = JSON.parse(jsonMessage.content)
    console.log('type:', type, 'content:', content)
    if (!type) {
      message.error('未知的消息类型：' + data.value)
      return
    }

    // 收到未读消息总数
    if (type === 'im-message-horn') {
      console.log('收到未读消息总数 im-message-horn:', content)
      const horn = JSON.parse(content)
      appStore.setAllUnReadCount(horn.unreadMessageTotal)
      showNotification()
    }
    // 收到消息
    else if (type === 'im-push-receive') {
      console.log('收到消息 im-push-receive:', content)
      showNotification()
      const senderId = parseInt(content.sender)
      const loginUser = localStorage.getItem('loginUser')

      if (loginUser) {
        try {
          const myId = parseInt(JSON.parse(loginUser).id)
          if (senderId !== myId) {
            playNotificationSound()
          }
        } catch (error) {
          console.error('解析 loginUser 失败:', error)
          message.error('解析 loginUser 失败')
        }
      } else {
        console.error('loginUser 不存在')
        message.error('loginUser 不存在')
      }
    }

    // 处理其他类型消息
    else if (type === 'wflow-push' || type === 'im-push-handler') {
      useMyStore().handleWS(true)
      setTimeout(() => {
        useMyStore().handleWS(false)
      }, 1000)
    } else if (type === 'im-push-todoNum' || type === 'im-push-ccNum') {
      if (/^\d+$/.test(content)) {
        const handler =
          type === 'im-push-todoNum' ? useMyStore().handleTodoNum : useMyStore().handleCcNum
        handler(Number(content))
      }
    }
  } catch (error) {
    message.error('处理消息发生异常：' + data.value)
    console.error(error)
  }
})

/** 发送消息 */
const handlerSend = () => {
  // 1.1 先 JSON 化 message 消息内容
  const messageContent = JSON.stringify({
    text: 'im-push-processNum'
  })
  // 1.2 再 JSON 化整个消息
  const jsonMessage = JSON.stringify({
    type: 'im-push-process',
    content: messageContent
  })
  // 2. 最后发送消息
  send(jsonMessage)
}

handlerSend()
</script>
