<template>
  <div>
    <p>审批通过后，智能人事中的员工状态将在转正日期后自动变为正式</p>
    <el-form label-position="top">
      <el-form-item label="允许代他人提交" class="switchItem">
        <span class="spanClass"
          >审批单将按照代提交人的流程进行审批，可在代提交人的「已发起」列表中查看</span
        >
        <el-switch
          v-model="modelValue[0].props.isShow"
          active-color="#C0CCDA"
          inactive-color="#409EFF"
        ></el-switch>
      </el-form-item>
      <el-form-item label="职位选项设置" class="options">
        <template #label>
          <div class="option-item-label">
            <span>职位设置（鼠标拖拽排序）</span>
            <el-button
              icon="el-icon-plus"
              link
              type="primary"
              @click="modelValue[4].props.options.push({ name: '', value: '' })"
              >新增选项
            </el-button>
          </div>
        </template>
        <draggables
          item-key="id"
          v-model="modelValue[4].props.options"
          class="option-items"
          :component-data="{ tag: 'div', type: 'transition-group' }"
          handler=".el-icon-rank"
          v-bind="dragOption"
        >
          <template #item="{ element, index }">
            <div class="option-item">
              <el-input
                v-model="element.value"
                style="width: 100px"
                placeholder="选项value值"
                clearable
              />
              <span class="splitSpan">~</span>
              <el-input v-model="element.name" placeholder="选项名称" style="width: 140px" />
              <icon
                name="el-icon-delete del-btn"
                @click="modelValue[4].props.options.splice(index, 1)"
              ></icon>
            </div>
          </template>
        </draggables>
      </el-form-item>
      <el-form-item label="职位多选模式" class="other-switchIte">
        <el-switch v-model="modelValue[4].props.multiple"></el-switch>
      </el-form-item>
      <el-form-item label="岗位职级选项设置" class="options">
        <template #label>
          <div class="option-item-label">
            <span>岗位职级设置（鼠标拖拽排序）</span>
            <el-button
              icon="el-icon-plus"
              link
              type="primary"
              @click="modelValue[5].props.options.push({ name: '', value: '' })"
              >新增选项
            </el-button>
          </div>
        </template>
        <draggables
          item-key="id"
          v-model="modelValue[5].props.options"
          class="option-items"
          :component-data="{ tag: 'div', type: 'transition-group' }"
          handler=".el-icon-rank"
          v-bind="dragOption"
        >
          <template #item="{ element, index }">
            <div class="option-item">
              <el-input
                v-model="element.value"
                style="width: 100px"
                placeholder="选项value值"
                clearable
              />
              <span class="splitSpan">~</span>
              <el-input v-model="element.name" placeholder="选项名称" style="width: 140px" />
              <icon
                name="el-icon-delete del-btn"
                @click="modelValue[5].props.options.splice(index, 1)"
              ></icon>
            </div>
          </template>
        </draggables>
      </el-form-item>
      <el-form-item label="岗位职级多选模式" class="other-switchIte">
        <el-switch v-model="modelValue[5].props.multiple"></el-switch>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import draggables from 'vuedraggable'
export default {
  components: { draggables },
  props: {
    modelValue: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      dragOption: {
        animation: 300,
        sort: true,
        group: 'option'
      }
    }
  },
  methods: {},
  emits: ['update:modelValue']
}
</script>

<style lang="less" scoped>
.spanClass {
  line-height: 1;
  margin: 5px 0;
  color: rgba(25, 31, 37, 0.4);
}

:deep(.options) {
  .el-form-item__label {
    display: block;
    width: 100%;
    text-align: left;
    padding: 0;
  }

  .el-icon-rank {
    padding-right: 5px;
    cursor: move;
  }
}

.option-div {
  width: 100%;
}

.option-item {
  display: flex;
  align-items: center;
  padding-bottom: 5px;

  :deep(.el-input) {
    // width: 250px;
    float: right;
  }
}

.option-item-label {
  display: flex;
  justify-content: space-between;

  button {
    float: right;
  }
}

.switchItem {
  ::v-deep(.el-switch.is-checked .el-switch__core .el-switch__action) {
    left: 1px !important;
  }

  ::v-deep(.el-switch__core .el-switch__action) {
    left: calc(100% - 17px) !important;
  }
}

.other-switchItem {
  ::v-deep(.el-switch.is-checked .el-switch__core .el-switch__action) {
    left: calc(100% - 17px) !important;
  }

  ::v-deep(.el-switch__core .el-switch__action) {
    left: 1px !important;
  }
}

.splitSpan {
  margin: 0 4px;
}

.del-btn {
  cursor: pointer;
  margin-left: 2px;
  padding: 5px;
  border-radius: 50%;

  &:hover {
    background: #dddfe5;
  }
}
</style>
