<template>
  <div>
    <!-- <el-form label-position="top">
      <el-form-item class="options" label="扣款/扣分原因选项设置">
        <template #label>
          <div class="option-item-label">
            <span>扣款/扣分原因（鼠标拖拽排序）</span>
            <el-button icon="el-icon-plus" link type="primary"
              @click="modelValue[3].props.options.push({ name: '', value: '' })">新增选项
            </el-button>
          </div>
        </template>
        <draggables item-key="id" v-model="modelValue[3].props.options" class="option-items"
          :component-data="{ tag: 'div', type: 'transition-group' }" handler=".el-icon-rank" v-bind="dragOption">
          <template #item="{ element, index }">
            <div class="option-item">
              <el-input v-model="element.value" style="width: 100px;" placeholder="选项value值" clearable />
              <span class="splitSpan">~</span>
              <el-input v-model="element.name" placeholder="选项名称" style="width: 140px;" />
              <icon name="el-icon-delete del-btn" @click="modelValue[3].props.options.splice(index, 1)"></icon>
            </div>
          </template>
        </draggables>
      </el-form-item>
      <el-form-item label="扣款/扣分原因多选模式" class="switchItem">
        <el-switch v-model="modelValue[3].props.multiple"></el-switch>
      </el-form-item>
    </el-form> -->
  </div>
</template>

<script>
import draggables from 'vuedraggable'

export default {
  components: { draggables },
  props: {
    modelValue: {
      type: Object,
      default: () => {
        return {}
      },
    },
  },
  data() {
    return {
      dragOption: {
        animation: 300,
        sort: true,
        group: 'option'
      },
    }
  },
  methods: {},
  emits: ['update:modelValue'],
}
</script>

<style lang="less" scoped>
:deep(.options) {
  .el-form-item__label {
    display: block;
    width: 100%;
    text-align: left;
    padding: 0;
  }

  .el-icon-rank {
    padding-right: 5px;
    cursor: move;
  }
}

.option-div {
  width: 100%;
}

.option-item {
  display: flex;
  align-items: center;
  padding-bottom: 6px;

  :deep(.el-input) {
    // width: 250px;
    float: right;
  }
}

.option-item-label {
  display: flex;
  justify-content: space-between;

  button {
    float: right;
  }
}

.switchItem {
  margin-bottom: 15px;

  // ::v-deep .el-switch.is-checked .el-switch__core .el-switch__action {
  //   left: 1px !important;
  // }

  // ::v-deep .el-switch__core .el-switch__action {
  //   left: calc(100% - 17px) !important;
  // }
}

.splitSpan {
  margin: 0 4px;
}

.del-btn {
  cursor: pointer;
  margin-left: 2px;
  padding: 5px;
  border-radius: 50%;

  &:hover {
    background: #DDDFE5;
  }
}
</style>
