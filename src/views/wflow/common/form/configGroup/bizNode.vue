<template>
  <div>
    <div v-if="showAlert" class="alertBox">
      <p class="title">补卡申请通过后会更改员工考勤状态</p>
      <p>1.修改缺卡记录为正常</p>
      <p>2.加班忘打卡可以补加班卡</p>
      <p>3.上班忘打卡，可以通过补卡更新为正常</p>
      <el-button type="text" @click="showAlert = false">我知道了</el-button>
    </div>
    <p>打卡次数和时间限制：<span class="blueColor" @click="handleGo">去设置</span></p>
  </div>
</template>

<script>

export default {
  props: {
    modelValue: {
      type: Object,
      default: () => {
        return {}
      },
    },
  },
  data() {
    return {
      showAlert: true
    }
  },
  methods: {
    handleGo(){
      this.$router.push('/e_settings/attendance/attendance_bc')
    },
  },
  emits: ['update:modelValue'],
}
</script>

<style lang="less" scoped>
.alertBox {
  background-color: #e6f7ff;
  color: #333;
  padding: 12px;
  border-radius: 6px;
  margin-top: 20px;
  font-size: 14px;
  line-height: 2;

  p {
    margin: 0;
  }

  .title {
    font-weight: bold;
  }
}
</style>
