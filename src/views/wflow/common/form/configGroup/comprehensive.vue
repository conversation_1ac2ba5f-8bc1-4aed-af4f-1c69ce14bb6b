<template>
  <div>
    <p
      >审批过程中，审批人可按照权限查看表单中的数据，审批通过后，审批单中的数据将自动同步到数据源</p
    >
    <el-form label-position="top">
      <el-form-item label="允许代他人提交" class="switchItem">
        <span class="spanClass"
          >审批单将按照代提交人的流程进行审批，可在代提交人的「已发起」列表中查看</span
        >
        <el-switch
          v-model="modelValue[0].props.isShow"
          active-color="#C0CCDA"
          inactive-color="#409EFF"
        ></el-switch>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
export default {
  props: {
    modelValue: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {}
  },
  methods: {},
  emits: ['update:modelValue']
}
</script>

<style lang="less" scoped>
.spanClass {
  line-height: 1;
  margin: 5px 0;
  color: rgba(25, 31, 37, 0.4);
}
.switchItem {
  margin-bottom: 15px;

  ::v-deep(.el-switch.is-checked .el-switch__core .el-switch__action) {
    left: 1px !important;
  }

  ::v-deep(.el-switch__core .el-switch__action) {
    left: calc(100% - 17px) !important;
  }
}
</style>
