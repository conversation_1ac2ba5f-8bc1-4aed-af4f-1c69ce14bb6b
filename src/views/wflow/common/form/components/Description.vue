<template>
  <div :style="{ color: color }">
    <!-- <icon name="el-icon-warning"></icon> -->
    <span class="spanClass"> {{ placeholder }}</span>
  </div>
</template>

<script>
import componentMinxins from '../ComponentMinxins'

export default {
  mixins: [componentMinxins],
  name: 'Description',
  components: {},
  props: {
    color: {
      type: String,
      default: '#868686',
    },
    placeholder: {
      type: String,
      default: '只是一段说明文字',
    },
  },
  data() {
    return {}
  },
  methods: {},
}
</script>

<style scoped lang="less">
.spanClass {
  font-size: 14px;
  // margin-bottom: 20px;
  display: inline-block;
}
</style>
