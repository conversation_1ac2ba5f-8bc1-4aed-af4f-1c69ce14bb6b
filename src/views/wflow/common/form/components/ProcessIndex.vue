<template>
  <div>
    <div v-if="mode === 'DESIGN'">请选择审批单</div>
    <div v-else-if="mode === 'MO<PERSON>LE' && !readonly">
      <div v-for="(row, i) in _value" :key="row.id" style="padding: 5px 10px" class="selected-process"
        @click="_showProcess(row.id)">
        <div style="width: 100px; display: flex; align-items: center">
          <span style="margin-right: 10px">{{ i + 1 }}. </span>
          <avatar :size="30" :name="row.startUser.name" :src="row.startUser.avatar"></avatar>
        </div>
        <ellipsis style="font-size: 0.9rem" :content="`${row.createTime.substring(5, 16)} 提交的 - ${row.name}`" />
        <icon name="el-icon-close" @click.stop="_value.splice(i, 1)"></icon>
      </div>
      <div class="m-add-row" @click="addRow" v-if="!readonly">
        <icon name="el-icon-plus"></icon>
        <span> {{ placeholder }}</span>
      </div>
      <popup v-model:show="visible" :style="popupStyle" position="left" lazy-render safe-area-inset-bottom>
        <nav-bar placeholder fixed title="请选择" left-text="返回" left-arrow @click-left="visible = false" />
        <tabs v-model:active="mTabActive" @change="typeChange">
          <tab title="我发起的" v-loading="loading">
            <m-process-item type="SUBMIT" :datas="dataList" @click="selectProcess" />
          </tab>
          <tab title="待我处理" v-loading="loading">
            <m-process-item type="UNDO" :datas="dataList" @click="selectProcess" />
          </tab>
          <tab title="已处理的" v-loading="loading">
            <m-process-item type="DONE" :datas="dataList" @click="selectProcess" />
          </tab>
          <tab title="我收到的" v-loading="loading">
            <m-process-item type="SUBMIT" :datas="dataList" @click="selectProcess" />
          </tab>
        </tabs>
        <pagination v-show="total > params.pageSize" class="pagination" v-model="params.pageNo" :total-items="total"
          :items-per-page="params.pageSize" />
      </popup>
    </div>
    <div v-else-if="mode === 'PC' && !readonly">
      <w-dialog title="选择审批单" v-model="visible" width="70%">
        <el-radio-group v-model="type" @change="typeChange">
          <el-radio-button label="SUBMIT">我发起的</el-radio-button>
          <el-radio-button label="UNDO">待我处理</el-radio-button>
          <el-radio-button label="DONE">已处理的</el-radio-button>
          <el-radio-button label="CC">我收到的</el-radio-button>
        </el-radio-group>
        <div v-loading="loading">
          <el-table ref="dataTable" :key="key + '_update'" :data="dataList"
            :header-cell-style="{ background: '#e8e8e8' }" style="width: 100%; margin: 20px 0">
            <el-table-column fixed prop="processDefName" label="审批类型" show-overflow-tooltip
              width="190px"></el-table-column>
            <el-table-column prop="instanceId" label="审批编号" show-overflow-tooltip width="220px"></el-table-column>
            <el-table-column prop="staterUser" show-overflow-tooltip label="发起人" width="160px">
              <template v-slot="scope">
                <avatar :size="35" v-if="type === 'SUBMIT' || type === 'CC'" :name="(scope.row.staterUser || {}).name"
                  :src="(scope.row.staterUser || {}).avatar" />
                <avatar :size="35" v-else :name="(scope.row.owner || {}).name" :src="(scope.row.owner || {}).avatar" />
              </template>
            </el-table-column>
            <el-table-column show-overflow-tooltip
              :prop="type === 'SUBMIT' || type === 'CC' ? 'startTime' : 'createTime'" label="提交时间"
              width="170px"></el-table-column>
            <el-table-column v-if="type === 'UNDO'" show-overflow-tooltip prop="taskCreateTime" label="任务到达时间"
              width="180px"></el-table-column>
            <el-table-column v-else-if="type === 'SUBMIT'" show-overflow-tooltip prop="finishTime" label="结束时间"
              width="170px"></el-table-column>
            <template v-else-if="type === 'DONE'">
              <el-table-column show-overflow-tooltip prop="taskEndTime" label="任务完成时间" width="180px"></el-table-column>
              <el-table-column prop="status" label="处理结果" width="130px">
                <template v-slot="scope">
                  <el-tag v-if="scope.row.taskResult" :type="taskStatus[scope.row.taskResult].type">
                    {{ taskStatus[scope.row.taskResult].text }}
                  </el-tag>
                </template>
              </el-table-column>
            </template>
            <el-table-column show-overflow-tooltip prop="taskName" :label="type === 'DONE' ? '目标节点' : '当前节点'"
              width="120px"></el-table-column>
            <el-table-column prop="status" v-if="type !== 'DONE' && type !== 'UNDO'" label="审批状态" width="120px">
              <template v-slot="scope">
                <process-status :instance="scope.row" />
              </template>
            </el-table-column>
            <el-table-column prop="status" label="操作">
              <template v-slot="scope">
                <el-button type="primary" link @click.stop="selectProcess(scope.row)">选择</el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
        <div style="text-align: right">
          <el-pagination hide-on-single-page background layout="prev, pager, next" :total="total"
            :page-size="params.pageSize" v-model:current-page="params.pageNo" />
        </div>
      </w-dialog>
      <el-button type="primary" link @click="handleAdd" style="margin:10px 0;">+ 添加审批单</el-button>
      <div v-for="(row, i) in _value" :key="row.id" class="selected-process" @click="_showProcess(row.id)">
        <span class="span1">{{ i + 1 }}. </span>
        <div>
          <avatar :size="25" :name="row.startUser.name" :src="row.startUser.avatar"></avatar>
          <ellipsis class="ell-my" :content="`${row.createTime} 提交的 - ${row.name}`" />
          <icon name="el-icon-close" @click.stop="_value.splice(i, 1)"></icon>
        </div>
      </div>
    </div>
    <div v-else>
      <div v-for="(row, i) in _value" :key="row.id" style="padding: 5px 10px" class="selected-process"
        @click="_showProcess(row.id)">
        <span class="span1">{{ i + 1 }}. </span>
        <div>
          <avatar :size="25" :name="row.startUser.name" :src="row.startUser.avatar"></avatar>
          <ellipsis class="ell-my" :content="`${row.createTime} 提交的 - ${row.name}`" />
        </div>
      </div>
    </div>
    <el-drawer :size="isMobile ? '100%' : '560px'" direction="rtl" title="审批详情" v-model="processVisible"
      class="custom-detail-header">
      <instance-preview v-if="processVisible" :instance-id="selectInstance"></instance-preview>
    </el-drawer>
  </div>
</template>

<script>
import taskApi from '@/api/processTask'
import componentMinxins from '../ComponentMinxins'
import ProcessStatus from '@/components/common/ProcessStatus.vue'
import MProcessItem from './subs/MProcessItem.vue'
import InstancePreview from '@/views/wflow/workspace/approval/ProcessInstancePreview.vue'
import { getCcMeList } from '@/api/modelGroup'
import { Popup, NavBar, Pagination, Tab, Tabs, Toast } from 'vant'
import { getTenantId } from '@/utils/auth'

export default {
  mixins: [componentMinxins],
  name: 'ProcessIndex',
  components: {
    ProcessStatus,
    MProcessItem,
    InstancePreview,
    Popup,
    NavBar,
    Pagination,
    Tab,
    Tabs,
  },
  props: {
    modelValue: {
      type: Array,
      default: () => {
        return []
      },
    },
    placeholder: {
      type: String,
      default: '请添加审批单',
    },
    processCode: {
      type: Array,
      default: () => {
        return []
      },
    },
  },
  computed: {
    isMobile() {
      return window.screen.width < 450
    },
  },
  data() {
    return {
      mTabActive: 0,
      visible: false,
      type: 'SUBMIT',
      dataList: [],
      total: 0,
      key: 0,
      loading: false,
      refreshing: false,
      finished: true,
      params: {
        pageSize: 10,
        pageNo: 1,
        finished: null,
        tenantId: getTenantId(),
        // code: this.processCode,
      },
      processVisible: false,
      selectInstance: null,
      popupStyle: {
        height: '100%',
        width: '100%',
        background: '#f7f7f9',
      },
      taskStatus: {
        agree: { type: 'success', text: '已同意' },
        refuse: { type: 'danger', text: '已拒绝' },
        recall: { type: 'warning', text: '已退回' },
        transfer: { type: 'primary', text: '已转交' },
      },
    }
  },
  mounted() {
    this.getCode()
    // this.getDataList()
  },
  methods: {
    getCode() {
      if (this.processCode && this.processCode.length > 0) {
        // this.processCode.forEach((item, index) => {
        //   this.params[encodeURIComponent('code[' + index + ']')] = item
        // })
        this.params.code = (Object.values(this.processCode)).join(',')
      }
    },
    handleAdd() {
      this.visible = true
      this.getDataList()
    },
    onLoad() { },
    getDataList() {
      if (this.isMobile) {
        switch (this.mTabActive) {
          case 0:
            this.getSubmittedList()
            break
          case 1:
            this.getTodoList()
            break
          case 2:
            this.getIdoList()
            break
          case 3:
            this.getCcMeList()
            break
        }
      } else {
        this.key++
        switch (this.type) {
          case 'SUBMIT':
            this.getSubmittedList()
            break
          case 'UNDO':
            this.getTodoList()
            break
          case 'DONE':
            this.getIdoList()
            break
          case 'CC':
            this.getCcMeList()
            break
        }
      }
    },
    typeChange() {
      // this.params.code = this.processCode
      this.params.pageNo = 1
      this.getDataList()
    },
    addRow() {
      this.visible = true
    },
    getTodoList() {
      this.loading = true
      taskApi
        .getUserTodoList(this.params)
        .then((rsp) => {
          this.loading = false
          this.total = rsp.data.total
          this.dataList = rsp.data.records
        })
        .catch((err) => {
          this.loading = false
          this.$showError(err.response.data.message || err.response.data)
        })
    },
    selectProcess(row) {
      if (this.modelValue.findIndex((v) => v.id === row.instanceId) > -1) {
        if (this.mode === 'MOBILE') {
          Toast('该记录已添加')
        } else {
          this.$message.warning('该记录已添加')
        }
        return
      }
      const startUser = row.staterUser || row.owner
      this._value.push({
        id: row.instanceId,
        name: row.processDefName,
        startUser: {
          id: startUser.id,
          avatar: startUser.avatar,
          name: startUser.name,
        },
        createTime: (row.createTime || row.startTime).substring(0, 16),
      })
      this.$emit('update:modelValue', this._value)
      this.visible = false
    },
    getCcMeList() {
      this.loading = true
      getCcMeList(this.params)
        .then((rsp) => {
          this.loading = false
          this.total = rsp.data.total
          this.dataList = rsp.data.records
        })
        .catch((err) => {
          this.loading = false
          this.$showError(err.response.data.message || err.response.data)
        })
    },
    getSubmittedList() {
      this.loading = true
      taskApi
        .getUserSubmittedList(this.params)
        .then((rsp) => {
          this.loading = false
          this.total = rsp.data.total
          this.dataList = rsp.data.records
        })
        .catch((err) => {
          this.loading = false
          this.$showError(err.response.data.message || err.response.data)
        })
    },
    getIdoList() {
      this.loading = true
      taskApi
        .getIdoList(this.params)
        .then((rsp) => {
          this.loading = false
          this.total = rsp.data.total
          this.dataList = rsp.data.records
        })
        .catch((err) => {
          this.loading = false
          this.$showError(err.response.data.message || err.response.data)
        })
    },
    _showProcess(id) {
      this.processVisible = true
      this.selectInstance = id
    },
    showProcess(row) {
      this.processVisible = true
      this.selectInstance = row.instanceId
    },
  },
  watch: {
    'params.pageNo': {
      deep: true,
      handler() {
        this.getDataList()
      },
    },
    'params.pageSize': {
      deep: true,
      handler() {
        this.getDataList()
      },
    },
  },
  emits: ['update:modelValue'],
}
</script>

<style lang="less" scoped>
.selected-process {
  padding: 10px;
  margin: 4px 0 10px;
  background: #f3f4f7;
  cursor: pointer;
  border-radius: 4px;
  display: flex;
  position: relative;

  .span1 {
    line-height: 25px;
    margin-right: 10px;
  }

  &:hover {
    background: #d3d3d3;
    transition: background 0.5s;
  }

  :deep(.avatar .name) {
    text-align: left;
    width: 84%;
  }

  .ell-my {
    font-size: 14px;
    white-space: normal
  }

  :deep(.el-icon-close) {
    position: absolute;
    top: 7px;
    right: 4px;
    padding: 5px;
    cursor: pointer;

    &:hover {
      color: #e74c3c;
    }
  }
}

.m-add-row {
  color: @theme-primary;
  text-align: center;
  cursor: pointer;
  margin: 15px 0 10px;
}
</style>