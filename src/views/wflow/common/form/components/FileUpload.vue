<template>
  <div>
    <div v-if="mode === 'DESIGN'">
      <el-button size="default" icon="el-icon-paperclip" round>选择文件</el-button>
      <ellipsis :row="1" :content="placeholder + sizeTip" hoverTip class="el-upload__tip" />
    </div>
    <div v-else-if="mode === 'PC' && !readonly" v-loading="loading">
      <el-upload :file-list="fileList" :action="uploadUrl" :headers="header" :limit="maxNumber" with-credentials
        :multiple="maxNumber > 1" :data="uploadParams" :on-success="uploadSuccess" auto-upload
        :before-upload="beforeUpload" :on-remove="handleRemove" :on-error="uploadFail" :accept="String(fileTypes)"
        :on-exceed="overLimit">
        <el-button size="default" icon="el-icon-paperclip" round>选择文件</el-button>
        <template #tip>
          <ellipsis :row="1" :content="placeholder + sizeTip" hoverTip class="el-upload__tip" />
        </template>
      </el-upload>
    </div>
    <div v-else-if="mode === 'MOBILE' && !readonly">
      <uploader v-model="fileList" :accept="String(fileTypes)" :multiple="maxNumber > 1"
        :max-count="maxNumber > 0 ? maxNumber : 99" deletable :before-delete="handleRemove" upload-text="选择文件"
        :after-read="afterRead" :max-size="maxSize * 1024 * 1024" @oversize="onOversize" upload-icon="description">
      </uploader>
      <div style="color: #9b9595">{{ placeholder }} {{ sizeTip }}</div>
    </div>
    <div v-else class="file-preview">
      <div v-if="mode === 'PC'" class="file-preview-pc">
        <div v-for="file in filteredFiles" :key="file?.id" class="center-align">
          <ellipsis class="file-item" type="primary" @click="download(file)" :content="file.name">
            <template #pre>
              <icon name="el-icon-document" style="top:1px"></icon>
            </template>
            <template #aft>
              <!-- <el-tag size="small">{{ getSize(file.size) }}</el-tag> -->
            </template>
          </ellipsis>
          <el-button type="text" size="mini" style="margin-left: 10px" @click="downloadUrl(file)"> <icon name="el-icon-download" />下载</el-button>
        </div>
      </div>
      <div v-else>
        <el-row v-for="file in filteredFiles" :key="file?.id">
          <el-col :span="20">
            <ellipsis style="display: inline-block" class="file-item" type="primary" @click="download(file)"
              :content="file.name">
              <template #pre>
                <icon name="el-icon-document"></icon>
              </template>
            </ellipsis>
          </el-col>
          <el-col :span="4">
            <el-tag size="small">{{ getSize(file.size) }}</el-tag>
          </el-col>
        </el-row>
      </div>
    </div>
  </div>
  <readFile ref="readFileRef" />
</template>

<script>
import componentMinxins from '../ComponentMinxins'
import { Uploader, showSuccessToast, showFailToast, showLoadingToast } from 'vant'
import { getAccessToken, getTenantId } from '@/utils/auth'
import { wflowRes } from "@/api/org";
import readFile from '@/views/system/notify/list/readFile.vue'
const message = useMessage()
export default {
  mixins: [componentMinxins],
  name: 'FileUpload',
  components: { Uploader, readFile },
  props: {
    placeholder: {
      type: String,
      default: '请选择附件',
    },
    modelValue: {
      type: Array,
      default: () => {
        return []
      },
    },
    maxSize: {
      type: Number,
      default: 5,
    },
    maxNumber: {
      type: Number,
      default: 20,
    },
    fileTypes: {
      type: Array,
      default: () => {
        return []
      },
    },
  },
  computed: {
    filteredFiles() {
      return this._value.filter(file => file !== null);
    },
    sizeTip() {
      if (this.fileTypes.length > 0) {
        return ` | 只允许上传[${String(this.fileTypes).replaceAll(
          ',',
          '、'
        )}]格式的文件，且单个附件不超过${this.maxSize}MB`
      }
      return this.maxSize > 0 ? ` | 单个附件不超过${this.maxSize}MB` : ''
    },
    fileList: {
      get() {
        return this._value.map((f) => {
          return {
            name: f.name,
            url: this.$getRes(f.url),
            status: 'success',
            file: new File([], f.name, {}),
            isImage: f.isImage,
            previewUrl: f.previewUrl,
            size: f.size
          }
        })
      },
      set(val) {
      },
    },
  },
  data() {
    return {
      loading: false,
      disabled: false,
      catchList: [],
      uploadUrl: `${import.meta.env.VITE_BASE_URL + import.meta.env.VITE_API_URL}/wflow/res`,
      uploadParams: { isImg: false },
      header: {
        'Authorization': 'Bearer ' + getAccessToken(),
        'tenant-id': getTenantId()
      },
      num: 0
    }
  },
  mounted() {
    if (this.mode === 'PC' && !this.readonly) {
      setTimeout(() => {
        this.num = this._value.length
        console.log(this._value.length, this.num, 'ififif')
      }, 1000)
    }
    console.log(this._value.length, this.num)
  },
  methods: {
    beforeUpload(file) {
      if (Array.isArray(file)) {
        for (let i = 0; i < file.length; i++) {
          if (!this.validFile(file[i])) {
            return false
          }
        }
        return true
      } else {
        return this.validFile(file)
      }
    },
    validFile(file) {
      if (this.maxSize > 0 && file.size / 1024 / 1024 > this.maxSize) {
        this.$message.warning(`单个文件最大不超过 ${this.maxSize}MB`)
      } else {
        this.loading = true
        return true
      }
      return false
    },
    getSize(size) {
      if (size > 1048576) {
        return (size / 1048576).toFixed(1) + 'MB'
      } else if (size > 1024) {
        return (size / 1024).toFixed(1) + 'KB'
      } else {
        return size + 'B'
      }
    },
    removeFile(fileId) {
      this.$axios.delete(`${import.meta.env.VITE_BASE_URL + import.meta.env.VITE_API_URL}/wflow/res/${fileId}`, {
        header: {
          'Authorization': 'Bearer ' + getAccessToken(),
          'tenant-id': getTenantId()
        }
      }).then((rsp) => {
        this.$message.success('移除文件成功')
      })
    },
    uploadSuccess(response, file, fileList) {
      this.maxFileLength = 0
      let length = fileList.length
      this.maxFileLength = Math.max(length, this.maxFileLength)
      if (this.maxFileLength !== length) {
        return
      }
      console.log('this.num', this.num, 'length', length)
      setTimeout(async () => {
        if (this.maxFileLength !== length) {
          return
        }
        this.num += 1
        if (this.num == length) {
          console.log('全部上传完毕')
          this.loading = false
          this.submit(fileList)
        }
      }, 0)
      // this.loading = false
      // this.fileList.push(response.data)
      // // let ft = fileList.filter(f => f.status == "success")
      // console.log(response)
      // if (this.fileList.length === fileList.length) {
      //   this.$emit('update:modelValue', this.fileList)
      // }
      // this.$message.success(response.data.name + '上传成功')
    },
    submit(fileList) {
      fileList.forEach((item) => {
        if (item.response && item.response.code == 0) {
          this.fileList.push(item.response.data)
        }
        if (item.response && item.response.code != 0) {
          this.num -= 1
        }
      })
      let ok = fileList.filter((item) => item.response && item.response.code == 0)
      let fail = fileList.filter((item) => item.response && item.response.code != 0)
      if (ok.length > 0 && fail.length > 0) {
        this.$message.warning('部分文件上传成功，部分文件上传失败')
      }
      else if (ok.length > 0) {
        this.$message.success('文件上传成功')
      }
      else if (fail.length > 0) {
        this.$message.error('文件上传失败')
      }
      this.$emit('update:modelValue', this.fileList)
    },
    uploadFail(err) {
      this.loading = false
      this.$message.error('文件上传失败 ' + err)
    },
    overLimit() {
      if (this.mode === 'PC') {
        this.$message.warning('最多只能上传' + this.maxNumber + '个附件')
        this.loading = false
      } else {
        showFailToast('数量超出限制')
      }
    },
    afterRead(file) {
      if (Array.isArray(file)) {
        file.forEach((fl) => {
          this.uploadFile(fl)
        })
      } else {
        this.uploadFile(file)
      }
    },
    uploadFile(file) {
      //上传文件
      const formData = new FormData()
      formData.append('file', file.file)
      formData.append('isImg', false)
      showLoadingToast({ message: '上传中...', forbidClick: true })
      wflowRes(formData).then((res) => {
        if (res.code == 0) {
          this._value.push(res.data)
          this.$emit('update:modelValue', this._value)
          showSuccessToast('上传成功')
        }
      }).catch((err) => {
        showFailToast('上传失败')
      })
    },
    onOversize(file) {
      showFailToast('文件过大')
    },
    handleRemove(file, fileList) {
      let i = this._value.findIndex((v) => v.name === file.name)
      if (i > -1) {
        //this.removeFile(this._value[i].id)
        this._value.splice(i, 1)
        this.$emit('update:modelValue', this._value)
        this.num -= 1
      }
    },
    handlePictureCardPreview(file) {
    },
    handleDownload(file) {
      // console.log(file)
    },
    download(file) {
      // window.open(`${this.$getRes(file.url)}?name=${file.name}`, '_blank')
      this.$refs.readFileRef.open(file, true, '1')
    },
    downloadUrl(file) {
      // console.log("downloadUrl file=",file.name)
      
      const link = document.createElement('a');
          fetch(file.downloadUrl)
            .then((res) => res.blob())
            .then((blob) => {
            // 将链接地址字符内容转变成blob地址
              link.href = URL.createObjectURL(blob);
              link.download = file.name;
              document.body.appendChild(link);
              link.click();
              document.body.removeChild(link);
            });
    }  
  },
  emits: ['update:modelValue'],
}
</script>

<style lang="less" scoped>
:deep(.el-upload-list__item) {
  transition: none;
}

.file-item {
  color: @theme-primary;
  cursor: pointer;
  white-space: normal;
  overflow-wrap: break-word;
}

.file-preview-pc>div {
  margin-bottom: 6px
}

.file-preview-pc>div:nth-last-child(1) {
  margin-bottom: 0
}

:deep(.el-upload-list) {
  min-width: 300px;
}
</style>
