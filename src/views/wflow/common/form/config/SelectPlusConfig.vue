<template>
  <div>
    <el-form-item label="提示文字">
      <el-input v-model="modelValue.placeholder" placeholder="请设置提示语"/>
    </el-form-item>
    <el-form-item label="选项模式">
      <el-radio-group v-model="modelValue.fixed">
        <el-radio :label="true">固定选项</el-radio>
        <el-radio :label="false">远程加载</el-radio>
      </el-radio-group>
    </el-form-item>
    <el-form-item class="options" v-if="modelValue.fixed">
      <template #label>
        <div class="option-item-label">
          <span>选项设置（鼠标拖拽排序）</span>
          <el-button icon="el-icon-plus" link type="primary"
                     @click="modelValue.options.push({name: '', value: ''})">新增选项
          </el-button>
        </div>
      </template>
      <draggables item-key="id" v-model="modelValue.options" class="option-items"
                 :component-data="{tag: 'div', type: 'transition-group'}"
                 handler=".el-icon-rank" v-bind="dragOption">
        <template #item="{element, index}">
          <div class="option-item">
            <el-input v-model="element.value" style="width: 100px;" placeholder="选项value值" clearable/>
            ~
            <el-input v-model="element.name" placeholder="选项名称" style="width: 130px;"/>
            <icon name="el-icon-delete del-btn" @click="modelValue.options.splice(index, 1)"></icon>
          </div>
        </template>
      </draggables>
    </el-form-item>

    <el-form-item label="配置数据源" v-else>
      <el-button icon="el-icon-link" @click="visible = true">编辑http数据源</el-button>
    </el-form-item>
    <el-form-item label="选项展开">
      <el-switch v-model="modelValue.expanding"></el-switch>
    </el-form-item>
    <el-form-item label="多选模式">
      <el-switch v-model="modelValue.multiple"></el-switch>
    </el-form-item>
    <w-dialog title="配置http数据源请求" width="600px" v-model="visible" @opened="loadHttp" @ok="httpOk">
      <http-req ref="http" :show-tip="false" v-model="tempHttp"/>
    </w-dialog>
  </div>
</template>

<script>
import HttpReq from "@/components/common/HttpReq.vue";
import draggables from "vuedraggable";
import ConfigMinxins from '../ConfigMinxins.js'

export default {
  name: "ScoreConfig",
  mixins: [ConfigMinxins],
  components: {draggables, HttpReq},
  data() {
    return {
      visible: false,
      tempHttp: {
        url: '',
        method: 'GET',
        headers: [],
        contentType: 'JSON',
        params: [],
        data: '',
        preHandler: null,
        aftHandler: null
      },
      dragOption: {
        animation: 300,
        sort: true,
        group: 'option'
      },
    }
  },
  methods: {
    loadHttp(){
      this.tempHttp = this.$deepCopy(this.modelValue.http)
    },
    httpOk(){
      this.$refs.http.validate((valid, err) => {
        if (valid){
          this.modelValue.http = this.$deepCopy(this.tempHttp)
          this.visible = false
        }else {
          this.$message.warning(err)
        }
      })
    },
  }
}
</script>

<style scoped lang="less">
:deep(.options) {
  display: flex;
  flex-direction: column;

  .el-form-item__label{
    width: 100% !important;
  }

  .option-item{
    margin-bottom: 5px;
  }

  .del-btn{
    cursor: pointer;
    margin-left: 5px;
    padding: 5px;
    border-radius: 50%;
    &:hover{
      background: #DDDFE5;
    }
  }
}


.option-item-label {
  display: flex;
  text-align: left;
  button {
    float: right;
  }
}

</style>
