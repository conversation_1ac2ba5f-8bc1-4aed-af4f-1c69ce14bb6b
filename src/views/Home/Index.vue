<template>
  <div>
    <el-card class="marginBottom" shadow="never" v-if="notifyData.length > 0 && isFlag">
      <el-row :gutter="30">
        <el-col :xl="12" :lg="12" :md="12" :sm="24" :xs="24">
          <el-carousel height="160px" :autoplay="false" class="carousel-item-box">
            <el-carousel-item v-for="(item, index) in notifyData" :key="index" @click="goNotify(item.id)">
              <h2 class="carousel-text">{{ item.title }}</h2>
              <img :src="i1" alt="轮播图片" class="carousel-img" />
            </el-carousel-item>
          </el-carousel>
        </el-col>
        <el-col :xl="12" :lg="12" :md="12" :sm="24" :xs="24">
          <span class="cardTitle" @click="goNotify('')">
            公告
            <Icon icon="ep:arrow-right" color="var(--el-text-color-primary)" />
          </span>
          <span v-for="(item, index) in notifyData" :key="index" class="right-info" @click="goNotify(item.id)">{{
            item.title }}</span>
        </el-col>
      </el-row>
    </el-card>
    <el-row :gutter="15">
      <el-col :xl="isFlag ? 16 : 24" :lg="isFlag ? 16 : 24" :md="isFlag ? 12 : 24" :sm="24" :xs="24">
        <detail :type="'index'" @child-event='childEvent'></detail>
        <!-- <applicationCenter v-if="isFlag" :type="'index'"></applicationCenter> -->
      </el-col>
      <el-col :xl="8" :lg="8" :md="12" :sm="24" :xs="24" :loading="loading" v-if="isFlag">
        <el-card class="oaCard marginBottom">
          <span class="cardTitle" @click="goOA('')">
            事项
            <Icon icon="ep:arrow-right" color="var(--el-text-color-primary)" />
          </span>
          <el-row :gutter="8" class="row-right">
            <el-col :span="8" :xl="8" :lg="8" :md="24" :sm="24" :xs="24">
              <div class="matter-col" @click="goOA('0')">
                <span class="matterText">待我处理</span>
                <span class="matterNumber">{{ taskCount.todo }}</span>
              </div>
            </el-col>
            <el-col :span="8" :xl="8" :lg="8" :md="24" :sm="24" :xs="24">
              <div class="matter-col" @click="goOA('2')">
                <span class="matterText">我发起的</span>
                <span class="matterNumber">{{ taskCount.mySubmited }}</span>
              </div>
            </el-col>
            <el-col :span="8" :xl="8" :lg="8" :md="24" :sm="24" :xs="24">
              <div class="matter-col" @click="goOA('3')">
                <span class="matterText">我收到的</span>
                <span class="matterNumber">{{ taskCount.cc }}</span>
              </div>
            </el-col>
          </el-row>
        </el-card>
        <el-card class="oaCard">
          <messageList></messageList>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>
<script type="ts" setup>
defineOptions({ name: 'Index' })
import { useUserStore } from '@/store/modules/user'
import i1 from '@/assets/imgs/banner1.png'
import { Icon } from '@/components/Icon'
import detail from '@/views/wflow/workspace/oa/fromsApp_new.vue'
import applicationCenter from '@/views/wflow/workspace/oa/applicationCenter.vue'
import { getProcessCountData } from '@/api/modelGroup'
import { getNoticeList } from '@/api/notify'
import { useRouter } from 'vue-router'
import messageList from '@/views/wflow/bb_oa/messageList.vue'
import { useMyStore } from '@/store/modules/jump'
import { CACHE_KEY, useCache } from '@/hooks/web/useCache'
const router = useRouter()
const { t } = useI18n()
const userStore = useUserStore()
const loading = ref(true)
const avatar = userStore.getUser.avatar
const username = userStore.getUser.nickname
const isAdmin = ref(0)//是否配置可见 1可见


const isFlag = ref(true)
const childEvent = (v) => {
  isFlag.value = v
}

// 审批模块剩余数字接口
const taskCount = ref({})
const getTotal = async () => {
  loading.value = true
  try {
    const res = await getProcessCountData()
    taskCount.value = res.data
  } finally {
    loading.value = false
  }
}
// 进oa模块
const goOA = (type) => {
  router.push('/oa/formsList')
}
// 获取公告列表
const notifyData = ref([])
// const notifyTitle = ref([])
const getNotify = async () => {
  loading.value = true
  try {
    const res = await getNoticeList({
      pageNo: 1,
      pageSize: 5,
      isAdmin: isAdmin.value
    })
    notifyData.value = res.data.list
    // notifyTitle.value = res.data.list
  } finally {
    loading.value = false
  }
}
// 进公告列表模块
const goNotify = (id) => {
  if (id) {
    router.push('/notify/list/person?id=' + id)
  } else {
    router.push('/notify/list/person')
  }
}

const getIfAdmin = () => {
  const { wsCache } = useCache()
  const permissions = wsCache.get(CACHE_KEY.USER).permissions
  const hasPermission = permissions.some(item => item == 'system:notice:recordHidden')
  isAdmin.value = hasPermission ? 1 : 0
  getNotify()
}
const getAllApi = async () => {
  await Promise.all([
    getTotal(),
    // getNotify()
    getIfAdmin()
  ])
  loading.value = false
}
getAllApi()

const store = useMyStore()
watch(() => store.wsPush, (newValue, oldValue) => {
  if (newValue) {
    getTotal()
  }
})


// 类似onshow  每次进入调用获取下列表
const { currentRoute, push, replace } = useRouter()
watch(
  () => currentRoute.value,
  () => {
    if(currentRoute.value.path == '/home'){
      // console.log("currentRoute.value change",currentRoute.value)
      getNotify()
    }
     
  }
)

</script>
<style lang="less" scoped>
.marginBottom {
  margin-bottom: 15px;
}

.carousel-item-box {
  position: relative;
  border-radius: 10px;
  cursor: pointer;
}

.carousel-text {
  position: absolute;
  left: 50px;
  top: 10px;
  color: #fff;
}

.carousel-img {
  width: 100%;
}

.cardTitle {
  cursor: pointer;
  font-size: 16px;
  font-weight: bold;
  display: flex;
  align-items: center;
  margin-bottom: 15px;

  i {
    margin-left: 4px;
  }
}

.right-info {
  cursor: pointer;
  display: block;
  margin-bottom: 6px;
  width: fit-content;
}

.right-info:hover {
  font-weight: bold;
}

.el-card {
  border-radius: 8px;
  border: 1px solid #eceded;
}

.row-right {
  margin-bottom: -10px;
}

.matter-col {
  cursor: pointer;
  background: #f3f4f7;
  border-radius: 8px;
  padding: 12px 12px;
  box-sizing: border-box;
  margin-bottom: 10px;

  span {
    display: block;
  }

  .matterNumber {
    margin-top: 10px;
    color: #3370ff;
    font-weight: bold;
    font-size: 20px;
  }
}

@media (max-width: 992px) {
  .cardTitle {
    margin: 15px 0 10px 0;
  }
}
</style>
