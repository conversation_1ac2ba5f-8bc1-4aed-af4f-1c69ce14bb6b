<template>
  <div>



    <el-dialog v-model="directBuyVisible" title="立即购买" width="600" @close="handleClose">

      <div class="center-view">
        <div class="center-align" style="width: 430px;justify-content: space-between;">
          <div class="center-align">
            <div v-if="active == 0" class="step-dot center-view">
              1
            </div>
            <img v-if="active > 0" style="width:20px;height:20px;" src="@/assets/images/business/selected.png" />

            <div v-if="active == 0" class="step-text">请选择规格</div>
            <div v-else class="step-text2">请选择规格</div>

          </div>
          <div>
            <div v-if="active == 0" style="border-bottom: 1px dotted #000;width:60px;"></div>
            <div v-else style="border-bottom: 1px solid #ECEDED;width:60px;"></div>
          </div>
          <div class="center-align">
            <div v-if="active == 0" class="step-dot2 center-view">
              2
            </div>
            <div v-if="active == 1" class="step-dot center-view">
              2
            </div>
            <img v-if="active > 1" style="width:20px;height:20px;" src="@/assets/images/business/selected.png" />
            <div v-if="active == 1" class="step-text">请支付订单</div>
            <div v-else class="step-text2">请支付订单</div>
          </div>

          <div>
            <div v-if="active == 1" style="border-bottom: 1px dotted #000;width:60px;"></div>
            <div v-else style="border-bottom: 1px solid #ECEDED;width:60px;"></div>
          </div>
          <div class="center-align">
            <div v-if="active == 2" class="step-dot center-view">
              3
            </div>
            <div v-else class="step-dot2 center-view">
              3
            </div>
            <div v-if="active == 2" class="step-text">完成支付</div>
            <div v-else class="step-text2">完成支付</div>
          </div>
        </div>

      </div>



      <div v-if="active == 0">
        <div class="center-align" style="margin-top:30px;">
          <div class="title" style="">
            产品价格：
          </div>
          <div v-if="specsInfo.price" style="font-weight: 500;font-size: 14px;line-height: 20px;">
            <span v-if="specsInfo.price == '-1'">
              <!-- 无货 -->

              <span v-if="tempSpecListText.every(item => item)">暂无此规格</span>
              <span v-else> 请选择规格</span>
            </span>
            <span v-else style="color: #FF5757;">
              ￥
              {{
      orderInfo.orderDetail.id &&
        !isHandleSelectChange &&
        orderForm.buyNum === orderInfo.orderDetail.buyNum
        ? orderInfo.orderDetail.orderAmount
        : (Number(specsInfo.price) * orderForm.buyNum).toFixed(2)
    }}
            </span>



          </div>
        </div>
        <div class="center-align" style="margin-top:24px;margin-bottom:12px;">
          <div class="title" style="">
            购买数量：
          </div>
          <div>

            <el-input-number v-model="orderForm.buyNum" :min="1" />

          </div>
        </div>

        <div v-for="(item, index) in  specsInfo.specs " :key="index" style="margin-top:12px;">
          <div class="title" style="">
            {{ item.name }}：
          </div>
          <div style="display: flex;flex-wrap: wrap;margin-top: 16px;">
            <div v-for="(childItem, childIndex) in  item.children " @click="handleSelect(childItem, index)"
              :key="childIndex" class="spec-item2 center-view"
              :class="{ 'spec-item': tempSpecListText.indexOf(childItem.name) != -1, 'a': childItem && tempSpecListText.indexOf(childItem.name) == -1 }"
              style="">
              {{ childItem.name }}
            </div>
          </div>
        </div>
      </div>

      <div v-if="active == 1">

        <div class="center-align" style="margin-top: 20px">
          <div style="width: 70px; font-weight: 500; font-size: 14px; color: #303133; line-height: 20px">
            支付方式：
          </div>
          <div class="center-align" style="font-size: 14px; color: #ff5757; line-height: 20px">
            <el-radio-group v-model="payForm.payType" @change="payTypeChange">
              <el-radio :label="1">微信</el-radio>
              <el-radio :label="2">支付宝</el-radio>
            </el-radio-group>
          </div>
        </div>

        <div class="center-view" style="
          border-radius: 4px;
          padding: 0px 32px 16px;
          flex-flow: column;
        ">
          <div style="font-weight: 500; font-size: 18px; color: #303133; line-height: 25px;margin-top:20px;">
            请使用
            <span v-if="payForm.payType == 1">微信</span>
            <span v-if="payForm.payType == 2">支付宝</span>
            扫码
          </div>

          <div style="display: flex; margin-top: 12px">
            <img style="width: 160px; height: 160px; margin-right: 24px" :src="pqyCode" />



          </div>

          <div class="center-align">
            <div style="
                font-weight: 500;
                font-size: 14px;
                color: #303133;
                line-height: 20px;
                margin-top: 20px;
              ">
              支付金额：
            </div>

            <div style="
                display: flex;
                align-items: flex-end;
                margin-top: 14px;
                font-weight: 500;
                font-size: 14px;
                color: #ff5757;
                line-height: 20px;
              ">
              <div>￥</div>
              <div style="font-size: 20px">{{ orderInfo.orderDetail.orderAmount }}</div>
            </div>
          </div>
          <div class="center-view" style="margin-top:7px;font-size: 14px;color: #909399;line-height: 20px;">
            订单编号：{{ orderInfo.orderDetail.orderNum }}

          </div>



        </div>



      </div>

      <div v-if="active == 2">
        <div class="center-view" style="padding: 83px 0 138px 0; flex-flow: column">
          <img style="width: 72px; height: 72px; margin-bottom: 16px" src="@/assets/images/business/success.png" />

          <div style="font-weight: 500; font-size: 18px; color: #303133; line-height: 25px">
            购买成功
          </div>

          <div style="font-size: 14px; color: #a2a3a5; line-height: 20px; margin-top: 8px">
            请稍等，商家后续会与您联系
          </div>
        </div>

      </div>




      <template #footer v-if="active == 0">
        <div class="dialog-footer">
          <el-button @click="handleClose()">取消</el-button>
          <el-button type="primary" @click="toNext()"> 确定 </el-button>
        </div>
      </template>
    </el-dialog>



  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, reactive, unref, nextTick, computed } from 'vue'
import starYellow from '@/assets/images/business/star_yellow.png'
import starGray from '@/assets/images/business/star_gray.png'
import { throttle } from 'lodash'
import {
  addRecommendOrder,
  getProductPage,
  getProductDetail,
  getProductCategoryList,
  getProductCommentPage,
  postYqbOrderPay,
  getYqbOrderPayStatus,
  getYqbOrderPage,
  getYqbProductSpecList,
  recommendOrderUpdate
} from '@/api/common'
const message = useMessage() // 消息弹窗

const directBuyVisible = ref(false)
const active = ref(0)


const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  productDetail: {
    type: Object,
    default: () => ({})
  },
  tempActive: {
    type: Number,
    default: 0
  },
  tempOrderDetail: {
    type: Object,
    default: () => ({})
  }


})

// active.value = props.tempActive

watch(
  () => props.visible,
  (newVal, oldVal) => {
    console.log("dialogVisible change", newVal)


  }
)


watch(
  [() => props.tempActive, () => props.tempOrderDetail, () => props.visible],
  ([newActive, newOrderDetail, newVisible], [oldActive, oldOrderDetail, oldVisible]) => {
    console.log("tempActive change", newActive);
    console.log("tempOrderDetail change", newOrderDetail);



    if (newOrderDetail) {
      orderInfo.orderDetail = newOrderDetail;
      payForm.id = newOrderDetail.id
      payForm.type = 1
    }

    if (newActive) {
      if (newActive == 1) {
        console.log("触发设置newActive", newActive)
        active.value = newActive;

        if (newVisible && active.value == 1) {
          console.log("触发")
          getPayCode(1, 1)
        }
      }
    }

    if (newVisible) {
      isHandleSelectChange.value = false
      specHistory.value=[]

      // 是已有订单信息
      if (orderInfo.orderDetail.id) {
        orderForm.type = orderInfo.orderDetail.type
        orderForm.specs = orderInfo.orderDetail.specs
        orderForm.buyNum = orderInfo.orderDetail.buyNum

        if (orderInfo.orderDetail.type != 1) {
          active.value = 0
        }

        if(orderForm.specs){
          specHistory.value=orderForm.specs.split(",")
        }


        // active.value = 0

      } else {
        // 没有订单信息 
        orderForm.type = 1
        orderForm.specs = ''
        orderForm.buyNum = 1
        active.value = 0
      }




      tempSpecListText.value = []
      // tempSpecList.value = []

      getProductSpecList()



      directBuyVisible.value = newVisible

    }

  }
);



const specsInfo = ref({})
const specForm = reactive({
  productId: null,   // 产品id
  specs: "",         // 规格列表
})
// 已选择的文字
const tempSpecListText = ref([])
// 已选择的id
// const tempSpecList = ref([])


const getProductSpecList = async () => {
  // 根据 specHistory 的顺序来设置 orderForm.specs
  // let text = []

  // for (let i = 0; i < tempSpecListText.value.length; i++) {
  //   if (tempSpecListText.value[i]) {
  //     text.push(tempSpecListText.value[i])
  //   }
  // }
  specForm.productId = props.productDetail.id
  // specForm.specs = text.join(",")
  specForm.specs = specHistory.value.join(",")



  const response = await getYqbProductSpecList(specForm)
  if (response.code === 0) {
    // console.log('getProductSpecList res=', response)
    specsInfo.value = response.data

    // 如果没有则初始化数量
    // if (tempSpecList.value.length == 0) {
    //   for (let i = 0; i < response.data.specs.length; i++) {
    //     tempSpecList.value.push(null)
    //   }
    // }
    if (tempSpecListText.value.length == 0) {
      for (let i = 0; i < response.data.specs.length; i++) {
        tempSpecListText.value.push('')
      }
    }

    specsInfo.value = response.data


    if (orderForm.specs) {

      // 将 spec 字符串分割为数组
      const specArray = orderForm.specs.split(',');
      console.log('specArray=', specArray)

      // 遍历 specs 数组
      response.data.specs.forEach((specItem, index) => {
        // 遍历 specItem.children 数组
        specItem.children.forEach((child) => {
          // 检查 specArray 中是否存在当前 child 的 name
          const specIndex = specArray.findIndex((s) => s === child.name);

          if (specIndex !== -1) {
            // 如果找到匹配项，更新 tempSpecListText 和 tempSpecList
            tempSpecListText.value[index] = child.name;
            // tempSpecList.value[index] = child.id;
          }
        });
      });

      orderForm.specs = ""
      getProductSpecList()

      console.log('tempSpecListText.value=', tempSpecListText.value)
      // console.log('tempSpecList.value=', tempSpecList.value)





    }




  } else {
    message.error(response.msg)
  }


}



const emit = defineEmits(['handleClose', 'paySuccess'])

const handleClose = () => {
  directBuyVisible.value = false

  emit('handleClose', false)
}

const orderForm = reactive({
  productId: null,
  type: 1,
  buyNum: 1,
  specs: "",
})


const isHandleSelectChange = ref(false)
const specHistory = ref([])

const handleSelect = (item, index) => {
  isHandleSelectChange.value = true
  if (orderForm.specs) {
    orderForm.specs = ""
  }

  // 如果点击的是已选中的规格，不做任何操作
  if (tempSpecListText.value[index] === item.name) {
    return
  }

  // 如果同级别已有选择，则替换
  if (tempSpecListText.value[index]) {
    // 找到specHistory中同级别的旧值
    const oldValue = tempSpecListText.value[index]
    // 替换specHistory中的值
    const historyIndex = specHistory.value.indexOf(oldValue)
    if (historyIndex !== -1) {
      specHistory.value[historyIndex] = item.name
    }
  } 
  // 如果是首次选择该级别，则添加到specHistory末尾
  else {
    specHistory.value.push(item.name)
  }

  // 更新当前选择的规格
  tempSpecListText.value[index] = item.name
  getProductSpecList()
}



const orderInfo = reactive({
  orderDetail: {}

})



const payForm = reactive({
  id: null,
  payType: 1
})
const pqyCode = ref('')
const mchOrderNo = ref('')

// 获取支付二维码
const getPayCode = async (payType: number, type: number) => {
  payForm.payType = payType
  const response = await postYqbOrderPay(payForm)

  if (response.code === 0) {
    console.log('getPayCode res=', response)

    if (type == 2) {
      message.success('切换成功')
    }

    mchOrderNo.value = response.data.mchOrderNo
    pqyCode.value = response.data.url

  } else {
    message.error(response.msg)
  }
}

const payTypeChange = async (val: any) => {
  console.log('payTypeChange=', val)
  getPayCode(val, 2)
}



const toNext = async () => {


  if (!tempSpecListText.value.every(item => item)) {

    message.error('请选择规格')

    return
  }
  if (specsInfo.value.price == '-1') {

    message.error('暂无此商品规格')

    return
  }


   // 根据 specHistory 的顺序来设置 orderForm.specs
   orderForm.specs = specHistory.value.join(",")
  // let text = []
  // for (let i = 0; i < tempSpecListText.value.length; i++) {
  //   text.push(tempSpecListText.value[i])
  // }
  // orderForm.specs = text.join(",")

  // 已有订单id
  if (orderInfo.orderDetail.id) {

    if ((orderForm.specs == orderInfo.orderDetail.specs) && (orderForm.buyNum == orderInfo.orderDetail.buyNum)) {
      console.log("无需更新")

      payForm.id = orderInfo.orderDetail.id
      active.value = 1
      getPayCode(1, 1)


    } else {
      const response = await recommendOrderUpdate({
        id: orderInfo.orderDetail.id,
        buyNum: orderForm.buyNum,
        specs: orderForm.specs,
      })
      if (response.code === 0) {
        orderInfo.orderDetail = response.data
        payForm.id = response.data.id
        active.value = 1
        getPayCode(1, 1)

      }
    }









  } else {
    // 没有订单id 要创建订单


    orderForm.productId = props.productDetail.id



    const response = await addRecommendOrder(orderForm)

    if (response.code === 0) {
      // recommendVisible.value = false
      // console.log("addRecommendOrder res=", response)
      //  message.success('提交成功')

      if (props.productDetail.chargeRule == 2) {
        if (response.data.id) {
          orderInfo.orderDetail = response.data
          payForm.id = response.data.id
          active.value = 1
          getPayCode(1, 1)

          // const postYqbOrderPay = await postYqbOrderPay({
          //   id: response.data.id,
          //   payType:1,
          // })
        }
      } else {
        active.value = 2
      }
    }


  }








  // getPayCode(1, 1)
}










// 轮询相关变量
let pollTimer = null
const POLL_INTERVAL = 2000 // 2秒间隔

// 模拟 API 调用（替换为你的真实接口）
const fetchPaymentStatus = async () => {
  try {
    const response = await getYqbOrderPayStatus({ mchOrderNo: mchOrderNo.value }) // 替换为你的接口调用
    if (response.code === 0) {
      if (response.data && response.data.state === 2) {
        // 成功条件：停止轮询
        stopPolling()
        active.value = 2
        emit('paySuccess')

      }
    }
    return response
  } catch (error) {
    console.error('轮询请求失败:', error)
    // 失败时可根据需求处理（如重试机制）
    throw error
  }
}

// 启动轮询
const startPolling = () => {
  // 先清除可能存在的旧定时器
  stopPolling()

  // 立即执行第一次请求
  fetchPaymentStatus()

  // 设置定时器
  pollTimer = setInterval(async () => {
    if (!(active.value == 1 && directBuyVisible.value)) {
      stopPolling()
      return
    }

    await fetchPaymentStatus()
  }, POLL_INTERVAL)
}

// 停止轮询
const stopPolling = () => {
  if (pollTimer) {
    clearInterval(pollTimer)
    pollTimer = null

    console.log('轮询已停止')
  }
}

// 监听状态变化
watchEffect(() => {
  if (active.value == 1 && directBuyVisible.value) {
    console.log('开始轮询')
    setTimeout(() => {
      startPolling()
    }, 2000)
  } else {
    stopPolling()
  }
})

// 组件卸载时自动清理
onUnmounted(stopPolling)



const buyDialogVisible = ref(false)
const buyDialogClose = () => {
  buyDialogVisible.value = false
}




</script>

<style scoped lang="less">
.step-dot {
  width: 20px;
  height: 20px;
  background: #3370FF;
  border-radius: 50%;
  font-weight: 500;
  font-size: 11px;
  color: #FFFFFF;
  line-height: 15px;
}

.step-dot2 {
  width: 20px;
  height: 20px;
  border: 1px solid #C0C4CC;
  border-radius: 50%;
  font-weight: 500;
  font-size: 11px;
  color: #C0C4CC;
  line-height: 15px;
}

.step-text {
  font-weight: 500;
  font-size: 12px;
  color: #303133;
  line-height: 17px;
  margin-left: 9px;
}

.step-text2 {
  font-size: 12px;
  color: #909399;
  line-height: 17px;
  margin-left: 9px;
}

.title {
  font-weight: 500;
  font-size: 14px;
  color: #303133;
  line-height: 20px;
  width: 100px;
  margin-right: 10px;
}

.spec-item {
  height: 32px;
  padding: 0 20px;
  background: #3370FF !important;
  border-radius: 18px;
  font-weight: 500;
  font-size: 14px;
  color: #FFFFFF !important;
  line-height: 20px;
  margin-right: 12px;
  margin-bottom: 12px;
  cursor: pointer;
}



.spec-item2 {
  height: 32px;
  padding: 0 20px;
  border: 1px solid #DCDFE6;
  border-radius: 18px;
  font-weight: 500;
  font-size: 14px;
  color: #303133;
  line-height: 20px;
  margin-right: 12px;
  margin-bottom: 12px;
  cursor: pointer;
}


.no-allow {
  opacity: 0.5;
  cursor: not-allowed !important;
}
</style>