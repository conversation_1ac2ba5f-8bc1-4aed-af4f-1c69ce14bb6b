<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form class="-mb-15px" :model="queryParams" ref="queryFormRef" :inline="true" label-width="68px">
      <el-form-item label="员工姓名" prop="nickName">
        <el-input v-model="queryParams.nickName" placeholder="请输入员工姓名" clearable @keyup.enter="handleQuery"
          class="!w-240px" />
      </el-form-item>
      <el-form-item label="员工账号" prop="userName">
        <el-input v-model="queryParams.userName" placeholder="请输入员工账号" clearable @keyup.enter="handleQuery"
          class="!w-240px" />
      </el-form-item>
      <el-form-item label="时间" prop="dateRange">
        <el-date-picker v-model="createTimeRange" type="daterange" range-separator="至" start-placeholder="开始日期"
          end-placeholder="结束日期" value-format="YYYY-MM-DD" class="!w-240px" @change="hanlderTimeChange" />
      </el-form-item>
      <el-form-item label="定位状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择定位状态" clearable class="!w-240px">
          <el-option v-for="dict in statusOptions" :key="dict.value" :label="dict.label" :value="dict.value" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button @click="handleQuery" type="primary">
          <Icon icon="ep:search" class="mr-5px" />
          查询
        </el-button>
        <el-button @click="resetQuery">
          <Icon icon="ep:refresh" class="mr-5px" />
          重置
        </el-button>
      </el-form-item>

    </el-form>

    <div style="margin-top: 20px;">
      <el-button @click="launch()" type="primary">

        发起定位
      </el-button>
    </div>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table v-loading="loading" :data="list">
      <el-table-column label="序号" align="center" width="100" type="index" />
      <el-table-column label="发起日期" align="center" width="120" prop="sendDate" />
      <el-table-column label="员工姓名" align="center" width="120" prop="nickName" />
      <el-table-column label="员工账号" align="center" prop="userName" />
      <el-table-column label="部门" align="center" width="280" prop="deptName" />
      <el-table-column label="当前位置" align="center" prop="address" />
      <el-table-column label="定位状态" align="center" prop="status">
        <template #default="scope">
          <div v-for="dict in statusOptions" :key="dict.value">
            <el-tag v-if="scope.row.status === dict.value" :type="dict.type" size="small">
              {{ dict.label }}
            </el-tag>
          </div>
        </template>
      </el-table-column>

      <el-table-column label="操作">
        <template #default="scope">
          <el-button v-if="scope.row.status === 3" link type="primary" @click="reRequest(scope.row)">
            重新请求</el-button>
          <el-button link type="primary" @click="showMapRoute(scope.row)">
            地图路线</el-button>
          <el-button link type="primary" @click="detailShow(scope.row)">
            定位详情</el-button>
          <el-button link type="primary" @click="reportShow(scope.row)">
            定位上报</el-button>
          <el-button v-if="scope.row.status === 4" link type="primary" @click="auditShow(scope.row)">
            审核</el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination :total="total" v-model:page="queryParams.pageNo" v-model:limit="queryParams.pageSize"
      @pagination="getList" />
  </ContentWrap>

  <!-- 发起定位弹框 -->
  <el-dialog v-model="launchVisible" title="发起定位" width="500">
    <div style="padding: 20px 20px 60px 20px; ">
      <div style="margin-bottom: 10px">
        员工姓名：
      </div>
      <el-select v-model="userIds" clearable placeholder="可输入搜索" multiple filterable>
        <el-option v-for="item in userList" :key="item.id" :label="item.nickname" :value="item.id" />
      </el-select>
    </div>
    <template #footer>
      <el-button @click="launchVisible = false">取 消</el-button>
      <el-button type="primary" @click="submitLaunch()">确 定</el-button>
    </template>
  </el-dialog>

  <!-- 定位取消审核弹框 -->
  <el-dialog v-model="auditVisible" title="审核" width="500">
    <div style="padding: 20px 20px 60px 20px; ">
      <div style="margin-bottom: 10px">
        <span style="color: red;">*</span> 定位取消审核：
      </div>

      <el-radio-group v-model="auditResult">
        <el-radio label="1">审核通过</el-radio>
        <el-radio label="2">审核拒绝</el-radio>
      </el-radio-group>

    </div>
    <template #footer>
      <el-button @click="auditVisible = false">取 消</el-button>
      <el-button type="primary" @click="submitAudit()">确 定</el-button>
    </template>
  </el-dialog>

  <!-- 地图线路弹框 -->
  <el-dialog v-model="mapRouteVisible" title="地图线路" width="1200" top="5vh">
    <div style="padding: 20px 20px 60px 20px; ">

      <el-form class="-mb-15px" :model="mapRouteQueryParams" ref="mapRouteQueryFormRef" :inline="true"
        label-width="68px">
        <el-form-item label="员工姓名" prop="nickName">
          <el-input v-model="mapRouteQueryParams.nickName" placeholder="请输入员工姓名" clearable @keyup.enter="handleQuery"
            class="!w-240px" />
        </el-form-item>
        <el-form-item label="员工账号" prop="userName">
          <el-input v-model="mapRouteQueryParams.userName" placeholder="请输入员工账号" clearable @keyup.enter="handleQuery"
            class="!w-240px" />
        </el-form-item>
        <el-form-item label="时间" prop="dateRange">
          <el-date-picker v-model="mapRouteCreateTimeRange" type="daterange" range-separator="至" start-placeholder="开始日期"
            end-placeholder="结束日期" value-format="YYYY-MM-DD" class="!w-240px" @change="hanlderMapRouteTimeChange" />
        </el-form-item>
       
        <el-form-item>
          <el-button @click="handleMapRouteQuery" type="primary">
            <Icon icon="ep:search" class="mr-5px" />
            查询
          </el-button>
          <el-button @click="handleMapRouteResetQuery">
            <Icon icon="ep:refresh" class="mr-5px" />
            重置
          </el-button>
        </el-form-item>
      </el-form>
      <el-table v-loading="mapRouteLoading" :data="mapRouteList" style="margin-top: 20px;">
        <el-table-column label="序号" align="center" width="100" type="index"  />
        <el-table-column label="日期" align="center" width="120" prop="dateTime" />
        <el-table-column label="员工姓名" align="center" width="120" prop="nickName" />
        <el-table-column label="员工账号" align="center" prop="userName" />
        <el-table-column label="部门" align="center" width="280" prop="deptName" />
        <el-table-column label="当前位置" align="center" prop="address" />
      
        <el-table-column label="操作">
          <template #default="scope">
            <el-button link type="primary" @click="showMapRouteLine(scope.row)">
              地图路线</el-button>
          </template>
        </el-table-column>
      </el-table>
      <!-- 分页 -->
      <Pagination :total="mapRouteTotal" v-model:page="mapRouteQueryParams.pageNo"
        v-model:limit="mapRouteQueryParams.pageSize" @pagination="getMapRouteQueryList" />



    </div>
    <template #footer>
      <el-button type="primary" @click="mapRouteVisible = false">关 闭</el-button>
    </template>
  </el-dialog>


<mapes ref="map"  />
<detailInfo ref="detailDialog" />
<reportInfo ref="reportDialog" />


</template>
<script lang="ts" setup>
import mapes from './map.vue'
import detailInfo from './detail.vue'
import reportInfo from './report.vue'

import {
  getUserTrackSettingPage,
  userTrackSettingSend,
  restartTrackSettingSend,
  auditTrackSettingSend,
  getUserTrackMonthPage,
  getUserTrackMapList,
} from '@/api/location/index'

import * as UserApi from '@/api/system/user'
import { useRouter, useRoute } from 'vue-router'
import { ref, onMounted, watch } from 'vue'
import { useUserStore } from '@/store/modules/user'

const userStore = useUserStore()
const router = useRouter()
const route = useRoute()
const message = useMessage() // 消息弹窗


const loading = ref(true) // 列表的加载中
const total = ref(0) // 列表的总页数
const list = ref([]) // 列表的数据
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  nickName: undefined,
  userName: undefined,
  startDate: undefined,
  endDate: undefined,
  status: undefined,
})
const queryFormRef = ref() // 搜索的表单

// 合同状态选项
const statusOptions = [
  {
    label: '实时定位中',
    value: 1,
    type: 'success'
  },
  {
    label: '发起定位申请',
    value: 2,
    type: 'primary'
  },
  {
    label: '拒绝定位申请',
    value: 3,
    type: 'danger'
  },
  {
    label: '定位申请取消',
    value: 4,
    type: 'warning'
  },
  {
    label: '定位已取消',
    value: 5,
    type: 'info'
  }
]

const createTimeRange = ref([])


const getList = async () => {
  loading.value = true
  try {
    const res = await getUserTrackSettingPage(queryParams)
    console.log('getUserTrackSettingPage=', res)
    list.value = res.data.list
    total.value = res.data.total
  } finally {
    loading.value = false
  }
}

//修改时间
const hanlderTimeChange = (val) => {
  if (!val || val.length == 0) {
    queryParams.startDate = undefined;
    queryParams.endDate = undefined;
  } else {
    queryParams.startDate = val[0];
    queryParams.endDate = val[1];
  }
  getList();
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {

  queryFormRef.value.resetFields()
  createTimeRange.value = [];
  queryParams.startDate = undefined;
  queryParams.endDate = undefined;
  handleQuery()
}

/** 初始化 **/
onMounted(() => {
  getList()
})


// 发起定位弹框
const launchVisible = ref(false)
const userIds = ref<Number[]>([]) // 选中的用户id
const userList = ref<UserApi.UserVO[]>([]) // 用户列表

const launch = async () => {
  // 获得用户列表
  userList.value = await UserApi.getSimpleUserList()
  userIds.value = []

  launchVisible.value = true
}

const submitLaunch = async () => {

  if (userIds.value.length == 0) {
    message.error('请选择员工')

    return
  }

  const res = await userTrackSettingSend({
    userIds: userIds.value,
  })


  if (res.code == 0) {
    message.success('发起定位成功')

  }

  getList()
  launchVisible.value = false

}

// 重新审核  
const reRequest = async (val: any) => {
  ElMessageBox.confirm(
    '是否重新发起定位申请请求？',
    '重新请求',
    {
      confirmButtonText: '确认',
      cancelButtonText: '取消',
      type: 'warning',
    }
  )
    .then(async () => {
      const res = await restartTrackSettingSend({
        ids: val.id,
      })
      if (res.code == 0) {
        message.success('重新发起定位成功')
      }
      getList()
    })
}


// 审核  auditVisible
const auditVisible = ref(false)
const auditUserIds = ref<Number[]>([]) // 选中的用户id
const auditResult = ref<any>(null) // 审核结果



const auditShow = async (val) => {
  // 获得用户列表

  auditUserIds.value = [val.id]

  auditResult.value = null
  auditVisible.value = true
}

const submitAudit = async () => {
  if (!auditResult.value) {
    message.error('请选择审核结果')
    return
  }
  const res = await auditTrackSettingSend({
    ids: auditUserIds.value.join(','),
    status: auditResult.value
  })
  if (res.code == 0) {
    message.success('审核操作成功')
  }
  getList()
  auditVisible.value = false

}


// 地图线路
const mapRouteVisible = ref(false)
const mapRouteLoading = ref(true) // 列表的加载中
const mapRouteCreateTimeRange = ref([]) // 时间范围
const mapRouteTotal = ref(0) // 列表的总页数
const mapRouteList = ref([]) // 列表的数据
const mapRouteQueryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  nickName: '',
  userName: undefined,
  startDate: undefined,
  endDate: undefined,
  sysUserId: undefined,
})
const mapRouteQueryFormRef = ref() // 搜索的表单
const mapRouteSysUserId = ref<any>(undefined) // 系统用户id
const showMapRoute = async (val) => {

  console.log('showMapRoute', val.sysUserId)
  mapRouteSysUserId.value = val.sysUserId
  mapRouteVisible.value = true
  setTimeout(() => {
    handleMapRouteResetQuery()
  }, 200);


}

//修改时间
const hanlderMapRouteTimeChange = (val) => {
  if (!val || val.length == 0) {
    mapRouteQueryParams.startDate = undefined;
    mapRouteQueryParams.endDate = undefined;
  } else {
    mapRouteQueryParams.startDate = val[0];
    mapRouteQueryParams.endDate = val[1];
  }
  getMapRouteQueryList();
}


/** 搜索按钮操作 */
const handleMapRouteQuery = () => {
  mapRouteQueryParams.pageNo = 1
  getMapRouteQueryList()
}

/** 重置按钮操作 */
const handleMapRouteResetQuery = () => {

  mapRouteQueryFormRef.value.resetFields()
  mapRouteQueryParams.sysUserId = mapRouteSysUserId.value
  mapRouteCreateTimeRange.value = [];
  mapRouteQueryParams.startDate = undefined;
  mapRouteQueryParams.endDate = undefined;
  handleMapRouteQuery()
}

/** 查询列表 */
const getMapRouteQueryList = async () => {
  mapRouteLoading.value = true
  try {
    const res = await getUserTrackMonthPage(mapRouteQueryParams)
    console.log('getUserTrackMonthPage=', res)
    mapRouteList.value = res.data.list
    mapRouteTotal.value = res.data.total
  } finally {
    mapRouteLoading.value = false
  }
}

const map = ref()
// 地图线路弹框
const showMapRouteLine = async (val) => {

   const res = await getUserTrackMapList({sysUserId: val.sysUserId, dateTime: val.dateTime})
   if(res.data.length == 0){
     message.error('暂无数据')
     return
   }


   map.value.open(val,res.data)



}  

const detailDialog = ref()
const detailShow = (val) => {
  console.log('detailShow', val)
  detailDialog.value.open(val.sysUserId)
}


const reportDialog = ref()
const reportShow = (val) => {
  console.log('reportShow', val)
  reportDialog.value.open(val.sysUserId)
}

</script>
