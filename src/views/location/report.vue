<template>
    <Dialog v-model="dialogVisible" :title="titleName" width="80%" top="5vh">
        <div class="">

            <el-form class="-mb-15px" :model="queryParams" ref="queryFormRef" :inline="true" label-width="68px">
                <el-form-item label="上报人" prop="nickName">
                    <el-input v-model="queryParams.nickName" placeholder="请输入上报人" clearable @keyup.enter="handleQuery"
                        class="!w-240px" />
                </el-form-item>
                <el-form-item label="标题" prop="title">
                    <el-input v-model="queryParams.title" placeholder="请输入标题" clearable @keyup.enter="handleQuery"
                        class="!w-240px" />
                </el-form-item>
                <el-form-item label="时间" prop="dateRange">
                    <el-date-picker v-model="createTimeRange" type="daterange" range-separator="至"
                        start-placeholder="开始日期" end-placeholder="结束日期" value-format="YYYY-MM-DD" class="!w-240px"
                        @change="hanlderTimeChange" />
                </el-form-item>


                <el-form-item>
                    <el-button @click="handleQuery" type="primary">
                        <Icon icon="ep:search" class="mr-5px" />
                        查询
                    </el-button>
                    <el-button @click="resetQuery">
                        <Icon icon="ep:refresh" class="mr-5px" />
                        重置
                    </el-button>
                </el-form-item>

            </el-form>


            <el-table v-loading="loading" :data="list" style="margin-top: 20px;">
                <el-table-column label="序号" align="center" width="100" prop="id" />
                <el-table-column label="标题" align="center" prop="title" />
                <el-table-column label="定位上报" align="center" prop="location" />
                <el-table-column label="上报人" align="center" prop="userName" />
                <el-table-column label="上报时间" align="center" prop="reportDate" />
                <el-table-column label="操作">
                    <template #default="scope">


                        <el-button link type="primary" @click="showDetail(scope.row)">
                            详情</el-button>


                    </template>
                </el-table-column>


            </el-table>

            <!-- 分页 -->
            <Pagination class="temp" :total="total" v-model:page="queryParams.pageNo"
                v-model:limit="queryParams.pageSize" @pagination="getList" />


        </div>

        <template #footer>
            <el-button type="primary" @click="dialogVisible = false">关闭</el-button>
        </template>
    </Dialog>

    <el-dialog v-model="detailVisible" title="详情" width="600">
        <div style="padding: 20px 20px 60px 20px; ">
            <div class="" style="margin-bottom: 10px;display: flex;">
                <div style="width: 120px;flex-shrink: 0;">
                    标题：
                </div>
                <div style="width: 300px;">
                    {{detailData.title}}
                </div>
            </div>
            <div class="" style="margin-bottom: 10px;display: flex;">
                <div style="width: 120px;flex-shrink: 0;">
                    描述：
                </div style="width: 300px;">
                {{detailData.desc}}
            </div>
            <div class="" style="margin-bottom: 10px;display: flex;">
                <div style="width: 120px;flex-shrink: 0;">
                    上传图片：
                </div>
                <div style="display: flex;flex-wrap: wrap;width: 300px;">
                    <el-image v-for="(item, index) in detailData.files" :key="index" :src="item.url" fit="cover"
                    :preview-src-list="[item.url]"
                        style="width: 100px;height: 100px;margin-right: 10px;margin-bottom: 10px;" />
                </div>
            </div>
            <div class="" style="margin-bottom: 10px;display: flex;">
                <div style="width: 120px;flex-shrink: 0;">
                    定位上传：
                </div>
                <div style="width: 300px;">
                    {{detailData.location}}
                </div>
            </div>
            <div class="" style="margin-bottom: 10px;display: flex;">
                <div style="width: 120px;flex-shrink: 0;">
                    备注：
                </div>
                <div style="width: 300px;">
                    {{detailData.remark}}
                </div>


            </div>
        </div>

        <template #footer>
            <el-button @click="detailVisible = false">关 闭</el-button>
        </template>
    </el-dialog>

    <!-- <ContentWrap class="h-1/1"> </ContentWrap> -->
</template>

<script lang="ts" setup>
    import AMapLoader from '@amap/amap-jsapi-loader'

    import {
        getLocationReportPage,
        getLocationReportDetail,

    } from '@/api/location/index'

    const titleName = ref('定位上报')


    const message = useMessage() // 消息弹窗

    const dialogVisible = ref(false) // 弹窗的是否展示

    const createTimeRange = ref([])
    const loading = ref(true) // 列表的加载中
    const total = ref(0) // 列表的总页数
    const list = ref([]) // 列表的数据
    const queryParams = reactive({
        pageNo: 1,
        pageSize: 10,
        nickName: undefined,
        userName: undefined,
        reportDateStart: undefined,
        reportDateEnd: undefined,

        userId: undefined,
    })
    const queryFormRef = ref() // 搜索的表单

    const userId = ref(null) // 系统用户id


    const getList = async () => {
        loading.value = true
        try {
            const res = await getLocationReportPage(queryParams)
            console.log('getLocationReportPage=', res)
            list.value = res.data.list
            total.value = res.data.total
        } finally {
            loading.value = false
        }
    }

    //修改时间
    const hanlderTimeChange = (val) => {
        if (!val || val.length == 0) {
            queryParams.reportDateStart = undefined;
            queryParams.reportDateEnd = undefined;
        } else {
            queryParams.reportDateStart = val[0];
            queryParams.reportDateEnd = val[1];
        }
        getList();
    }

    /** 搜索按钮操作 */
    const handleQuery = () => {
        queryParams.pageNo = 1
        getList()
    }

    /** 重置按钮操作 */
    const resetQuery = () => {

        queryFormRef.value.resetFields()
        createTimeRange.value = [];
        queryParams.reportDateStart = undefined;
        queryParams.reportDateEnd = undefined;
        queryParams.userId = userId.value;
        handleQuery()
    }

    const open = async (id) => {
        userId.value = id
        queryParams.userId = userId.value;

        getList()
        dialogVisible.value = true



    }

    defineExpose({ open }) // 提供 open 方法，用于打开弹窗
    /** 初始化 */
    onMounted(() => { })

    const detailVisible = ref(false) // 弹窗的是否展示
    const detailData = ref({}) // 详情数据
    const tempRow = ref({}) // 临时变量，用于存储当前行数据

    const showDetail = async (row) => {
        tempRow.value = row
        const res = await getLocationReportDetail(row.id)
        console.log('getLocationReportDetail=', res)
        detailData.value = res.data
        detailVisible.value = true
    }








</script>

<style lang="scss" scoped>


</style>