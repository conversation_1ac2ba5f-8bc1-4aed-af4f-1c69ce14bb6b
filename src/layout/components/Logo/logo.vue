<template>
  <div>
    <img alt="" class="mr-115px h-18px w-63px" src="@/assets/imgs/logo.png"  v-if="!isC"/>
    <img alt="" class="mr-20px h-16px w20px" src="@/assets/imgs/logoLeft.png"  v-if="isC"/>
  </div>
</template>

<script lang="ts" setup>
import { useAppStore } from '@/store/modules/app'
const appStore = useAppStore()
const collapse = computed(() => appStore.getCollapse)
const isC = ref(false)

watch(() => collapse.value, (newPath, oldPath) => {
  isC.value = newPath
})
</script>

<style></style>