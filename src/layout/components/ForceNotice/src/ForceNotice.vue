<template>
  <div class="force-notice" v-if="isShow">
    <div class="f-content">
      <div class="f-title">
        <img src="./icon.png" />
      </div>
      <div class="notice-title">{{ mandatoryRead.title }}</div>
      <div class="notice-time">{{ mandatoryRead.createTime }}</div>
      <div class="notice-btn" @click="goNoticeDetail" v-if="!isMandatorySign">立即查看</div>
      <div class="notice-btn" @click="checkContractOk('button')" v-else>我已签署</div>
      <div class="notice-tip">{{ tips }}</div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { defineOptions, ref, watch, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { getForceReadNotice } from '@/api/notify'

const router = useRouter()
defineOptions({ name: 'ForceNotice' })

const isShow = ref(false)
const tips = ref('请先阅读再进行其他操作')
const isMandatorySign = ref(false)
const mandatoryRead = ref({
  id: null,
  title: null,
  createTime: null,
  previewUrl: null
})

let intervalId: number | null = null // 用于存储定时器的 ID

// 跳转公告详情
const goNoticeDetail = () => {
  isShow.value = false
  router.push('/notify/list/person?id=' + mandatoryRead.value.id)
}

// 检查合同是否签署
const checkContractOk = (type: string) => {
  console.log('getForceReadNotice2')
  getForceReadNotice()
    .then((res: any) => {
      if (res && res.data && res.data.mandatoryReadVO) {
        const { data } = res
        if (data.mandatoryReadVO.type==1) {
          if (type === 'button') {
            ElMessage.error('请先签署合同再进行其他操作')
            return
          }
        }
        

      }else{
        isShow.value = false
      }
    })
    .catch((error: any) => {
      console.error('获取合同签署状态失败:', error)
      isShow.value = false
    })
}

// 初始化数据
const init = () => {
  console.log('getForceReadNotice1')
  getForceReadNotice()
    .then((res: any) => {
      if (res && res.data && res.data.mandatoryReadVO) {
        const { data } = res
        // 强制签署合同
        if (data.mandatoryReadVO.type==2) {
          // 强制阅读公告
          mandatoryRead.value = {
            ...data.mandatoryReadVO
          }
        } else if (data.mandatoryReadVO.type==1) {
          mandatoryRead.value.title = '请至办办手机端签署合同'
          tips.value = '请先签署合同再进行其他操作'
          isMandatorySign.value = true
          // mandatoryRead.value = {
          //   id: null,
          //   title: null,
          //   createTime: null,
          //   previewUrl: null
          // }
        }
        // 显示 isShow
        isShow.value = true
      } else {
        // 如果 res.data 不存在，隐藏 isShow
        isShow.value = false
      }
    })
    .catch((error: any) => {
      console.error('获取强制阅读公告失败:', error)
      isShow.value = false
    })
}

// 监听 isShow 的变化
watch(isShow, (newVal) => {
  if (newVal) {
    // 如果 isShow 为 true，启动定时器
    intervalId = setInterval(() => {
      checkContractOk('interval')
    }, 5000)
  } else {
    // 如果 isShow 为 false，清除定时器
    if (intervalId) {
      clearInterval(intervalId)
      intervalId = null
    }
  }
})

// 组件卸载时清除定时器
onUnmounted(() => {
  if (intervalId) {
    clearInterval(intervalId)
  }
})

// 初始化数据
init()
console.log('ForceNotice组件加载完成')
// 路由加载前
router.beforeEach(async (to, from, next) => {
  if (to.path !== '/notify/list/person') {
    init()
  }
  next()
})
</script>
<style lang="scss" scoped>
.force-notice {
  background-color: rgba(0, 0, 0, 0.4);
  width: 100%;
  height: 100vh;
  position: fixed;
  left: 0;
  top: 0;
  z-index: 1001;
  display: flex;
  justify-content: center;
  align-items: center;

  .f-content {
    width: 484px;
    height: 293px;
    display: flex;
    flex-direction: column;
    align-items: center;
    background: #fff;
    border-radius: 11px;

    .f-title {
      width: 100%;
      height: 76px;
      background-image: url('./title-bg.png');
      background-size: 100% 100%;
      background-repeat: no-repeat;
      position: relative;

      & img {
        width: 167px;
        height: 121px;
        position: absolute;
        left: 0;
        right: 0;
        margin: 0 auto;
        top: -46px;
      }
    }

    .notice-title {
      font-weight: bold;
      color: #333;
      max-width: 450px;
      font-size: 18px;
      margin-top: 20px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    .notice-time {
      color: #606666;
      font-size: 14px;
      margin: 20px;
    }

    .notice-btn {
      width: 174px;
      height: 30px;
      display: flex;
      justify-content: center;
      align-items: center;
      color: #fff;
      background: #3370ff;
      border-radius: 5px;
      margin-top: 30px;
      font-size: 14px;
      cursor: pointer;
    }

    .notice-tip {
      color: #a2a3a5;
      font-size: 12px;
      margin-top: 15px;
    }
  }
}
</style>
