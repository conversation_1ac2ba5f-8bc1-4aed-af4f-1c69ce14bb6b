<template>
  <div>
    <div v-if="dataList.length > 0" class="tenant-box w-[var(--left-menu-max-width)]"
      :class="{ ' w-[var(--left-menu-min-width)] isCBox': collapse, 'isCBox-else': windowWidth > 768 }">
      <div class="tenant-div" @click="handleOpen">
        <img class="img" src="@/assets/imgs/orz.png" />
        <span>{{ currentList[0].name }}</span>
        <icon name="el-icon-caretbottom" />
      </div>
      <div class="checkBox" v-if="isOpen" ref="refBox">
        <span class="title">请选择组织/团队</span>
        <div v-for="(item, index) in dataList" :key="index" :class="{ isCheck: item.id == currentList[0].id }"
          class="checkBox-content" @click="checkBind(item.id)">
          <img class="img2" src="@/assets/imgs/orz.png" />
          <div>
            <span class="span1">{{ item.name }}</span>
            <span class="span2" v-if="item.id == currentList[0].id">对外展示身份（主企业）</span>
            <icon v-if="item.id == currentList[0].id" name="el-icon-check" />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import { getTenant, bindTenant } from '@/api/sys'
import { getTenantId } from '@/utils/auth'
import * as LoginApi from '@/api/login'
import * as authUtil from '@/utils/auth'
import { resetRouter } from '@/router'
import { deleteUserCache, useCache } from '@/hooks/web/useCache'
import { useAppStore } from '@/store/modules/app'
const { wsCache } = useCache()
const appStore = useAppStore()
export default {
  data() {
    return {
      dataList: [],
      currentList: [],
      isOpen: false,
      loading: false,
      windowWidth: window.innerWidth
    }
  },
  computed: {
    collapse() {
      return appStore.getCollapse
    },
  },
  watch: {
    isOpen: function (oldV, newV) {
      document.addEventListener('click', this.hideBox)
    },
    collapse: function (oldV, newV) {
      this.collapse = newV
    },
  },
  created() {
    getTenant().then((res) => {
      this.dataList = res.data
      localStorage.setItem('tenantList', JSON.stringify(res.data))
      res.data.forEach((item) => {
        if (getTenantId() == item.id) {
          this.currentList.push(item)
          localStorage.setItem('currentList', JSON.stringify(this.currentList))
        }
      })
    })
  },
  mounted() {
    document.addEventListener('click', this.hideBox)
    window.addEventListener('resize', this.handleResize)
  },
  unmounted() {
    document.removeEventListener('click', this.hideBox)
    window.removeEventListener('resize', this.handleResize)
  },
  methods: {
    handleResize() {
      console.log(window.innerWidth)
      this.windowWidth = window.innerWidth
    },
    handleOpen() {
      setTimeout(() => {
        this.isOpen = !this.isOpen
      })
    },
    hideBox(e) {
      const targetDiv = this.$refs.refBox
      if (targetDiv && targetDiv.contains && !targetDiv.contains(e.target)) {
        this.isOpen = false
      }
    },
    checkBind(id) {
      bindTenant({ tenantId: id }).then((r) => {
        if (r.code == 0) {
          this.$router.push('/index')
          setTimeout(() => {
            resetRouter() // 重置静态路由表
            deleteUserCache() // 删除用户缓存
            authUtil.removeToken()
            this.getLogin()
          }, 100)
        }
      })
    },
    async getLogin() {
      try {
        const loginForm = authUtil.getLoginForm()
        const res = await LoginApi.login(loginForm)
        if (!res) {
          return
        }
        this.loading = ElLoading.service({
          lock: true,
          text: '正在加载系统中...',
          background: 'rgba(0, 0, 0, 0.7)'
        })
        authUtil.setLoginForm(loginForm)
        authUtil.setToken(res)
        authUtil.setTenantId(res.tenantId)
        // wsCache.delete(CACHE_KEY.USER)
        // wsCache.delete(CACHE_KEY.ROLE_ROUTERS)
        location.reload()
      } finally {
        this.loading = false
      }
    }
  }
}
</script>

<style lang="less" scope>
.tenant-box {
  height: 60px;
  background: #fff;
  // width: 200px;

  .tenant-div {
    display: flex;
    align-items: center;
    font-weight: bold;
    cursor: pointer;
    height: 100%;
    box-sizing: border-box;
    padding: 0 36px 0 20px;
    position: relative;

    span {
      text-overflow: ellipsis;
      white-space: nowrap;
      overflow: hidden;
    }
  }

  .img {
    width: 36px;
    height: 36px;
    margin-right: 5px;
  }

  .el-icon-caretbottom {
    position: absolute;
    right: 20px;
    color: #303133;
  }
}

.tenant-div:hover {
  background: rgba(51, 112, 255, 0.1);

  .img {
    border: 1px solid #e4e7ed;
    border-radius: 4px;
    box-sizing: border-box;
  }
}

.checkBox {
  position: absolute;
  left: 20px;
  top: 50px;
  width: 350px;
  background: #fff;
  z-index: 10000;
  box-shadow: 0px 0px 6px 2px rgba(0, 0, 0, 0.2);
  border-radius: 2px;
  padding: 0 10px 10px;

  .title {
    font-size: 18px;
    padding: 15px 10px;
    display: block;
  }

  .checkBox-content {
    display: flex;
    justify-content: left;
    // align-items: center;
    margin-bottom: 5px;
    padding: 10px;
    position: relative;
    cursor: pointer;

    span {
      display: block;
    }

    .span1 {
      font-size: 18px;
      margin-bottom: 2px;
      padding-right: 30px;
    }

    .span2 {
      font-size: 12px;
      color: #909399;
    }
  }

  .img2 {
    margin-right: 10px;
    width: 36px;
    height: 36px;
  }

  .isCheck {
    background: rgba(51, 112, 255, 0.1);
    border-radius: 10px;
  }

  .checkBox-content:hover {
    background: rgba(51, 112, 255, 0.1);
    border-radius: 10px;
  }

  .el-icon-check {
    position: absolute;
    right: 20px;
    top: 20px;
    color: #1989fa;
  }
}

.isCBox {
  // width: 64px;
  margin-left: -1000px;

  .tenant-div {
    padding: 0;
    justify-content: center;
  }

  .el-icon-caretbottom,
  span {
    display: none;
  }

  .img {
    margin-right: 0;
  }
}

.isCBox-else {
  margin-left: 0;
}
</style>