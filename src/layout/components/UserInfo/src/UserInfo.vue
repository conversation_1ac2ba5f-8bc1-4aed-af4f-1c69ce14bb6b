<script lang="ts" setup>
import { ElMessageBox } from 'element-plus'

import avatarImg from '@/assets/imgs/avatar.png'
import { useDesign } from '@/hooks/web/useDesign'
import { useTagsViewStore } from '@/store/modules/tagsView'
import { useUserStore } from '@/store/modules/user'
import LockDialog from './components/LockDialog.vue'
import LockPage from './components/LockPage.vue'
import { useLockStore } from '@/store/modules/lock'
import { Icon } from '@/components/Icon'
import { CACHE_KEY, useCache } from '@/hooks/web/useCache'

import { useAppStore } from '@/store/modules/app'

import { getContractStatus } from '@/api/system/user/profile'

const appStore = useAppStore()

defineOptions({ name: 'UserInfo' })

const { t } = useI18n()
const message = useMessage() // 消息弹窗
const { wsCache } = useCache()

const { push, replace } = useRouter()

const userStore = useUserStore()

const tagsViewStore = useTagsViewStore()

const { getPrefixCls } = useDesign()

const prefixCls = getPrefixCls('user-info')

const avatar = computed(() => (userStore.user.avatar ? userStore.user.avatar : avatarImg))
const userName = computed(() => (userStore.user.nickname ? userStore.user.nickname : 'Admin'))

// 从 sessionStorage 中获取是否显示合同列表的开关
const showContractListSwitch = computed(() => userStore.getIsOpenContract)
// 在页面加载时执行的逻辑
onMounted(() => {
  getContractStatusValue()
  try {
    // 从 sessionStorage 中获取 dictCache
    const dictCache = sessionStorage.getItem('dictCache')
    if (dictCache) {
      // 解析 dictCache
      const { v } = JSON.parse(dictCache)

      var contractInfo =null
      if (v) {
           contractInfo = JSON.parse(v).contract_info[0]
      }
    } else {
      console.error('dictCache is not found in sessionStorage')
    }
  } catch (error) {
    console.error('Error parsing dictCache from sessionStorage', error)
  }
})
// 锁定屏幕
const lockStore = useLockStore()
const getIsLock = computed(() => lockStore.getLockInfo?.isLock ?? false)
const dialogVisible = ref<boolean>(false)
const lockScreen = () => {
  dialogVisible.value = true
}

// 右上角个人合同开关状态查看
const getContractStatusValue = async () => {
  const res = await getContractStatus()
  //isOpenContract=2：关闭，isOpenContract=1：开启
  if (res?.isOpenContract) {
    userStore.setIsOpenContract(res.isOpenContract)
  }
}
/** 刷新菜单缓存按钮操作 */
const refreshMenu = async () => {
  try {
    await message.confirm('即将更新缓存刷新浏览器！', '刷新菜单缓存')
    // 清空，从而触发刷新
    wsCache.delete(CACHE_KEY.USER)
    wsCache.delete(CACHE_KEY.ROLE_ROUTERS)
    // 刷新浏览器
    location.reload()
  } catch {}
}

const loginOut = async () => {
  try {
    await ElMessageBox.confirm(t('common.loginOutMessage'), t('common.reminder'), {
      confirmButtonText: t('common.ok'),
      cancelButtonText: t('common.cancel'),
      type: 'warning'
    })
    appStore.setAllUnReadCount(0)
    await userStore.loginOut()
    tagsViewStore.delAllViews()
    replace('/login?redirect=/index')
  } catch {}
}
const toProfile = async () => {
  push('/user/profile')
}
const toUserRoster = async () => {
  push('/user/personal-roster')
}
const toUserContract = () => {
  push({ path: '/user/personal-contract', query: { userId: userStore.user.id } })
}
const toDocument = () => {
  window.open('https://doc.iocoder.cn/')
}
</script>

<template>
  <ElDropdown class="user-avatar-div" :class="prefixCls" trigger="click">
    <div class="flex items-center">
      <ElAvatar :src="avatar" alt="" class="my-avatar-wflow" />
      <span class="pl-[5px] text-14px text-[var(--top-header-text-color)] <lg:hidden">
        {{ userName }}
      </span>
    </div>
    <template #dropdown>
      <ElDropdownMenu>
        <ElDropdownItem>
          <Icon icon="ep:tools" />
          <div @click="toProfile">{{ t('common.profile') }}</div>
        </ElDropdownItem>
        <ElDropdownItem divided>
          <Icon icon="fa:address-book-o" />
          <div @click="toUserRoster">个人档案</div>
        </ElDropdownItem>
        <ElDropdownItem v-if="showContractListSwitch === 1" divided>
          <Icon icon="fa:file-text-o" />
          <div @click="toUserContract">个人合同</div>
        </ElDropdownItem>
        <!-- <ElDropdownItem>
          <Icon icon="ep:menu" />
          <div @click="toDocument">{{ t('common.document') }}</div>
        </ElDropdownItem> -->
        <ElDropdownItem divided>
          <Icon icon="ep:refresh" />
          <div @click="refreshMenu">刷新缓存</div>
        </ElDropdownItem>
        <!-- <ElDropdownItem divided>
          <Icon icon="ep:lock" />
          <div @click="lockScreen">{{ t('lock.lockScreen') }}</div>
        </ElDropdownItem> -->
        <ElDropdownItem divided @click="loginOut">
          <Icon icon="ep:switch-button" />
          <div>{{ t('common.loginOut') }}</div>
        </ElDropdownItem>
      </ElDropdownMenu>
    </template>
  </ElDropdown>

  <LockDialog v-if="dialogVisible" v-model="dialogVisible" />

  <teleport to="body">
    <transition name="fade-bottom" mode="out-in">
      <LockPage v-if="getIsLock" />
    </transition>
  </teleport>
</template>

<style scoped lang="scss">
.fade-bottom-enter-active,
.fade-bottom-leave-active {
  transition:
    opacity 0.25s,
    transform 0.3s;
}

.fade-bottom-enter-from {
  opacity: 0;
  transform: translateY(-10%);
}

.fade-bottom-leave-to {
  opacity: 0;
  transform: translateY(10%);
}

.user-avatar-div {
  margin: 0 20px;
  cursor: pointer;
  color: #fff;

  .my-avatar-wflow {
    height: 25px;
    width: 25px;
    margin: 8px 0;
  }
}
</style>
