import type { App } from 'vue'
import { hasRole } from './permission/hasRole'
import { hasPermi } from './permission/hasPermi'

import { clickOutside } from './clickOutside/clickOutside'


/**
 * 导出指令：v-xxx
 * @methods hasRole 用户权限，用法: v-hasRole
 * @methods hasPermi 按钮权限，用法: v-hasPermi
 * @methods clickOutside 是否点击了外面，用法: v-click-out-side
 */
export const setupAuth = (app: App<Element>) => {
  hasRole(app)
  hasPermi(app)
  clickOutside(app)
}
