<script lang="ts" setup>
import { isDark } from '@/utils/is'
import { useAppStore } from '@/store/modules/app'
import { useDesign } from '@/hooks/web/useDesign'
import { CACHE_KEY, useCache } from '@/hooks/web/useCache'
import { useMyStore } from '@/store/modules/jump'
import formModule from '@/views/wflow/workspace/oa/formModule.vue'
import watchWs from '@/views/wflow/bb_oa/watchWs.vue'
const stores = useMyStore()
defineOptions({ name: 'APP' })

const { getPrefixCls } = useDesign()
const prefixCls = getPrefixCls('app')
const appStore = useAppStore()
const currentSize = computed(() => appStore.getCurrentSize)
const greyMode = computed(() => appStore.getGreyMode)
const { wsCache } = useCache()

// 根据浏览器当前主题设置系统主题色
const setDefaultTheme = () => {
  let isDarkTheme = wsCache.get(CACHE_KEY.IS_DARK)
  if (isDarkTheme === null) {
    isDarkTheme = isDark()
  }
  appStore.setIsDark(isDarkTheme)
}
setDefaultTheme()

const formModuleRef = ref()
watch(
  () => stores.openForm,
  (newValue, oldValue) => {
    if (newValue) {
      useMyStore().handleOpenForm(false)
      useMyStore().handleFormClose(false)
      formModuleRef.value.open(stores.openFormObj)
    }
  }
)
</script>
<template>
  <ConfigGlobal :size="currentSize">
    <RouterView :class="greyMode ? `${prefixCls}-grey-mode` : ''" />
    <!-- 发起流程 -->
    <formModule ref="formModuleRef" class="overallLaunch"></formModule>
    <!-- 监听ws审批单数量 -->
    <!--     <watchWs></watchWs>-->
  </ConfigGlobal>
</template>
<style lang="scss">
$prefix-cls: #{$namespace}-app;

.size {
  width: 100%;
  height: 100%;
}

html,
body {
  @extend .size;

  padding: 0 !important;
  margin: 0;
  overflow: hidden;

  #app {
    @extend .size;
  }
}

.#{$prefix-cls}-grey-mode {
  filter: grayscale(100%);
}

.overallLaunch {
  position: absolute !important;
  top: 80px !important;
  left: 200px;
  z-index: 100;
  right: 0;
  bottom: 0;
  background: #f3f4f7;
  padding: 0 15px;
  overflow: auto;
}
</style>
